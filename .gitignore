# AIGC Service Hub - Git Ignore Configuration

# ================================
# Dependencies
# ================================
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# ================================
# Build outputs
# ================================
dist/
build/
out/
.next/
.nuxt/
.vuepress/dist/
.serverless/
.fusebox/
.dynamodb/

# ================================
# Environment variables
# ================================
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
.env.*.local

# ================================
# Logs
# ================================
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*
.pnpm-debug.log*

# ================================
# Runtime data
# ================================
pids
*.pid
*.seed
*.pid.lock

# ================================
# Coverage directory used by tools like istanbul
# ================================
coverage/
*.lcov
.nyc_output

# ================================
# Dependency directories
# ================================
jspm_packages/

# ================================
# TypeScript cache
# ================================
*.tsbuildinfo

# ================================
# Optional npm cache directory
# ================================
.npm

# ================================
# Optional eslint cache
# ================================
.eslintcache

# ================================
# Microbundle cache
# ================================
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# ================================
# Optional REPL history
# ================================
.node_repl_history

# ================================
# Output of 'npm pack'
# ================================
*.tgz

# ================================
# Yarn Integrity file
# ================================
.yarn-integrity

# ================================
# parcel-bundler cache
# ================================
.cache
.parcel-cache

# ================================
# Next.js build output
# ================================
.next
out

# ================================
# Nuxt.js build / generate output
# ================================
.nuxt
dist

# ================================
# Gatsby files
# ================================
.cache/
public

# ================================
# Vuepress build output
# ================================
.vuepress/dist

# ================================
# Serverless directories
# ================================
.serverless/

# ================================
# FuseBox cache
# ================================
.fusebox/

# ================================
# DynamoDB Local files
# ================================
.dynamodb/

# ================================
# TernJS port file
# ================================
.tern-port

# ================================
# Stores VSCode versions used for testing VSCode extensions
# ================================
.vscode-test

# ================================
# yarn v2
# ================================
.yarn/cache
.yarn/unplugged
.yarn/build-state.yml
.yarn/install-state.gz
.pnp.*

# ================================
# Database
# ================================
*.db
*.sqlite
*.sqlite3

# ================================
# OS generated files
# ================================
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# ================================
# IDE files
# ================================
.vscode/
.idea/
*.swp
*.swo
*~

# ================================
# Temporary files
# ================================
tmp/
temp/
*.tmp
*.temp

# ================================
# Docker
# ================================
.dockerignore

# ================================
# Terraform
# ================================
*.tfstate
*.tfstate.*
.terraform/
.terraform.lock.hcl
terraform.tfvars
*.tfplan

# ================================
# AWS
# ================================
.aws/

# ================================
# Python
# ================================
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
venv/
ENV/
env.bak/
venv.bak/

# ================================
# Java
# ================================
*.class
*.jar
*.war
*.ear
*.zip
*.tar.gz
*.rar

# ================================
# Generated files
# ================================
*.generated.*
*.auto.*

# ================================
# Backup files
# ================================
*.bak
*.backup
*.old

# ================================
# Lock files (keep package-lock.json for consistency)
# ================================
# yarn.lock
# pnpm-lock.yaml

# ================================
# Local configuration
# ================================
.local
local.json
local.config.js

# ================================
# Test files
# ================================
test-results/
playwright-report/
test-results.xml

# ================================
# Storybook
# ================================
storybook-static/

# ================================
# Sentry
# ================================
.sentryclirc

# ================================
# Vercel
# ================================
.vercel

# ================================
# Netlify
# ================================
.netlify

# ================================
# Firebase
# ================================
.firebase/
firebase-debug.log
firestore-debug.log

# ================================
# Supabase
# ================================
.supabase/

# ================================
# Custom project files
# ================================
uploads/
storage/
backups/
*.key
*.pem
*.crt
*.p12

# ================================
# Documentation build
# ================================
docs/build/
docs/.docusaurus/

# ================================
# Analytics
# ================================
.analytics/

# ================================
# Misc
# ================================
*.tgz
*.tar.gz
