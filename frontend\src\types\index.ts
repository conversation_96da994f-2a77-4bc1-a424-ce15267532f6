// 用户相关类型定义
export interface User {
  id: string;
  email: string;
  username: string;
  displayName?: string;
  avatar?: string;
  bio?: string;
  userType: 'individual' | 'enterprise';
  status: 'pending' | 'active' | 'suspended' | 'banned';

  // 个人创作者字段
  firstName?: string;
  lastName?: string;
  phoneNumber?: string;
  country?: string;

  // 企业创作者字段
  companyName?: string;
  companyRegistrationNumber?: string;
  companyAddress?: string;
  taxId?: string;
  contactPersonName?: string;
  contactPersonTitle?: string;

  // PayPal相关字段
  paypalEmail?: string;
  paypalAccountId?: string;

  // 财务相关字段
  balance: number;
  frozenBalance: number;
  totalEarnings: number;
  totalSales: number;
  totalDownloads: number;
  totalLikes: number;

  // 积分系统
  points: number;
  lastSignInDate?: string;
  consecutiveSignInDays: number;

  // 邮箱验证
  emailVerified: boolean;

  // 账户设置
  emailNotifications: boolean;
  language: string;
  timezone: string;

  createdAt: string;
  updatedAt: string;
}

export interface UserRegistrationData {
  email: string;
  password: string;
  userType: 'individual' | 'enterprise';
  companyName?: string;
}

export interface UserLoginData {
  email: string;
  password: string;
}

// 资源相关类型定义
export type ResourceCategory =
  | 'fine_tuned_models'
  | 'lora'
  | 'workflows'
  | 'prompts'
  | 'tools';

export interface Resource {
  id: string;
  creatorId: string;
  title: string;
  description: string;
  category: ResourceCategory;
  tags: string[];
  priceUsd: number;
  pricePoints: number;
  fileUrl: string;
  fileSize: number;
  coverImageUrl?: string;
  downloadCount: number;
  salesCount: number;
  status: 'active' | 'inactive' | 'pending_review';
  createdAt: string;
  updatedAt: string;
}

export interface ResourceUploadData {
  title: string;
  description: string;
  category: ResourceCategory;
  tags: string[];
  priceUsd: number;
  pricePoints: number;
  file: File;
  coverImage?: File;
}

// 交易相关类型定义
export interface Transaction {
  id: string;
  buyerId: string;
  sellerId: string;
  resourceId: string;
  amountUsd: number;
  platformFee: number;
  creatorEarnings: number;
  paymentMethod: 'paypal' | 'points';
  paypalTransactionId?: string;
  status: 'pending' | 'completed' | 'failed' | 'refunded';
  downloadUrl?: string;
  downloadExpiresAt?: string;
  createdAt: string;
  updatedAt: string;
}

// API响应类型定义
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: {
    code: string;
    message: string;
    details?: any[];
  };
  message?: string;
  timestamp: string;
}

export interface PaginatedResponse<T> {
  items: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

// 表单相关类型定义
export interface FormErrors {
  [key: string]: string | undefined;
}

// 主题相关类型定义
export interface ThemeMode {
  mode: 'light' | 'dark';
}

// 路由相关类型定义
export interface RouteConfig {
  path: string;
  component: React.ComponentType;
  exact?: boolean;
  protected?: boolean;
}

// 文件上传相关类型定义
export interface FileUploadProgress {
  loaded: number;
  total: number;
  percentage: number;
}

export interface UploadChunk {
  chunk: Blob;
  chunkNumber: number;
  totalChunks: number;
  fileName: string;
  fileId: string;
}

// 积分相关类型定义
export interface PointsTransaction {
  id: string;
  userId: string;
  points: number;
  type: 'earned' | 'spent' | 'expired';
  source: 'daily_checkin' | 'first_upload' | 'sales_milestone' | 'purchase';
  description: string;
  createdAt: string;
}

// 排名相关类型定义
export interface CreatorRanking {
  userId: string;
  userName: string;
  userType: 'individual' | 'enterprise';
  totalSales: number;
  totalDownloads: number;
  totalEarnings: number;
  rank: number;
  period: 'week' | 'month' | 'all_time';
}

// 提现相关类型定义
export interface WithdrawalRequest {
  id: string;
  userId: string;
  amount: number;
  paypalFee: number;
  taxFee: number;
  netAmount: number;
  paypalEmail: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  paypalPayoutId?: string;
  processedAt?: string;
  createdAt: string;
}

// 退款相关类型定义
export interface RefundRequest {
  id: string;
  transactionId: string;
  requesterId: string;
  reason: string;
  adminNotes?: string;
  status: 'pending' | 'approved' | 'rejected';
  processedBy?: string;
  processedAt?: string;
  createdAt: string;
}
