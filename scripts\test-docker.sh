#!/bin/bash

# AIGC Service Hub Docker 配置测试脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 测试Docker配置文件语法
test_docker_compose_syntax() {
    log_info "测试Docker Compose配置文件语法..."
    
    # 测试开发环境配置
    if docker-compose -f docker-compose.dev.yml config > /dev/null 2>&1; then
        log_success "开发环境配置语法正确"
    else
        log_error "开发环境配置语法错误"
        return 1
    fi
    
    # 测试生产环境配置
    if docker-compose -f docker-compose.prod.yml config > /dev/null 2>&1; then
        log_success "生产环境配置语法正确"
    else
        log_error "生产环境配置语法错误"
        return 1
    fi
}

# 测试Dockerfile语法
test_dockerfile_syntax() {
    log_info "测试Dockerfile语法..."
    
    # 测试前端Dockerfile
    if docker build --dry-run frontend/ > /dev/null 2>&1; then
        log_success "前端Dockerfile语法正确"
    else
        log_warning "前端Dockerfile可能存在问题（需要实际构建验证）"
    fi
    
    # 测试后端Dockerfile
    if docker build --dry-run backend/ > /dev/null 2>&1; then
        log_success "后端Dockerfile语法正确"
    else
        log_warning "后端Dockerfile可能存在问题（需要实际构建验证）"
    fi
}

# 测试网络配置
test_network_config() {
    log_info "测试网络配置..."
    
    # 检查网络配置是否合理
    local subnet_dev=$(docker-compose -f docker-compose.dev.yml config | grep -A 1 "subnet:" | tail -1 | awk '{print $2}')
    local subnet_prod=$(docker-compose -f docker-compose.prod.yml config | grep -A 1 "subnet:" | tail -1 | awk '{print $2}')
    
    if [ "$subnet_dev" != "$subnet_prod" ]; then
        log_success "开发和生产环境使用不同的网络子网"
    else
        log_warning "开发和生产环境使用相同的网络子网"
    fi
}

# 测试卷配置
test_volume_config() {
    log_info "测试卷配置..."
    
    # 检查是否有持久化卷
    local dev_volumes=$(docker-compose -f docker-compose.dev.yml config | grep -c "driver: local" || true)
    local prod_volumes=$(docker-compose -f docker-compose.prod.yml config | grep -c "driver: local" || true)
    
    if [ "$dev_volumes" -gt 0 ] && [ "$prod_volumes" -gt 0 ]; then
        log_success "开发和生产环境都配置了持久化卷"
    else
        log_warning "卷配置可能不完整"
    fi
}

# 测试环境变量
test_env_config() {
    log_info "测试环境变量配置..."
    
    if [ -f ".env.docker" ]; then
        log_success "环境变量模板文件存在"
    else
        log_error "环境变量模板文件不存在"
        return 1
    fi
    
    # 检查关键环境变量
    local required_vars=("DB_PASSWORD" "JWT_SECRET" "PAYPAL_CLIENT_ID")
    for var in "${required_vars[@]}"; do
        if grep -q "$var" .env.docker; then
            log_success "环境变量 $var 已定义"
        else
            log_warning "环境变量 $var 未定义"
        fi
    done
}

# 测试配置文件
test_config_files() {
    log_info "测试配置文件..."
    
    local config_files=(
        "frontend/nginx.conf"
        "nginx/nginx.prod.conf"
        "redis/redis.conf"
        "monitoring/prometheus.yml"
    )
    
    for file in "${config_files[@]}"; do
        if [ -f "$file" ]; then
            log_success "配置文件 $file 存在"
        else
            log_error "配置文件 $file 不存在"
        fi
    done
}

# 测试脚本文件
test_script_files() {
    log_info "测试脚本文件..."
    
    local script_files=(
        "scripts/deploy.sh"
        "scripts/deploy.ps1"
    )
    
    for file in "${script_files[@]}"; do
        if [ -f "$file" ]; then
            log_success "脚本文件 $file 存在"
            
            # 检查脚本是否可执行
            if [ -x "$file" ]; then
                log_success "脚本文件 $file 具有执行权限"
            else
                log_warning "脚本文件 $file 没有执行权限"
            fi
        else
            log_error "脚本文件 $file 不存在"
        fi
    done
}

# 测试Docker忽略文件
test_dockerignore() {
    log_info "测试Docker忽略文件..."
    
    local dockerignore_files=(
        "frontend/.dockerignore"
        "backend/.dockerignore"
    )
    
    for file in "${dockerignore_files[@]}"; do
        if [ -f "$file" ]; then
            log_success "Docker忽略文件 $file 存在"
        else
            log_warning "Docker忽略文件 $file 不存在"
        fi
    done
}

# 主测试函数
main() {
    log_info "开始Docker配置测试..."
    echo
    
    local test_passed=0
    local test_total=7
    
    # 运行所有测试
    if test_docker_compose_syntax; then ((test_passed++)); fi
    echo
    
    if test_dockerfile_syntax; then ((test_passed++)); fi
    echo
    
    if test_network_config; then ((test_passed++)); fi
    echo
    
    if test_volume_config; then ((test_passed++)); fi
    echo
    
    if test_env_config; then ((test_passed++)); fi
    echo
    
    if test_config_files; then ((test_passed++)); fi
    echo
    
    if test_script_files; then ((test_passed++)); fi
    echo
    
    test_dockerignore
    echo
    
    # 输出测试结果
    log_info "测试完成！"
    echo "通过测试: $test_passed/$test_total"
    
    if [ "$test_passed" -eq "$test_total" ]; then
        log_success "所有测试通过！Docker配置正确。"
        return 0
    else
        log_warning "部分测试未通过，请检查配置。"
        return 1
    fi
}

# 运行测试
main "$@"
