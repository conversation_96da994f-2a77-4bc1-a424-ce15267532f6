#!/bin/bash

# AIGC Service Hub 项目初始化脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 参数解析
SKIP_INSTALL=false
SKIP_GIT=false
VERBOSE=false

while [[ $# -gt 0 ]]; do
    case $1 in
        --skip-install)
            SKIP_INSTALL=true
            shift
            ;;
        --skip-git)
            SKIP_GIT=true
            shift
            ;;
        --verbose)
            VERBOSE=true
            shift
            ;;
        -h|--help)
            echo "用法: $0 [选项]"
            echo "选项:"
            echo "  --skip-install    跳过依赖安装"
            echo "  --skip-git        跳过Git初始化"
            echo "  --verbose         详细输出"
            echo "  -h, --help        显示帮助信息"
            exit 0
            ;;
        *)
            echo "未知选项: $1"
            exit 1
            ;;
    esac
done

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${CYAN}[$1/$2]${NC} $3"
}

# 检查先决条件
check_prerequisites() {
    log_info "检查先决条件..."
    
    local errors=()
    
    # 检查Node.js
    if command -v node &> /dev/null; then
        local node_version=$(node --version | cut -d'v' -f2)
        local required_version="18.0.0"
        
        if [ "$(printf '%s\n' "$required_version" "$node_version" | sort -V | head -n1)" = "$required_version" ]; then
            log_success "Node.js版本: $node_version ✓"
        else
            errors+=("Node.js版本过低: $node_version (需要 >= $required_version)")
        fi
    else
        errors+=("Node.js未安装")
    fi
    
    # 检查npm
    if command -v npm &> /dev/null; then
        local npm_version=$(npm --version)
        local required_version="8.0.0"
        
        if [ "$(printf '%s\n' "$required_version" "$npm_version" | sort -V | head -n1)" = "$required_version" ]; then
            log_success "npm版本: $npm_version ✓"
        else
            errors+=("npm版本过低: $npm_version (需要 >= $required_version)")
        fi
    else
        errors+=("npm未安装")
    fi
    
    # 检查Git
    if [ "$SKIP_GIT" = false ]; then
        if command -v git &> /dev/null; then
            local git_version=$(git --version)
            log_success "Git已安装: $git_version ✓"
        else
            errors+=("Git未安装")
        fi
    fi
    
    if [ ${#errors[@]} -gt 0 ]; then
        log_error "先决条件检查失败:"
        for error in "${errors[@]}"; do
            log_error "  - $error"
        done
        return 1
    fi
    
    log_success "所有先决条件满足 ✓"
    return 0
}

# 初始化Git仓库
initialize_git() {
    if [ "$SKIP_GIT" = true ]; then
        log_info "跳过Git初始化"
        return 0
    fi
    
    log_info "初始化Git仓库..."
    
    if [ ! -d ".git" ]; then
        git init
        log_success "Git仓库已初始化 ✓"
    else
        log_info "Git仓库已存在"
    fi
    
    # 设置Git配置模板
    if ! git config --get commit.template &> /dev/null; then
        git config commit.template .gitmessage
        log_success "Git提交模板已设置 ✓"
    fi
    
    return 0
}

# 安装依赖
install_dependencies() {
    if [ "$SKIP_INSTALL" = true ]; then
        log_info "跳过依赖安装"
        return 0
    fi
    
    log_info "安装项目依赖..."
    
    # 安装根目录依赖
    log_info "安装根目录依赖..."
    npm install
    log_success "根目录依赖安装完成 ✓"
    
    # 安装前端依赖
    log_info "安装前端依赖..."
    cd frontend && npm install && cd ..
    log_success "前端依赖安装完成 ✓"
    
    # 安装后端依赖
    log_info "安装后端依赖..."
    cd backend && npm install && cd ..
    log_success "后端依赖安装完成 ✓"
    
    return 0
}

# 设置Git hooks
setup_git_hooks() {
    if [ "$SKIP_GIT" = true ]; then
        log_info "跳过Git hooks设置"
        return 0
    fi
    
    log_info "设置Git hooks..."
    
    # 初始化husky
    npx husky install
    log_success "Husky已初始化 ✓"
    
    return 0
}

# 创建环境配置文件
create_environment_files() {
    log_info "创建环境配置文件..."
    
    # 前端环境文件
    if [ ! -f "frontend/.env" ]; then
        cat > frontend/.env << EOF
# 前端环境配置
VITE_API_BASE_URL=http://localhost:3000/api
VITE_APP_NAME=AIGC Service Hub
VITE_APP_VERSION=1.0.0
VITE_ENABLE_MOCK=false
EOF
        log_success "前端环境文件已创建 ✓"
    else
        log_info "前端环境文件已存在"
    fi
    
    # 后端环境文件
    if [ ! -f "backend/.env" ]; then
        cat > backend/.env << EOF
# 后端环境配置
NODE_ENV=development
PORT=3000

# 数据库配置
DB_HOST=localhost
DB_PORT=5432
DB_NAME=aigc_service_hub
DB_USER=postgres
DB_PASSWORD=password

# JWT配置
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRES_IN=7d

# Redis配置 (可选)
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

# AWS配置 (生产环境)
AWS_REGION=us-east-1
AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
S3_BUCKET_NAME=

# 邮件配置 (可选)
SMTP_HOST=
SMTP_PORT=587
SMTP_USER=
SMTP_PASS=

# 日志配置
LOG_LEVEL=debug
EOF
        log_success "后端环境文件已创建 ✓"
    else
        log_info "后端环境文件已存在"
    fi
}

# 运行初始检查
run_initial_checks() {
    log_info "运行初始代码质量检查..."
    
    # 格式化代码
    log_info "格式化代码..."
    npm run format
    log_success "代码格式化完成 ✓"
    
    # 运行类型检查
    log_info "运行TypeScript类型检查..."
    if npm run type-check; then
        log_success "类型检查通过 ✓"
        return 0
    else
        log_warning "初始检查中发现问题，请稍后手动修复"
        return 1
    fi
}

# 显示下一步指引
show_next_steps() {
    log_success "项目初始化完成！"
    echo
    echo -e "${CYAN}🚀 下一步操作:${NC}"
    echo
    echo "1. 配置数据库:"
    echo "   - 安装PostgreSQL"
    echo "   - 创建数据库: aigc_service_hub"
    echo "   - 更新 backend/.env 中的数据库配置"
    echo
    echo "2. 启动开发服务器:"
    echo "   npm run dev                    # 同时启动前后端"
    echo "   # 或者分别启动:"
    echo "   cd frontend && npm run dev     # 前端: http://localhost:5173"
    echo "   cd backend && npm run dev      # 后端: http://localhost:3000"
    echo
    echo "3. 运行代码质量检查:"
    echo "   ./scripts/check-code-quality.sh"
    echo
    echo "4. 查看开发指南:"
    echo "   DEVELOPMENT_GUIDE.md"
    echo "   CODE_STANDARDS.md"
    echo
    echo -e "${GREEN}✨ 开始愉快的开发吧！${NC}"
}

# 主函数
main() {
    local total_steps=7
    local current_step=0
    
    echo "🚀 AIGC Service Hub 项目初始化"
    echo "================================"
    echo
    
    # 步骤1: 检查先决条件
    ((current_step++))
    log_step $current_step $total_steps "检查先决条件"
    if ! check_prerequisites; then
        log_error "项目初始化失败"
        return 1
    fi
    echo
    
    # 步骤2: 初始化Git
    ((current_step++))
    log_step $current_step $total_steps "初始化Git仓库"
    if ! initialize_git; then
        log_error "项目初始化失败"
        return 1
    fi
    echo
    
    # 步骤3: 安装依赖
    ((current_step++))
    log_step $current_step $total_steps "安装项目依赖"
    if ! install_dependencies; then
        log_error "项目初始化失败"
        return 1
    fi
    echo
    
    # 步骤4: 设置Git hooks
    ((current_step++))
    log_step $current_step $total_steps "设置Git hooks"
    if ! setup_git_hooks; then
        log_error "项目初始化失败"
        return 1
    fi
    echo
    
    # 步骤5: 创建环境文件
    ((current_step++))
    log_step $current_step $total_steps "创建环境配置文件"
    create_environment_files
    echo
    
    # 步骤6: 运行初始检查
    ((current_step++))
    log_step $current_step $total_steps "运行初始代码检查"
    run_initial_checks
    echo
    
    # 步骤7: 显示下一步
    ((current_step++))
    log_step $current_step $total_steps "完成初始化"
    show_next_steps
    
    return 0
}

# 运行主函数
main "$@"
