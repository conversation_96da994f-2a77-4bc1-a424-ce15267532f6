import {
  AccountCircle,
  Assessment,
  AttachMoney,
  CloudUpload,
  Download,
  Edit,
  ExitToApp,
  Favorite,
  MoreVert,
  Settings,
  Visibility,
} from '@mui/icons-material';
import {
  Alert,
  Avatar,
  Box,
  Button,
  Card,
  CardContent,
  Chip,
  Container,
  Divider,
  Grid,
  IconButton,
  List,
  ListItem,
  ListItemAvatar,
  ListItemSecondaryAction,
  ListItemText,
  Paper,
  Skeleton,
  Tab,
  Tabs,
  Typography,
} from '@mui/material';
import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../hooks/useAuth';

// 模拟资源数据类型
interface Resource {
  id: string;
  title: string;
  category: string;
  price: number;
  downloads: number;
  likes: number;
  revenue: number;
  status: 'active' | 'pending' | 'rejected' | 'draft';
  createdAt: string;
  thumbnail?: string;
}

// Tab面板组件
interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role='tabpanel'
      hidden={value !== index}
      id={`profile-tabpanel-${index}`}
      aria-labelledby={`profile-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ py: 3 }}>{children}</Box>}
    </div>
  );
}

const ProfileCenterPage: React.FC = () => {
  const { user, logout } = useAuth();
  const navigate = useNavigate();
  const [tabValue, setTabValue] = useState(0);
  const [loading, setLoading] = useState(true);
  const [resources, setResources] = useState<Resource[]>([]);

  // 模拟数据加载
  useEffect(() => {
    const loadData = async () => {
      setLoading(true);
      // 模拟API调用延迟
      await new Promise(resolve => setTimeout(resolve, 1000));

      // 模拟资源数据
      const mockResources: Resource[] = [
        {
          id: '1',
          title: 'AI人像生成LoRA模型',
          category: 'LoRA',
          price: 29.99,
          downloads: 156,
          likes: 89,
          revenue: 4678.44,
          status: 'active',
          createdAt: '2024-01-15',
        },
        {
          id: '2',
          title: 'Stable Diffusion工作流',
          category: '工作流',
          price: 19.99,
          downloads: 234,
          likes: 145,
          revenue: 4677.66,
          status: 'active',
          createdAt: '2024-01-10',
        },
        {
          id: '3',
          title: '商业摄影提示词集',
          category: '提示词',
          price: 9.99,
          downloads: 89,
          likes: 67,
          revenue: 889.11,
          status: 'pending',
          createdAt: '2024-01-08',
        },
      ];

      setResources(mockResources);
      setLoading(false);
    };

    loadData();
  }, []);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const handleLogout = () => {
    logout();
    navigate('/');
  };

  if (!user) {
    return null;
  }

  // 获取用户显示名称
  const getDisplayName = () => {
    if (user.userType === 'enterprise') {
      return user.companyName || user.username;
    }
    return (
      user.displayName || `${user.firstName} ${user.lastName}` || user.username
    );
  };

  // 获取用户状态颜色
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'success';
      case 'pending':
        return 'warning';
      case 'suspended':
        return 'error';
      case 'banned':
        return 'error';
      default:
        return 'default';
    }
  };

  // 获取资源状态颜色
  const getResourceStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'success';
      case 'pending':
        return 'warning';
      case 'rejected':
        return 'error';
      case 'draft':
        return 'default';
      default:
        return 'default';
    }
  };

  // 获取资源状态文本
  const getResourceStatusText = (status: string) => {
    switch (status) {
      case 'active':
        return '已发布';
      case 'pending':
        return '审核中';
      case 'rejected':
        return '已拒绝';
      case 'draft':
        return '草稿';
      default:
        return status;
    }
  };

  // 计算统计数据
  const totalRevenue = resources.reduce(
    (sum, resource) => sum + resource.revenue,
    0
  );
  const totalDownloads = resources.reduce(
    (sum, resource) => sum + resource.downloads,
    0
  );
  const totalLikes = resources.reduce(
    (sum, resource) => sum + resource.likes,
    0
  );
  const activeResources = resources.filter(r => r.status === 'active').length;

  return (
    <Container maxWidth='lg' sx={{ py: 4 }}>
      {/* 页面标题和用户信息 */}
      <Box sx={{ mb: 4 }}>
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            mb: 2,
          }}
        >
          <Typography variant='h4' component='h1' fontWeight='bold'>
            个人中心
          </Typography>
          <Button
            variant='outlined'
            startIcon={<ExitToApp />}
            onClick={handleLogout}
            color='inherit'
          >
            退出登录
          </Button>
        </Box>

        <Paper sx={{ p: 3 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 3 }}>
            <Avatar
              sx={{
                width: 80,
                height: 80,
                bgcolor: 'primary.main',
                fontSize: '2rem',
              }}
            >
              {user.avatar ? (
                <img
                  src={user.avatar}
                  alt='Avatar'
                  style={{ width: '100%', height: '100%' }}
                />
              ) : (
                <AccountCircle sx={{ fontSize: '3rem' }} />
              )}
            </Avatar>

            <Box sx={{ flex: 1 }}>
              <Box
                sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 1 }}
              >
                <Typography variant='h5' fontWeight='bold'>
                  {getDisplayName()}
                </Typography>
                <IconButton
                  size='small'
                  onClick={() => navigate('/profile/edit')}
                >
                  <Edit fontSize='small' />
                </IconButton>
              </Box>

              <Box sx={{ display: 'flex', gap: 1, mb: 1 }}>
                <Chip
                  label={
                    user.userType === 'individual' ? '个人创作者' : '企业创作者'
                  }
                  color='primary'
                  size='small'
                />
                <Chip
                  label={user.status === 'active' ? '活跃' : user.status}
                  color={getStatusColor(user.status) as any}
                  size='small'
                />
                {user.emailVerified && (
                  <Chip label='邮箱已验证' color='success' size='small' />
                )}
              </Box>

              <Typography variant='body2' color='text.secondary'>
                {user.email}
              </Typography>

              {user.bio && (
                <Typography variant='body2' sx={{ mt: 1 }}>
                  {user.bio}
                </Typography>
              )}
            </Box>
          </Box>
        </Paper>
      </Box>

      {/* 统计数据 */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <AttachMoney color='primary' />
                <Box>
                  <Typography variant='h6' fontWeight='bold'>
                    ${totalRevenue.toFixed(2)}
                  </Typography>
                  <Typography variant='body2' color='text.secondary'>
                    总收入
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <CloudUpload color='info' />
                <Box>
                  <Typography variant='h6' fontWeight='bold'>
                    {activeResources}
                  </Typography>
                  <Typography variant='body2' color='text.secondary'>
                    发布资源
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <Download color='success' />
                <Box>
                  <Typography variant='h6' fontWeight='bold'>
                    {totalDownloads}
                  </Typography>
                  <Typography variant='body2' color='text.secondary'>
                    总下载量
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <Favorite color='error' />
                <Box>
                  <Typography variant='h6' fontWeight='bold'>
                    {totalLikes}
                  </Typography>
                  <Typography variant='body2' color='text.secondary'>
                    获得点赞
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Tab导航和内容 */}
      <Paper sx={{ mb: 4 }}>
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs
            value={tabValue}
            onChange={handleTabChange}
            aria-label='个人中心导航'
          >
            <Tab label='我的资源' />
            <Tab label='收入明细' />
            <Tab label='下载记录' />
            <Tab label='账户设置' />
          </Tabs>
        </Box>

        {/* 我的资源 */}
        <TabPanel value={tabValue} index={0}>
          <Box
            sx={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              mb: 3,
            }}
          >
            <Typography variant='h6' fontWeight='bold'>
              我的资源 ({resources.length})
            </Typography>
            <Button
              variant='contained'
              startIcon={<CloudUpload />}
              onClick={() => navigate('/upload')}
            >
              上传新资源
            </Button>
          </Box>

          {loading ? (
            <Box>
              {[1, 2, 3].map(item => (
                <Card key={item} sx={{ mb: 2 }}>
                  <CardContent>
                    <Box sx={{ display: 'flex', gap: 2 }}>
                      <Skeleton variant='rectangular' width={80} height={60} />
                      <Box sx={{ flex: 1 }}>
                        <Skeleton variant='text' width='60%' height={24} />
                        <Skeleton variant='text' width='40%' height={20} />
                        <Skeleton variant='text' width='80%' height={20} />
                      </Box>
                    </Box>
                  </CardContent>
                </Card>
              ))}
            </Box>
          ) : resources.length === 0 ? (
            <Alert severity='info'>
              您还没有上传任何资源。
              <Button onClick={() => navigate('/upload')}>立即上传</Button>
            </Alert>
          ) : (
            <List>
              {resources.map(resource => (
                <Card key={resource.id} sx={{ mb: 2 }}>
                  <CardContent>
                    <ListItem sx={{ px: 0 }}>
                      <ListItemAvatar>
                        <Avatar
                          variant='rounded'
                          sx={{ width: 60, height: 60, bgcolor: 'grey.200' }}
                        >
                          {resource.thumbnail ? (
                            <img
                              src={resource.thumbnail}
                              alt={resource.title}
                              style={{ width: '100%', height: '100%' }}
                            />
                          ) : (
                            <CloudUpload />
                          )}
                        </Avatar>
                      </ListItemAvatar>

                      <ListItemText
                        primary={
                          <Box
                            sx={{
                              display: 'flex',
                              alignItems: 'center',
                              gap: 1,
                            }}
                          >
                            <Typography variant='subtitle1' fontWeight='bold'>
                              {resource.title}
                            </Typography>
                            <Chip
                              label={getResourceStatusText(resource.status)}
                              color={
                                getResourceStatusColor(resource.status) as any
                              }
                              size='small'
                            />
                          </Box>
                        }
                        secondary={
                          <Box sx={{ mt: 1 }}>
                            <Typography variant='body2' color='text.secondary'>
                              分类: {resource.category} | 价格: $
                              {resource.price} | 创建时间: {resource.createdAt}
                            </Typography>
                            <Box sx={{ display: 'flex', gap: 3, mt: 1 }}>
                              <Box
                                sx={{
                                  display: 'flex',
                                  alignItems: 'center',
                                  gap: 0.5,
                                }}
                              >
                                <Download fontSize='small' color='action' />
                                <Typography variant='body2'>
                                  {resource.downloads}
                                </Typography>
                              </Box>
                              <Box
                                sx={{
                                  display: 'flex',
                                  alignItems: 'center',
                                  gap: 0.5,
                                }}
                              >
                                <Favorite fontSize='small' color='action' />
                                <Typography variant='body2'>
                                  {resource.likes}
                                </Typography>
                              </Box>
                              <Box
                                sx={{
                                  display: 'flex',
                                  alignItems: 'center',
                                  gap: 0.5,
                                }}
                              >
                                <AttachMoney fontSize='small' color='action' />
                                <Typography variant='body2'>
                                  ${resource.revenue.toFixed(2)}
                                </Typography>
                              </Box>
                            </Box>
                          </Box>
                        }
                      />

                      <ListItemSecondaryAction>
                        <Box sx={{ display: 'flex', gap: 1 }}>
                          <IconButton
                            size='small'
                            onClick={() => navigate(`/resource/${resource.id}`)}
                          >
                            <Visibility />
                          </IconButton>
                          <IconButton
                            size='small'
                            onClick={() =>
                              navigate(`/resource/${resource.id}/edit`)
                            }
                          >
                            <Edit />
                          </IconButton>
                          <IconButton size='small'>
                            <MoreVert />
                          </IconButton>
                        </Box>
                      </ListItemSecondaryAction>
                    </ListItem>
                  </CardContent>
                </Card>
              ))}
            </List>
          )}
        </TabPanel>

        {/* 收入明细 */}
        <TabPanel value={tabValue} index={1}>
          <Typography variant='h6' fontWeight='bold' gutterBottom>
            收入明细
          </Typography>
          <Alert severity='info' sx={{ mb: 2 }}>
            收入明细功能正在开发中，敬请期待。
          </Alert>
        </TabPanel>

        {/* 下载记录 */}
        <TabPanel value={tabValue} index={2}>
          <Typography variant='h6' fontWeight='bold' gutterBottom>
            下载记录
          </Typography>
          <Alert severity='info' sx={{ mb: 2 }}>
            下载记录功能正在开发中，敬请期待。
          </Alert>
        </TabPanel>

        {/* 账户设置 */}
        <TabPanel value={tabValue} index={3}>
          <Typography variant='h6' fontWeight='bold' gutterBottom>
            账户设置
          </Typography>
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <Card>
                <CardContent>
                  <Typography
                    variant='subtitle1'
                    fontWeight='bold'
                    gutterBottom
                  >
                    个人信息
                  </Typography>
                  <Button
                    variant='outlined'
                    startIcon={<Edit />}
                    onClick={() => navigate('/profile/edit')}
                    fullWidth
                  >
                    编辑个人信息
                  </Button>
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12} md={6}>
              <Card>
                <CardContent>
                  <Typography
                    variant='subtitle1'
                    fontWeight='bold'
                    gutterBottom
                  >
                    安全设置
                  </Typography>
                  <Button
                    variant='outlined'
                    startIcon={<Settings />}
                    onClick={() => navigate('/profile/security')}
                    fullWidth
                  >
                    修改密码
                  </Button>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </TabPanel>
      </Paper>

      {/* 快速操作 */}
      <Paper sx={{ p: 3 }}>
        <Typography variant='h6' fontWeight='bold' gutterBottom>
          快速操作
        </Typography>
        <Divider sx={{ mb: 3 }} />

        <Grid container spacing={2}>
          <Grid item xs={12} sm={6} md={3}>
            <Button
              fullWidth
              variant='contained'
              startIcon={<CloudUpload />}
              size='large'
              sx={{ py: 2 }}
              onClick={() => navigate('/upload')}
            >
              上传资源
            </Button>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Button
              fullWidth
              variant='outlined'
              startIcon={<Assessment />}
              size='large'
              sx={{ py: 2 }}
              onClick={() => navigate('/analytics')}
            >
              数据分析
            </Button>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Button
              fullWidth
              variant='outlined'
              startIcon={<AttachMoney />}
              size='large'
              sx={{ py: 2 }}
              onClick={() => navigate('/finances')}
            >
              财务管理
            </Button>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Button
              fullWidth
              variant='outlined'
              startIcon={<Settings />}
              size='large'
              sx={{ py: 2 }}
              onClick={() => navigate('/settings')}
            >
              账户设置
            </Button>
          </Grid>
        </Grid>
      </Paper>
    </Container>
  );
};

export default ProfileCenterPage;
