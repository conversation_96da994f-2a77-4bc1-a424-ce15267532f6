# AIGC Service Hub Frontend Dockerfile
# 多阶段构建：构建阶段 + 运行阶段

# ================================
# 构建阶段
# ================================
FROM node:18-alpine AS builder

# 设置工作目录
WORKDIR /app

# 设置npm镜像源（可选，提高国内构建速度）
RUN npm config set registry https://registry.npmmirror.com

# 复制package文件
COPY package*.json ./

# 安装所有依赖（构建阶段需要开发依赖）
RUN npm ci && npm cache clean --force

# 复制源代码
COPY . .

# 构建应用
RUN npm run build

# ================================
# 运行阶段
# ================================
FROM nginx:alpine AS runtime

# 安装必要的工具
RUN apk add --no-cache curl

# 创建nginx用户和组
RUN addgroup -g 1001 -S nginx && \
    adduser -S nginx -u 1001

# 复制构建产物
COPY --from=builder /app/dist /usr/share/nginx/html

# 复制nginx配置
COPY nginx.conf /etc/nginx/nginx.conf

# 创建日志目录
RUN mkdir -p /var/log/nginx && \
    chown -R nginx:nginx /var/log/nginx && \
    chown -R nginx:nginx /usr/share/nginx/html

# 暴露端口
EXPOSE 80

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD curl -f http://localhost/ || exit 1

# 启动nginx
CMD ["nginx", "-g", "daemon off;"]
