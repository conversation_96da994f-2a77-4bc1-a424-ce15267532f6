/* AIGC Service Hub Brand Colors */
/* 品牌色彩系统 CSS 变量定义 */

:root {
  /* ================================
     主品牌色 - 蓝色系
     ================================ */
  --primary-50: #eff6ff;
  --primary-100: #dbeafe;
  --primary-200: #bfdbfe;
  --primary-300: #93c5fd;
  --primary-400: #60a5fa;
  --primary-500: #3b82f6;
  --primary-600: #2563eb; /* 主品牌色 */
  --primary-700: #1d4ed8;
  --primary-800: #1e40af;
  --primary-900: #1e3a8a;

  /* ================================
     辅助色 - 紫色系
     ================================ */
  --secondary-50: #faf5ff;
  --secondary-100: #f3e8ff;
  --secondary-200: #e9d5ff;
  --secondary-300: #d8b4fe;
  --secondary-400: #c084fc;
  --secondary-500: #a855f7;
  --secondary-600: #9333ea;
  --secondary-700: #7c3aed; /* 主辅助色 */
  --secondary-800: #6b21a8;
  --secondary-900: #581c87;

  /* ================================
     功能色彩 - 成功色
     ================================ */
  --success-50: #ecfdf5;
  --success-100: #d1fae5;
  --success-200: #a7f3d0;
  --success-300: #6ee7b7;
  --success-400: #34d399;
  --success-500: #10b981; /* 主成功色 */
  --success-600: #059669;
  --success-700: #047857;
  --success-800: #065f46;
  --success-900: #064e3b;

  /* ================================
     功能色彩 - 警告色
     ================================ */
  --warning-50: #fffbeb;
  --warning-100: #fef3c7;
  --warning-200: #fde68a;
  --warning-300: #fcd34d;
  --warning-400: #fbbf24;
  --warning-500: #f59e0b; /* 主警告色 */
  --warning-600: #d97706;
  --warning-700: #b45309;
  --warning-800: #92400e;
  --warning-900: #78350f;

  /* ================================
     功能色彩 - 错误色
     ================================ */
  --error-50: #fef2f2;
  --error-100: #fee2e2;
  --error-200: #fecaca;
  --error-300: #fca5a5;
  --error-400: #f87171;
  --error-500: #ef4444; /* 主错误色 */
  --error-600: #dc2626;
  --error-700: #b91c1c;
  --error-800: #991b1b;
  --error-900: #7f1d1d;

  /* ================================
     功能色彩 - 信息色
     ================================ */
  --info-50: #ecfeff;
  --info-100: #cffafe;
  --info-200: #a5f3fc;
  --info-300: #67e8f9;
  --info-400: #22d3ee;
  --info-500: #06b6d4; /* 主信息色 */
  --info-600: #0891b2;
  --info-700: #0e7490;
  --info-800: #155e75;
  --info-900: #164e63;

  /* ================================
     中性色系 - 灰色
     ================================ */
  --gray-50: #f9fafb; /* 背景色 */
  --gray-100: #f3f4f6; /* 浅背景 */
  --gray-200: #e5e7eb; /* 边框色 */
  --gray-300: #d1d5db; /* 分割线 */
  --gray-400: #9ca3af; /* 占位符 */
  --gray-500: #6b7280; /* 辅助文字 */
  --gray-600: #4b5563; /* 次要文字 */
  --gray-700: #374151; /* 主要文字 */
  --gray-800: #1f2937; /* 标题文字 */
  --gray-900: #111827; /* 最深文字 */

  /* ================================
     特殊色彩
     ================================ */
  --white: #ffffff;
  --black: #000000;

  /* ================================
     透明度变量
     ================================ */
  --overlay-light: rgba(255, 255, 255, 0.9);
  --overlay-dark: rgba(0, 0, 0, 0.5);
  --backdrop: rgba(0, 0, 0, 0.25);

  /* ================================
     语义化颜色别名
     ================================ */
  --color-brand: var(--primary-600);
  --color-brand-light: var(--primary-500);
  --color-brand-dark: var(--primary-700);

  --color-accent: var(--secondary-600);
  --color-accent-light: var(--secondary-500);
  --color-accent-dark: var(--secondary-700);

  --color-text-primary: var(--gray-900);
  --color-text-secondary: var(--gray-700);
  --color-text-muted: var(--gray-500);
  --color-text-inverse: var(--white);

  --color-bg-primary: var(--white);
  --color-bg-secondary: var(--gray-50);
  --color-bg-tertiary: var(--gray-100);

  --color-border-primary: var(--gray-200);
  --color-border-secondary: var(--gray-300);
  --color-border-focus: var(--primary-500);

  /* ================================
     渐变色定义
     ================================ */
  --gradient-brand: linear-gradient(
    135deg,
    var(--primary-600),
    var(--primary-700)
  );
  --gradient-accent: linear-gradient(
    135deg,
    var(--secondary-600),
    var(--secondary-700)
  );
  --gradient-rainbow: linear-gradient(
    135deg,
    var(--primary-500),
    var(--secondary-500),
    var(--info-500)
  );
  --gradient-success: linear-gradient(
    135deg,
    var(--success-500),
    var(--success-600)
  );
  --gradient-warning: linear-gradient(
    135deg,
    var(--warning-500),
    var(--warning-600)
  );
  --gradient-error: linear-gradient(135deg, var(--error-500), var(--error-600));
}

/* ================================
   深色模式适配
   ================================ */
@media (prefers-color-scheme: dark) {
  :root {
    --color-text-primary: var(--gray-100);
    --color-text-secondary: var(--gray-300);
    --color-text-muted: var(--gray-400);
    --color-text-inverse: var(--gray-900);

    --color-bg-primary: var(--gray-900);
    --color-bg-secondary: var(--gray-800);
    --color-bg-tertiary: var(--gray-700);

    --color-border-primary: var(--gray-700);
    --color-border-secondary: var(--gray-600);
  }
}

/* ================================
   深色模式强制类
   ================================ */
.dark {
  --color-text-primary: var(--gray-100);
  --color-text-secondary: var(--gray-300);
  --color-text-muted: var(--gray-400);
  --color-text-inverse: var(--gray-900);

  --color-bg-primary: var(--gray-900);
  --color-bg-secondary: var(--gray-800);
  --color-bg-tertiary: var(--gray-700);

  --color-border-primary: var(--gray-700);
  --color-border-secondary: var(--gray-600);
}

/* ================================
   实用工具类
   ================================ */
.text-brand {
  color: var(--color-brand);
}
.text-accent {
  color: var(--color-accent);
}
.text-success {
  color: var(--success-600);
}
.text-warning {
  color: var(--warning-600);
}
.text-error {
  color: var(--error-600);
}
.text-info {
  color: var(--info-600);
}

.bg-brand {
  background-color: var(--color-brand);
}
.bg-accent {
  background-color: var(--color-accent);
}
.bg-success {
  background-color: var(--success-600);
}
.bg-warning {
  background-color: var(--warning-600);
}
.bg-error {
  background-color: var(--error-600);
}
.bg-info {
  background-color: var(--info-600);
}

.border-brand {
  border-color: var(--color-brand);
}
.border-accent {
  border-color: var(--color-accent);
}
.border-success {
  border-color: var(--success-600);
}
.border-warning {
  border-color: var(--warning-600);
}
.border-error {
  border-color: var(--error-600);
}
.border-info {
  border-color: var(--info-600);
}

.gradient-brand {
  background: var(--gradient-brand);
}
.gradient-accent {
  background: var(--gradient-accent);
}
.gradient-rainbow {
  background: var(--gradient-rainbow);
}
