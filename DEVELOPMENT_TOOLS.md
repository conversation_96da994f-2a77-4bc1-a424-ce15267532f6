# AIGC Service Hub 开发工具配置

## 📋 配置概览

本文档描述了AIGC Service Hub项目的完整开发工具链配置，确保团队开发的一致性和代码质量。

## 🛠️ 已配置的开发工具

### 代码格式化和质量检查

**Prettier** - 代码格式化

- 配置文件: `.prettierrc.json`
- 忽略文件: `.prettierignore`
- 统一的代码格式标准
- 支持多种文件类型

**ESLint** - 代码质量检查

- 前端配置: `frontend/.eslintrc.json`
- 后端配置: `backend/.eslintrc.json`
- TypeScript支持
- React规则 (前端)
- Node.js规则 (后端)
- 安全检查规则

**EditorConfig** - 编辑器配置

- 配置文件: `.editorconfig`
- 统一的编辑器设置
- 跨IDE兼容性

### Git工作流

**Husky** - Git Hooks

- 预提交检查: `.husky/pre-commit`
- 提交信息检查: `.husky/commit-msg`
- 自动化代码质量检查

**lint-staged** - 暂存文件检查

- 配置文件: `.lintstagedrc.json`
- 只检查暂存的文件
- 提高检查效率

**Commitlint** - 提交信息规范

- 配置文件: `.commitlintrc.json`
- 约定式提交规范
- 提交信息模板: `.gitmessage`

### VSCode 集成

**工作区设置** - `.vscode/settings.json`

- 自动格式化配置
- ESLint集成
- TypeScript设置
- 文件关联配置

**推荐扩展** - `.vscode/extensions.json`

- 必需的开发扩展
- 可选的增强扩展
- 不推荐的扩展

**任务配置** - `.vscode/tasks.json`

- 开发服务器启动
- 构建任务
- 测试任务
- 代码检查任务

**调试配置** - `.vscode/launch.json`

- 前端调试 (Chrome)
- 后端调试 (Node.js)
- 测试调试
- 全栈调试

## 🚀 快速开始

### 1. 项目初始化

```bash
# 使用自动化脚本 (推荐)
./scripts/setup-project.ps1    # Windows
./scripts/setup-project.sh     # Linux/macOS

# 或手动执行
npm install
cd frontend && npm install && cd ..
cd backend && npm install && cd ..
npx husky install
```

### 2. 开发环境启动

```bash
# 同时启动前后端
npm run dev

# 或分别启动
npm run dev:frontend    # 前端: http://localhost:5173
npm run dev:backend     # 后端: http://localhost:3000
```

### 3. 代码质量检查

```bash
# 完整检查
./scripts/check-code-quality.ps1    # Windows
./scripts/check-code-quality.sh     # Linux/macOS

# 单独检查
npm run lint              # ESLint检查
npm run format:check      # 格式化检查
npm run type-check        # TypeScript类型检查
npm run test              # 运行测试
```

## 📝 开发工作流

### 日常开发流程

1. **创建功能分支**

   ```bash
   git checkout -b feature/your-feature-name
   ```

2. **编写代码**
   - VSCode会自动进行格式化和错误提示
   - 保存时自动运行ESLint修复

3. **提交代码**

   ```bash
   git add .
   git commit -m "feat(scope): your commit message"
   ```

   - 预提交检查会自动运行
   - 提交信息会自动验证

4. **推送和PR**

   ```bash
   git push origin feature/your-feature-name
   ```

   - GitHub Actions会自动运行CI检查

### 代码审查清单

- [ ] 代码符合ESLint规则
- [ ] 代码已正确格式化
- [ ] TypeScript类型检查通过
- [ ] 测试覆盖率满足要求
- [ ] 提交信息符合规范
- [ ] 无安全漏洞

## 🔧 工具配置详情

### Prettier配置

```json
{
  "semi": true,
  "trailingComma": "es5",
  "singleQuote": true,
  "printWidth": 80,
  "tabWidth": 2,
  "useTabs": false
}
```

### ESLint主要规则

- TypeScript严格模式
- React Hooks规则
- 导入排序和清理
- 无障碍性检查 (前端)
- 安全检查 (后端)

### Git提交规范

```
<type>(<scope>): <subject>

<body>

<footer>
```

**类型 (type)**:

- `feat`: 新功能
- `fix`: 修复bug
- `docs`: 文档更新
- `style`: 代码格式化
- `refactor`: 代码重构
- `test`: 测试相关
- `chore`: 其他杂项

## 📊 自动化检查

### 预提交检查

- ESLint代码质量检查
- Prettier代码格式化
- TypeScript类型检查
- 测试运行 (如有相关文件变更)

### CI/CD检查

- 多Node.js版本测试
- 代码质量检查
- 安全漏洞扫描
- 构建测试
- 测试覆盖率报告

## 🛡️ 代码质量保证

### 质量指标

- **ESLint错误**: 0个
- **TypeScript错误**: 0个
- **测试覆盖率**: >= 80%
- **安全漏洞**: 0个高危漏洞

### 质量检查脚本

```bash
# Windows
./scripts/check-code-quality.ps1

# Linux/macOS
./scripts/check-code-quality.sh
```

检查项目:

- Node.js和npm版本
- 依赖安装状态
- 代码格式化
- ESLint检查
- TypeScript类型检查
- 单元测试
- 安全漏洞扫描

## 🔍 故障排除

### 常见问题

**Husky hooks不工作**

```bash
# 重新安装husky
npx husky install
chmod +x .husky/pre-commit    # Linux/macOS
```

**ESLint错误**

```bash
# 自动修复
npm run lint:fix

# 查看详细错误
npm run lint
```

**TypeScript错误**

```bash
# 运行类型检查
npm run type-check

# 清理缓存
rm -rf node_modules/.cache
npm run clean
```

**依赖问题**

```bash
# 清理并重新安装
npm run clean
npm install
```

### 获取帮助

- 查看开发指南: `DEVELOPMENT_GUIDE.md`
- 查看代码规范: `CODE_STANDARDS.md`
- 联系开发团队: <EMAIL>

## 📈 持续改进

### 工具更新

- 定期更新开发依赖
- 关注新的最佳实践
- 收集团队反馈
- 优化开发体验

### 配置优化

- 根据项目需求调整规则
- 添加新的检查工具
- 优化CI/CD流程
- 提高自动化程度

---

**开发工具配置版本**: 1.0  
**最后更新**: 2025年7月17日  
**维护团队**: AIGC Service Hub 开发团队
