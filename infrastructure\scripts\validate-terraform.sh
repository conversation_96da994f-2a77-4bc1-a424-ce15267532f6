#!/bin/bash

# AIGC Service Hub Terraform配置验证脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查Terraform是否安装
check_terraform() {
    log_info "检查Terraform安装..."
    
    if ! command -v terraform &> /dev/null; then
        log_error "Terraform未安装，请先安装Terraform"
        return 1
    fi
    
    local version=$(terraform version -json | jq -r '.terraform_version' 2>/dev/null || terraform version | head -n1 | cut -d' ' -f2)
    log_success "Terraform已安装，版本: $version"
    return 0
}

# 检查配置文件
check_config_files() {
    log_info "检查配置文件..."
    
    local terraform_dir="infrastructure/terraform"
    local required_files=(
        "$terraform_dir/main.tf"
        "$terraform_dir/variables.tf"
        "$terraform_dir/outputs.tf"
    )
    
    local missing_files=()
    
    for file in "${required_files[@]}"; do
        if [ ! -f "$file" ]; then
            missing_files+=("$file")
        fi
    done
    
    if [ ${#missing_files[@]} -gt 0 ]; then
        log_error "缺少必要的配置文件:"
        for file in "${missing_files[@]}"; do
            echo "  - $file"
        done
        return 1
    fi
    
    log_success "所有必要的配置文件都存在"
    return 0
}

# 检查模块文件
check_modules() {
    log_info "检查Terraform模块..."
    
    local modules_dir="infrastructure/terraform/modules"
    local required_modules=(
        "vpc"
        "s3"
        "cloudfront"
    )
    
    local missing_modules=()
    
    for module in "${required_modules[@]}"; do
        if [ ! -d "$modules_dir/$module" ]; then
            missing_modules+=("$module")
        elif [ ! -f "$modules_dir/$module/main.tf" ]; then
            missing_modules+=("$module (main.tf missing)")
        fi
    done
    
    if [ ${#missing_modules[@]} -gt 0 ]; then
        log_error "缺少必要的模块:"
        for module in "${missing_modules[@]}"; do
            echo "  - $module"
        done
        return 1
    fi
    
    log_success "所有必要的模块都存在"
    return 0
}

# 验证Terraform语法
validate_syntax() {
    log_info "验证Terraform语法..."
    
    cd infrastructure/terraform
    
    # 初始化（仅下载provider，不配置backend）
    if ! terraform init -backend=false &>/dev/null; then
        log_error "Terraform初始化失败"
        return 1
    fi
    
    # 验证语法
    if ! terraform validate; then
        log_error "Terraform配置验证失败"
        return 1
    fi
    
    log_success "Terraform配置语法正确"
    return 0
}

# 检查变量文件
check_variables() {
    log_info "检查变量配置..."
    
    local tfvars_example="infrastructure/terraform/terraform.tfvars.example"
    local tfvars_file="infrastructure/terraform/terraform.tfvars"
    
    if [ ! -f "$tfvars_example" ]; then
        log_error "变量示例文件不存在: $tfvars_example"
        return 1
    fi
    
    if [ ! -f "$tfvars_file" ]; then
        log_warning "变量文件不存在: $tfvars_file"
        log_info "请从示例文件复制: cp $tfvars_example $tfvars_file"
        return 1
    fi
    
    log_success "变量文件配置正确"
    return 0
}

# 检查脚本文件
check_scripts() {
    log_info "检查部署脚本..."
    
    local scripts=(
        "infrastructure/scripts/deploy-aws.sh"
    )
    
    for script in "${scripts[@]}"; do
        if [ ! -f "$script" ]; then
            log_error "脚本文件不存在: $script"
            return 1
        fi
        
        if [ ! -x "$script" ]; then
            log_warning "脚本文件没有执行权限: $script"
            chmod +x "$script"
            log_info "已添加执行权限"
        fi
    done
    
    log_success "部署脚本检查完成"
    return 0
}

# 检查文档
check_documentation() {
    log_info "检查文档..."
    
    local docs=(
        "infrastructure/AWS_SETUP_GUIDE.md"
        "infrastructure/AWS_COST_ESTIMATION.md"
    )
    
    for doc in "${docs[@]}"; do
        if [ ! -f "$doc" ]; then
            log_warning "文档文件不存在: $doc"
        fi
    done
    
    log_success "文档检查完成"
    return 0
}

# 生成验证报告
generate_report() {
    log_info "生成验证报告..."
    
    local report_file="infrastructure/validation-report.txt"
    
    cat > "$report_file" << EOF
AIGC Service Hub AWS基础设施验证报告
生成时间: $(date)

配置文件检查: ✓
模块文件检查: ✓
语法验证: ✓
变量配置: ✓
脚本文件: ✓
文档文件: ✓

建议的下一步操作:
1. 配置AWS凭证: aws configure
2. 复制并编辑变量文件: cp terraform.tfvars.example terraform.tfvars
3. 初始化基础设施: ./scripts/deploy-aws.sh init
4. 规划部署: ./scripts/deploy-aws.sh plan
5. 应用配置: ./scripts/deploy-aws.sh apply

注意事项:
- 确保AWS账户有足够的权限
- 检查成本估算文档
- 在生产环境部署前进行充分测试
EOF
    
    log_success "验证报告已生成: $report_file"
}

# 主函数
main() {
    log_info "开始AWS基础设施配置验证..."
    echo
    
    local checks_passed=0
    local total_checks=6
    
    # 运行所有检查
    if check_terraform; then ((checks_passed++)); fi
    echo
    
    if check_config_files; then ((checks_passed++)); fi
    echo
    
    if check_modules; then ((checks_passed++)); fi
    echo
    
    if validate_syntax; then ((checks_passed++)); fi
    echo
    
    if check_variables; then ((checks_passed++)); fi
    echo
    
    if check_scripts; then ((checks_passed++)); fi
    echo
    
    check_documentation
    echo
    
    # 输出结果
    log_info "验证完成！"
    echo "通过检查: $checks_passed/$total_checks"
    
    if [ "$checks_passed" -eq "$total_checks" ]; then
        log_success "所有检查通过！AWS基础设施配置就绪。"
        generate_report
        return 0
    else
        log_warning "部分检查未通过，请修复问题后重新验证。"
        return 1
    fi
}

# 运行验证
main "$@"
