# AIGC Service Hub

> 全球领先的AI创作者服务平台 - MVP版本

[![Version](https://img.shields.io/badge/version-1.0.0--MVP-blue.svg)](https://github.com/aigc-service-hub/platform)
[![License](https://img.shields.io/badge/license-MIT-green.svg)](LICENSE)
[![Node.js](https://img.shields.io/badge/node.js-18.x-brightgreen.svg)](https://nodejs.org/)
[![React](https://img.shields.io/badge/react-18.x-blue.svg)](https://reactjs.org/)

## 📖 项目概述

AIGC Service
Hub 是一个专为AI创作者打造的资源交易与共享平台，致力于推动微调模型、工作流等AI资源的流通与交流，促进全球AI生态的繁荣发展。

### 🚀 核心功能特性

- **用户管理**: 支持个人/企业创作者注册，完整的用户认证体系
- **资源交易**: 支持AI模型、LoRA、工作流、提示词、工具等资源的上传与交易
- **支付系统**: 集成PayPal支付网关，支持美元和积分双重支付方式
- **财务管理**: 智能阶梯分佣系统，7天冻结期，自动化提现
- **大文件处理**: 支持最大30GB文件的分片上传和断点续传
- **游戏化系统**: 积分奖励、创作者排名、签到系统
- **后台管理**: 完整的管理员后台，支持用户、资源、交易管理

### 🛠 技术栈

**前端技术栈**

- React 18.x + TypeScript
- Vite (构建工具)
- Material-UI (UI组件库)
- React Router (路由管理)
- Axios (HTTP客户端)

**后端技术栈**

- Node.js 18.x + TypeScript
- Express.js (Web框架)
- PostgreSQL (主数据库)
- JWT (身份认证)
- PayPal SDK (支付集成)

**云服务与部署**

- AWS S3 (文件存储)
- AWS CloudFront (CDN)
- AWS EC2 (应用部署)
- Docker (容器化)
- GitHub Actions (CI/CD)

## 📁 项目目录结构

```
aigc-service-hub/
├── frontend/                 # 前端应用
│   ├── src/
│   │   ├── components/       # 可复用组件
│   │   ├── pages/           # 页面组件
│   │   ├── hooks/           # 自定义Hooks
│   │   ├── services/        # API服务
│   │   ├── utils/           # 工具函数
│   │   └── types/           # TypeScript类型定义
│   ├── public/              # 静态资源
│   └── package.json
├── backend/                  # 后端应用
│   ├── src/
│   │   ├── controllers/     # 控制器
│   │   ├── models/          # 数据模型
│   │   ├── routes/          # 路由定义
│   │   ├── middleware/      # 中间件
│   │   ├── services/        # 业务逻辑
│   │   └── utils/           # 工具函数
│   ├── migrations/          # 数据库迁移
│   └── package.json
├── docker/                   # Docker配置
│   ├── Dockerfile.frontend
│   ├── Dockerfile.backend
│   └── docker-compose.yml
├── docs/                     # 项目文档
├── assets/                   # 品牌素材库
│   ├── logos/               # LOGO文件
│   ├── colors/              # 色彩规范
│   ├── fonts/               # 字体文件
│   └── images/              # 图片素材
├── scripts/                  # 部署脚本
├── .env.example             # 环境变量模板
├── .gitignore
└── README.md
```

## ⚙️ 环境配置

### 系统要求

- Node.js >= 18.0.0
- npm >= 8.0.0 或 yarn >= 1.22.0
- PostgreSQL >= 14.0
- Docker >= 20.10.0 (可选)
- AWS CLI (生产环境部署)

### 本地开发环境设置

1. **克隆项目**

```bash
git clone https://github.com/aigc-service-hub/platform.git
cd aigc-service-hub
```

2. **安装依赖**

```bash
# 安装前端依赖
cd frontend
npm install

# 安装后端依赖
cd ../backend
npm install
```

3. **环境变量配置**

```bash
# 复制环境变量模板
cp .env.example .env

# 编辑环境变量文件
nano .env
```

### 环境变量配置说明

创建 `.env` 文件并配置以下变量：

```env
# 数据库配置
DATABASE_URL=postgresql://username:password@localhost:5432/aigc_service_hub
DB_HOST=localhost
DB_PORT=5432
DB_NAME=aigc_service_hub
DB_USER=your_username
DB_PASSWORD=your_password

# JWT配置
JWT_SECRET=your_super_secret_jwt_key
JWT_EXPIRES_IN=7d

# PayPal配置
PAYPAL_CLIENT_ID=your_paypal_client_id
PAYPAL_CLIENT_SECRET=your_paypal_client_secret
PAYPAL_MODE=sandbox  # 开发环境使用sandbox，生产环境使用live

# AWS配置
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key
AWS_REGION=us-west-2
AWS_S3_BUCKET=aigc-service-hub-files
AWS_CLOUDFRONT_DOMAIN=your_cloudfront_domain

# 邮件服务配置
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your_app_password

# 应用配置
NODE_ENV=development
PORT=3000
FRONTEND_URL=http://localhost:5173
BACKEND_URL=http://localhost:3000
```

### 数据库初始化

```bash
# 创建数据库
createdb aigc_service_hub

# 运行数据库迁移
cd backend
npm run migrate

# 插入初始数据（可选）
npm run seed
```

## 🚀 启动命令

### 本地开发环境

**方式一：分别启动前后端**

```bash
# 启动后端服务 (终端1)
cd backend
npm run dev
# 后端服务运行在 http://localhost:3000

# 启动前端服务 (终端2)
cd frontend
npm run dev
# 前端服务运行在 http://localhost:5173
```

**方式二：使用Docker Compose**

```bash
# 构建并启动所有服务
docker-compose up --build

# 后台运行
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f backend
docker-compose logs -f frontend

# 停止服务
docker-compose down

# 清理所有容器和数据
docker-compose down -v --remove-orphans
```

### Docker容器化详细配置

**Docker Compose配置文件**

```yaml
# docker-compose.yml
version: '3.8'

services:
  # PostgreSQL数据库
  postgres:
    image: postgres:14
    environment:
      POSTGRES_DB: aigc_service_hub
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backend/migrations:/docker-entrypoint-initdb.d
    ports:
      - '5432:5432'
    networks:
      - app-network

  # Redis缓存
  redis:
    image: redis:7-alpine
    ports:
      - '6379:6379'
    networks:
      - app-network

  # 后端服务
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    environment:
      - NODE_ENV=production
      - DATABASE_URL=********************************************/aigc_service_hub
      - REDIS_URL=redis://redis:6379
    ports:
      - '3000:3000'
    depends_on:
      - postgres
      - redis
    volumes:
      - ./backend:/app
      - /app/node_modules
    networks:
      - app-network

  # 前端服务
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    ports:
      - '80:80'
    depends_on:
      - backend
    networks:
      - app-network

volumes:
  postgres_data:

networks:
  app-network:
    driver: bridge
```

**Dockerfile配置**

```dockerfile
# backend/Dockerfile
FROM node:18-alpine AS builder

WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

FROM node:18-alpine AS runtime

WORKDIR /app
COPY --from=builder /app/node_modules ./node_modules
COPY . .

RUN npm run build

EXPOSE 3000

CMD ["npm", "start"]
```

```dockerfile
# frontend/Dockerfile
FROM node:18-alpine AS builder

WORKDIR /app
COPY package*.json ./
RUN npm ci

COPY . .
RUN npm run build

FROM nginx:alpine AS runtime

COPY --from=builder /app/dist /usr/share/nginx/html
COPY nginx.conf /etc/nginx/nginx.conf

EXPOSE 80

CMD ["nginx", "-g", "daemon off;"]
```

### 测试命令

```bash
# 运行单元测试
npm run test

# 运行集成测试
npm run test:integration

# 运行E2E测试
npm run test:e2e

# 生成测试覆盖率报告
npm run test:coverage
```

### AWS服务配置指南

**1. S3存储桶配置**

```bash
# 创建S3存储桶
aws s3 mb s3://aigc-service-hub-files --region us-west-2

# 配置存储桶策略
aws s3api put-bucket-policy --bucket aigc-service-hub-files --policy file://s3-policy.json

# 启用版本控制
aws s3api put-bucket-versioning \
  --bucket aigc-service-hub-files \
  --versioning-configuration Status=Enabled

# 配置CORS
aws s3api put-bucket-cors \
  --bucket aigc-service-hub-files \
  --cors-configuration file://s3-cors.json
```

```json
// s3-policy.json
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Sid": "PublicReadGetObject",
      "Effect": "Allow",
      "Principal": "*",
      "Action": "s3:GetObject",
      "Resource": "arn:aws:s3:::aigc-service-hub-files/public/*"
    },
    {
      "Sid": "AuthenticatedUpload",
      "Effect": "Allow",
      "Principal": {
        "AWS": "arn:aws:iam::ACCOUNT-ID:role/AIGC-Service-Hub-Role"
      },
      "Action": ["s3:PutObject", "s3:PutObjectAcl", "s3:DeleteObject"],
      "Resource": "arn:aws:s3:::aigc-service-hub-files/*"
    }
  ]
}
```

```json
// s3-cors.json
{
  "CORSRules": [
    {
      "AllowedOrigins": ["https://aigc-service-hub.com", "http://localhost:5173"],
      "AllowedHeaders": ["*"],
      "AllowedMethods": ["GET", "PUT", "POST", "DELETE", "HEAD"],
      "MaxAgeSeconds": 3000,
      "ExposeHeaders": ["ETag"]
    }
  ]
}
```

**2. CloudFront CDN配置**

```bash
# 创建CloudFront分发
aws cloudfront create-distribution --distribution-config file://cloudfront-config.json

# 创建缓存失效
aws cloudfront create-invalidation \
  --distribution-id E1234567890123 \
  --paths "/*"
```

**3. EC2实例配置**

```bash
# 创建密钥对
aws ec2 create-key-pair \
  --key-name aigc-service-hub-key \
  --query 'KeyMaterial' \
  --output text > aigc-service-hub-key.pem

chmod 400 aigc-service-hub-key.pem

# 创建安全组
aws ec2 create-security-group \
  --group-name aigc-service-hub-sg \
  --description "Security group for AIGC Service Hub"

# 配置安全组规则
aws ec2 authorize-security-group-ingress \
  --group-id sg-12345678 \
  --protocol tcp \
  --port 80 \
  --cidr 0.0.0.0/0

aws ec2 authorize-security-group-ingress \
  --group-id sg-12345678 \
  --protocol tcp \
  --port 443 \
  --cidr 0.0.0.0/0

aws ec2 authorize-security-group-ingress \
  --group-id sg-12345678 \
  --protocol tcp \
  --port 22 \
  --cidr YOUR-IP/32

# 启动EC2实例
aws ec2 run-instances \
  --image-id ami-0c02fb55956c7d316 \
  --instance-type t3.medium \
  --key-name aigc-service-hub-key \
  --security-group-ids sg-12345678 \
  --subnet-id subnet-12345678 \
  --user-data file://user-data.sh
```

### 生产环境部署

```bash
# 构建前端应用
cd frontend
npm run build

# 构建后端应用
cd ../backend
npm run build

# 使用Docker构建生产镜像
docker build -t aigc-service-hub-frontend:latest ./frontend
docker build -t aigc-service-hub-backend:latest ./backend

# 推送到ECR (可选)
aws ecr get-login-password --region us-west-2 | docker login --username AWS --password-stdin 123456789012.dkr.ecr.us-west-2.amazonaws.com

docker tag aigc-service-hub-frontend:latest 123456789012.dkr.ecr.us-west-2.amazonaws.com/aigc-service-hub-frontend:latest
docker push 123456789012.dkr.ecr.us-west-2.amazonaws.com/aigc-service-hub-frontend:latest

# 部署到EC2
scp -i aigc-service-hub-key.pem docker-compose.prod.yml ec2-user@your-ec2-ip:~/
ssh -i aigc-service-hub-key.pem ec2-user@your-ec2-ip "cd ~ && docker-compose -f docker-compose.prod.yml up -d"
```

## 🏗 架构决策

### 系统整体架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   React Frontend │    │  Express Backend │    │   PostgreSQL    │
│   (Material-UI)  │◄──►│   (TypeScript)   │◄──►│    Database     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│  CloudFront CDN │    │   PayPal API    │    │    AWS S3       │
│   (全球加速)     │    │   (支付处理)     │    │   (文件存储)     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 关键技术选型理由

**Material-UI**:

- 提供丰富的React组件库，加速UI开发
- 支持主题定制，保证设计一致性
- 良好的TypeScript支持

**PayPal SDK**:

- 全球领先的支付解决方案
- 支持多种货币和支付方式
- 完善的安全保障和合规性

**PostgreSQL**:

- 强大的关系型数据库，支持复杂查询
- 优秀的并发性能和数据一致性
- 丰富的数据类型支持

**AWS服务**:

- S3提供可靠的文件存储服务
- CloudFront确保全球访问速度
- EC2提供弹性计算资源

### 数据库设计核心表

```sql
-- 用户表
CREATE TABLE users (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  email VARCHAR(255) UNIQUE NOT NULL,
  password_hash VARCHAR(255) NOT NULL,
  user_type VARCHAR(20) NOT NULL, -- 'individual' | 'enterprise'
  company_name VARCHAR(255),
  paypal_email VARCHAR(255),
  is_verified BOOLEAN DEFAULT FALSE,
  total_earnings DECIMAL(12,2) DEFAULT 0,
  available_balance DECIMAL(12,2) DEFAULT 0,
  frozen_balance DECIMAL(12,2) DEFAULT 0,
  points_balance INTEGER DEFAULT 0,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- 资源表
CREATE TABLE resources (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  creator_id UUID REFERENCES users(id),
  title VARCHAR(255) NOT NULL,
  description TEXT,
  category VARCHAR(50) NOT NULL, -- 'fine_tuned_models' | 'lora' | 'workflows' | 'prompts' | 'tools'
  tags TEXT[], -- 用途标签数组
  price_usd DECIMAL(10,2),
  price_points INTEGER,
  file_url VARCHAR(500),
  file_size BIGINT, -- 文件大小（字节）
  cover_image_url VARCHAR(500),
  download_count INTEGER DEFAULT 0,
  sales_count INTEGER DEFAULT 0,
  status VARCHAR(20) DEFAULT 'active', -- 'active' | 'inactive' | 'pending_review'
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- 交易表
CREATE TABLE transactions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  buyer_id UUID REFERENCES users(id),
  seller_id UUID REFERENCES users(id),
  resource_id UUID REFERENCES resources(id),
  amount_usd DECIMAL(10,2),
  platform_fee DECIMAL(10,2),
  creator_earnings DECIMAL(10,2),
  payment_method VARCHAR(20), -- 'paypal' | 'points'
  paypal_transaction_id VARCHAR(255),
  status VARCHAR(20) DEFAULT 'pending', -- 'pending' | 'completed' | 'failed' | 'refunded'
  download_url VARCHAR(500),
  download_expires_at TIMESTAMP,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- 分佣记录表
CREATE TABLE commission_records (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  transaction_id UUID REFERENCES transactions(id),
  creator_id UUID REFERENCES users(id),
  resource_id UUID REFERENCES resources(id),
  sales_sequence INTEGER, -- 该资源的第几次销售
  commission_rate DECIMAL(5,4), -- 分佣比例 (0.0500 = 5%)
  platform_fee DECIMAL(10,2),
  creator_earnings DECIMAL(10,2),
  created_at TIMESTAMP DEFAULT NOW()
);

-- 提现记录表
CREATE TABLE withdrawal_requests (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id),
  amount DECIMAL(10,2) NOT NULL,
  paypal_fee DECIMAL(10,2),
  tax_fee DECIMAL(10,2),
  net_amount DECIMAL(10,2),
  paypal_email VARCHAR(255) NOT NULL,
  status VARCHAR(20) DEFAULT 'pending', -- 'pending' | 'processing' | 'completed' | 'failed'
  paypal_payout_id VARCHAR(255),
  processed_at TIMESTAMP,
  created_at TIMESTAMP DEFAULT NOW()
);

-- 积分记录表
CREATE TABLE points_transactions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id),
  points INTEGER NOT NULL,
  type VARCHAR(20) NOT NULL, -- 'earned' | 'spent' | 'expired'
  source VARCHAR(50), -- 'daily_checkin' | 'first_upload' | 'sales_milestone' | 'purchase'
  description TEXT,
  created_at TIMESTAMP DEFAULT NOW()
);

-- 退款申请表
CREATE TABLE refund_requests (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  transaction_id UUID REFERENCES transactions(id),
  requester_id UUID REFERENCES users(id),
  reason TEXT NOT NULL,
  admin_notes TEXT,
  status VARCHAR(20) DEFAULT 'pending', -- 'pending' | 'approved' | 'rejected'
  processed_by UUID REFERENCES users(id),
  processed_at TIMESTAMP,
  created_at TIMESTAMP DEFAULT NOW()
);
```

### API接口设计规范

**RESTful API设计原则**

```javascript
// API路由结构示例
const apiRoutes = {
  // 用户相关
  'POST /api/auth/register': '用户注册',
  'POST /api/auth/login': '用户登录',
  'POST /api/auth/logout': '用户登出',
  'GET /api/auth/profile': '获取用户信息',
  'PUT /api/auth/profile': '更新用户信息',

  // 资源相关
  'GET /api/resources': '获取资源列表',
  'GET /api/resources/:id': '获取资源详情',
  'POST /api/resources': '创建资源',
  'PUT /api/resources/:id': '更新资源',
  'DELETE /api/resources/:id': '删除资源',
  'POST /api/resources/:id/upload': '上传资源文件',

  // 交易相关
  'POST /api/transactions': '创建交易',
  'GET /api/transactions/:id': '获取交易详情',
  'POST /api/transactions/:id/confirm': '确认支付',
  'GET /api/transactions/:id/download': '获取下载链接',

  // 财务相关
  'GET /api/finance/balance': '获取账户余额',
  'POST /api/finance/withdraw': '申请提现',
  'GET /api/finance/transactions': '获取财务记录',

  // 积分相关
  'GET /api/points/balance': '获取积分余额',
  'POST /api/points/checkin': '每日签到',
  'GET /api/points/history': '积分历史记录',
};
```

**API响应格式标准**

```javascript
// 成功响应格式
{
  "success": true,
  "data": {
    // 实际数据
  },
  "message": "操作成功",
  "timestamp": "2025-07-17T10:30:00Z"
}

// 错误响应格式
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "输入数据验证失败",
    "details": [
      {
        "field": "email",
        "message": "邮箱格式不正确"
      }
    ]
  },
  "timestamp": "2025-07-17T10:30:00Z"
}

// 分页响应格式
{
  "success": true,
  "data": {
    "items": [...],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 100,
      "totalPages": 5,
      "hasNext": true,
      "hasPrev": false
    }
  }
}
```

### 安全策略说明

**1. 身份认证与授权**

```javascript
// JWT Token验证中间件
const authenticateToken = (req, res, next) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    return res.status(401).json({
      success: false,
      error: { message: '访问令牌缺失' },
    });
  }

  jwt.verify(token, process.env.JWT_SECRET, (err, user) => {
    if (err) {
      return res.status(403).json({
        success: false,
        error: { message: '访问令牌无效' },
      });
    }
    req.user = user;
    next();
  });
};

// 角色权限验证
const requireRole = roles => {
  return (req, res, next) => {
    if (!roles.includes(req.user.role)) {
      return res.status(403).json({
        success: false,
        error: { message: '权限不足' },
      });
    }
    next();
  };
};
```

**2. 数据验证与清理**

```javascript
// 使用Joi进行数据验证
const Joi = require('joi');

const userRegistrationSchema = Joi.object({
  email: Joi.string().email().required(),
  password: Joi.string()
    .min(8)
    .pattern(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
    .required(),
  userType: Joi.string().valid('individual', 'enterprise').required(),
  companyName: Joi.when('userType', {
    is: 'enterprise',
    then: Joi.string().required(),
    otherwise: Joi.string().optional(),
  }),
});

// 输入清理中间件
const sanitizeInput = (req, res, next) => {
  // 清理HTML标签和特殊字符
  const sanitizeHtml = require('sanitize-html');

  const sanitizeObject = obj => {
    for (let key in obj) {
      if (typeof obj[key] === 'string') {
        obj[key] = sanitizeHtml(obj[key], {
          allowedTags: [],
          allowedAttributes: {},
        });
      } else if (typeof obj[key] === 'object') {
        sanitizeObject(obj[key]);
      }
    }
  };

  sanitizeObject(req.body);
  next();
};
```

**3. 文件上传安全**

```javascript
// 文件类型验证
const allowedMimeTypes = [
  'application/zip',
  'application/x-zip-compressed',
  'application/octet-stream',
  'text/plain',
  'application/json',
];

const fileUploadSecurity = {
  // 文件大小限制 (30GB)
  maxFileSize: 30 * 1024 * 1024 * 1024,

  // 文件类型验证
  validateFileType: file => {
    return allowedMimeTypes.includes(file.mimetype);
  },

  // 文件名清理
  sanitizeFileName: filename => {
    return filename.replace(/[^a-zA-Z0-9.-]/g, '_');
  },

  // 病毒扫描（集成第三方服务）
  scanForMalware: async filePath => {
    // 集成ClamAV或其他病毒扫描服务
    return true; // 返回扫描结果
  },
};
```

**4. 支付安全**

```javascript
// PayPal Webhook验证
const verifyPayPalWebhook = (req, res, next) => {
  const webhookId = process.env.PAYPAL_WEBHOOK_ID;
  const headers = req.headers;
  const body = req.body;

  // 验证PayPal签名
  const isValid = paypal.notification.webhookEvent.verify(headers, body, webhookId);

  if (!isValid) {
    return res.status(400).json({
      success: false,
      error: { message: 'Webhook验证失败' },
    });
  }

  next();
};

// 防重复支付
const preventDuplicatePayment = async (req, res, next) => {
  const { resourceId, userId } = req.body;
  const recentTransaction = await Transaction.findOne({
    buyer_id: userId,
    resource_id: resourceId,
    created_at: { $gte: new Date(Date.now() - 5 * 60 * 1000) }, // 5分钟内
    status: { $in: ['pending', 'completed'] },
  });

  if (recentTransaction) {
    return res.status(409).json({
      success: false,
      error: { message: '请勿重复提交支付请求' },
    });
  }

  next();
};
```

## 🔄 开发流程

### Git工作流规范

```bash
# 功能开发流程
git checkout -b feature/user-authentication
git add .
git commit -m "feat: implement user registration and login"
git push origin feature/user-authentication
# 创建Pull Request进行代码审查
```

### 代码提交规范

使用Conventional Commits规范：

- `feat`: 新功能
- `fix`: 修复bug
- `docs`: 文档更新
- `style`: 代码格式调整
- `refactor`: 代码重构
- `test`: 测试相关
- `chore`: 构建过程或辅助工具的变动

### CI/CD流程

```yaml
# .github/workflows/deploy.yml
name: Deploy to AWS
on:
  push:
    branches: [main]
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Run tests
        run: npm test
  deploy:
    needs: test
    runs-on: ubuntu-latest
    steps:
      - name: Deploy to AWS
        run: npm run deploy:aws
```

## 🔧 故障排除

### 常见问题及解决方案

**问题1: 数据库连接失败**

```bash
# 检查PostgreSQL服务状态
sudo systemctl status postgresql

# 重启PostgreSQL服务
sudo systemctl restart postgresql

# 检查数据库连接配置
psql -h localhost -U your_username -d aigc_service_hub
```

**问题2: PayPal支付测试失败**

- 确认使用沙盒环境的测试账户
- 检查PayPal SDK配置是否正确
- 验证网络连接和API密钥
- 查看PayPal开发者控制台的日志

**问题3: 大文件上传失败**

- 检查AWS S3权限配置和CORS设置
- 确认文件大小限制设置（默认30GB）
- 验证分片上传配置和断点续传逻辑
- 检查网络稳定性和超时设置

**问题4: 前端构建失败**

```bash
# 清除node_modules和重新安装
rm -rf node_modules package-lock.json
npm install

# 检查TypeScript类型错误
npm run type-check

# 清除Vite缓存
npm run dev -- --force
```

**问题5: Docker容器启动失败**

```bash
# 查看容器日志
docker-compose logs backend
docker-compose logs frontend

# 重新构建镜像
docker-compose build --no-cache

# 检查端口占用
netstat -tulpn | grep :3000
```

### 调试技巧和工具推荐

**前端调试**

- 使用React Developer Tools浏览器扩展
- 利用Vite的HMR进行快速开发
- 使用Chrome DevTools的Network面板监控API请求

**后端调试**

- 使用VS Code的Node.js调试器
- 配置Winston日志记录器
- 使用Postman或Insomnia测试API接口

**数据库调试**

- 使用pgAdmin或DBeaver进行数据库管理
- 启用PostgreSQL查询日志
- 使用EXPLAIN ANALYZE分析查询性能

### 性能优化建议

**1. 前端性能优化**

```javascript
// 代码分割示例
const LazyComponent = React.lazy(() => import('./LazyComponent'));

// 图片懒加载
const LazyImage = ({ src, alt }) => {
  const [isLoaded, setIsLoaded] = useState(false);
  const imgRef = useRef();

  useEffect(() => {
    const observer = new IntersectionObserver(([entry]) => {
      if (entry.isIntersecting) {
        setIsLoaded(true);
        observer.disconnect();
      }
    });
    if (imgRef.current) observer.observe(imgRef.current);
    return () => observer.disconnect();
  }, []);

  return <img ref={imgRef} src={isLoaded ? src : ''} alt={alt} />;
};
```

**2. 后端性能优化**

```javascript
// Redis缓存示例
const redis = require('redis');
const client = redis.createClient();

const cacheMiddleware = duration => {
  return async (req, res, next) => {
    const key = req.originalUrl;
    const cached = await client.get(key);

    if (cached) {
      return res.json(JSON.parse(cached));
    }

    res.sendResponse = res.json;
    res.json = body => {
      client.setex(key, duration, JSON.stringify(body));
      res.sendResponse(body);
    };

    next();
  };
};
```

**3. 数据库性能优化**

```sql
-- 创建索引优化查询
CREATE INDEX idx_resources_category ON resources(category);
CREATE INDEX idx_transactions_created_at ON transactions(created_at);
CREATE INDEX idx_users_email ON users(email);

-- 分页查询优化
SELECT * FROM resources
WHERE category = $1
ORDER BY created_at DESC
LIMIT $2 OFFSET $3;
```

**4. AWS服务优化**

```javascript
// S3上传优化配置
const uploadParams = {
  Bucket: process.env.AWS_S3_BUCKET,
  Key: fileName,
  Body: fileStream,
  ContentType: mimeType,
  ServerSideEncryption: 'AES256',
  StorageClass: 'STANDARD_IA', // 降低存储成本
  Metadata: {
    'uploaded-by': userId,
    'upload-date': new Date().toISOString(),
  },
};
```

### 监控和日志配置

**应用监控设置**

```javascript
// Winston日志配置
const winston = require('winston');

const logger = winston.createLogger({
  level: 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.json()
  ),
  transports: [
    new winston.transports.File({ filename: 'error.log', level: 'error' }),
    new winston.transports.File({ filename: 'combined.log' }),
    new winston.transports.Console({
      format: winston.format.simple(),
    }),
  ],
});
```

**性能监控指标**

- API响应时间监控
- 数据库查询性能监控
- 文件上传成功率监控
- 支付成功率监控
- 用户活跃度统计

## 📞 联系方式

- **项目负责人**: [项目经理姓名]
- **技术支持**: <EMAIL>
- **问题反馈**: [GitHub Issues](https://github.com/aigc-service-hub/platform/issues)

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

---

**开发团队**: 洛拉（天津）人工智能有限公司 **最后更新**: 2025年7月17日
