# AIGC Service Hub 图标设计规范

## 🎯 图标设计原则

AIGC Service Hub的图标系统遵循简洁、一致、直观的设计原则，确保在各种场景下都能提供清晰的视觉传达。

### 核心原则

- **简洁性**: 去除不必要的细节，保持图形简洁
- **一致性**: 统一的设计风格和视觉语言
- **可识别性**: 图标含义清晰，易于理解
- **可扩展性**: 在不同尺寸下都保持清晰

## 📐 设计规格

### 基础网格系统

```
基准尺寸：24x24px
网格单位：2px
描边宽度：2px
圆角半径：2px
内边距：2px
```

### 尺寸规范

```css
/* 图标尺寸变量 */
:root {
  --icon-xs: 16px; /* 极小图标 */
  --icon-sm: 20px; /* 小图标 */
  --icon-md: 24px; /* 标准图标 */
  --icon-lg: 32px; /* 大图标 */
  --icon-xl: 48px; /* 超大图标 */
}
```

### 设计约束

```
最小可视元素：2px
最小间距：2px
最大描边宽度：2px
推荐视觉重量：中等
```

## 🎨 视觉风格

### 线性图标风格

```
描边样式：
- 宽度：2px
- 端点：圆形端点
- 连接：圆形连接
- 颜色：单色或双色

填充样式：
- 背景：透明或单色填充
- 描边：保持2px宽度
- 对比：确保足够的对比度
```

### 图标类型

#### 1. 线性图标 (Outline)

```
特点：
- 仅使用描边，无填充
- 适用于大多数界面场景
- 视觉重量轻，不抢夺注意力

使用场景：
- 导航菜单
- 工具栏
- 表单元素
- 列表项
```

#### 2. 填充图标 (Filled)

```
特点：
- 实心填充设计
- 视觉重量较重
- 适用于强调和选中状态

使用场景：
- 选中状态
- 主要操作按钮
- 状态指示
- 重要功能
```

#### 3. 双色图标 (Duotone)

```
特点：
- 使用两种颜色
- 层次感更强
- 适用于特殊场景

使用场景：
- 功能分类
- 品牌展示
- 特殊强调
```

## 📚 图标分类

### 1. 功能图标

```
基础功能：
- 搜索 (search)
- 上传 (upload)
- 下载 (download)
- 删除 (delete)
- 编辑 (edit)
- 设置 (settings)
- 帮助 (help)
- 关闭 (close)

导航功能：
- 首页 (home)
- 返回 (back)
- 前进 (forward)
- 菜单 (menu)
- 更多 (more)
```

### 2. 资源类型图标

```
AI资源：
- 模型 (model)
- LoRA (lora)
- 工作流 (workflow)
- 提示词 (prompt)
- 工具 (tool)

文件类型：
- 图片 (image)
- 视频 (video)
- 音频 (audio)
- 文档 (document)
- 代码 (code)
```

### 3. 状态图标

```
操作状态：
- 成功 (success)
- 错误 (error)
- 警告 (warning)
- 信息 (info)
- 加载 (loading)

用户状态：
- 在线 (online)
- 离线 (offline)
- 忙碌 (busy)
- 验证 (verified)
```

### 4. 社交图标

```
互动功能：
- 点赞 (like)
- 收藏 (favorite)
- 分享 (share)
- 评论 (comment)
- 关注 (follow)

社交平台：
- GitHub
- Twitter
- Discord
- Telegram
```

## 🎨 颜色应用

### 单色图标

```css
/* 默认颜色 */
.icon-default {
  color: var(--gray-600);
}

/* 主要颜色 */
.icon-primary {
  color: var(--primary-600);
}

/* 成功状态 */
.icon-success {
  color: var(--success-600);
}

/* 警告状态 */
.icon-warning {
  color: var(--warning-600);
}

/* 错误状态 */
.icon-error {
  color: var(--error-600);
}
```

### 双色图标

```css
/* 主要+辅助色 */
.icon-duotone-primary {
  --icon-primary: var(--primary-600);
  --icon-secondary: var(--primary-200);
}

/* 品牌双色 */
.icon-duotone-brand {
  --icon-primary: var(--primary-600);
  --icon-secondary: var(--secondary-600);
}
```

## 📱 响应式图标

### 尺寸适配

```css
/* 基础响应式图标 */
.icon-responsive {
  width: var(--icon-md);
  height: var(--icon-md);
}

/* 移动端适配 */
@media (max-width: 768px) {
  .icon-responsive {
    width: var(--icon-lg);
    height: var(--icon-lg);
  }
}

/* 触摸友好尺寸 */
.icon-touch {
  width: var(--icon-xl);
  height: var(--icon-xl);
  padding: 12px;
  min-width: 44px;
  min-height: 44px;
}
```

## 🔧 技术实现

### SVG图标

```html
<!-- 内联SVG -->
<svg class="icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
</svg>

<!-- 图标组件 -->
<Icon name="plus" size="md" color="primary" />
```

### 图标字体

```css
/* 图标字体类 */
.icon-font {
  font-family: 'AIGC-Icons';
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
```

### React组件示例

```jsx
// Icon组件
const Icon = ({ name, size = 'md', color = 'default', className }) => {
  return (
    <svg
      className={`icon icon-${size} icon-${color} ${className}`}
      viewBox='0 0 24 24'
      fill='none'
      stroke='currentColor'
    >
      <use href={`#icon-${name}`} />
    </svg>
  );
};

// 使用示例
<Icon name='search' size='lg' color='primary' />;
```

## 📋 图标清单

### 必需图标 (第一优先级)

```
基础功能：
✓ search (搜索)
✓ upload (上传)
✓ download (下载)
✓ user (用户)
✓ settings (设置)
✓ menu (菜单)
✓ close (关闭)
✓ home (首页)

资源类型：
✓ model (AI模型)
✓ lora (LoRA)
✓ workflow (工作流)
✓ prompt (提示词)
✓ tool (工具)

状态指示：
✓ success (成功)
✓ error (错误)
✓ warning (警告)
✓ info (信息)
✓ loading (加载)
```

### 扩展图标 (第二优先级)

```
高级功能：
○ filter (筛选)
○ sort (排序)
○ export (导出)
○ import (导入)
○ copy (复制)
○ share (分享)
○ bookmark (书签)
○ heart (喜欢)

社交功能：
○ comment (评论)
○ like (点赞)
○ follow (关注)
○ message (消息)
```

## ✅ 设计检查清单

### 设计质量

- [ ] 图标是否遵循24px网格系统
- [ ] 描边宽度是否为2px
- [ ] 圆角是否为2px
- [ ] 视觉重量是否一致
- [ ] 图标含义是否清晰

### 技术质量

- [ ] SVG代码是否优化
- [ ] 路径是否简化
- [ ] 文件大小是否合理
- [ ] 是否支持currentColor
- [ ] 是否有合适的viewBox

### 可用性

- [ ] 在最小尺寸下是否清晰
- [ ] 在不同背景上是否可见
- [ ] 是否符合无障碍标准
- [ ] 是否易于理解和识别

## 📦 图标资源

### 文件组织

```
icons/
├── svg/              # SVG源文件
│   ├── outline/      # 线性图标
│   ├── filled/       # 填充图标
│   └── duotone/      # 双色图标
├── sprite/           # SVG雪碧图
├── font/             # 图标字体
└── react/            # React组件
```

### 命名规范

```
文件命名：
- icon-[name]-[style].svg
- 例如：icon-search-outline.svg

组件命名：
- [Name]Icon
- 例如：SearchIcon, UploadIcon

CSS类命名：
- .icon-[name]
- 例如：.icon-search, .icon-upload
```

---

**图标规范版本**: 1.0  
**最后更新**: 2025年7月17日  
**维护团队**: AIGC Service Hub 设计团队
