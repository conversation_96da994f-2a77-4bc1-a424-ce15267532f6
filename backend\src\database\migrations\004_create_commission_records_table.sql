-- 创建分佣记录表
-- 记录阶梯分佣系统的详细计算过程

CREATE TABLE commission_records (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    transaction_id UUID NOT NULL REFERENCES transactions(id) ON DELETE CASCADE,
    creator_id UUID NOT NULL REFERENCES users(id) ON DELETE RESTRICT,
    resource_id UUID NOT NULL REFERENCES resources(id) ON DELETE RESTRICT,
    
    -- 分佣计算
    sales_sequence INTEGER NOT NULL CHECK (sales_sequence > 0),
    commission_rate DECIMAL(5,4) NOT NULL CHECK (commission_rate >= 0 AND commission_rate <= 1),
    base_amount DECIMAL(10,2) NOT NULL CHECK (base_amount >= 0),
    platform_fee DECIMAL(10,2) NOT NULL CHECK (platform_fee >= 0),
    creator_earnings DECIMAL(10,2) NOT NULL CHECK (creator_earnings >= 0),
    
    -- 创作者类型和费率信息
    creator_type VARCHAR(20) NOT NULL CHECK (creator_type IN ('individual', 'enterprise')),
    initial_rate DECIMAL(5,4) NOT NULL,
    final_rate DECIMAL(5,4) NOT NULL,
    decay_factor DECIMAL(5,4) NOT NULL,
    
    -- 时间戳
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- 约束
    CONSTRAINT valid_commission_calculation CHECK (
        base_amount = platform_fee + creator_earnings
    ),
    CONSTRAINT valid_rates CHECK (
        initial_rate >= final_rate AND
        initial_rate <= 1 AND
        final_rate >= 0 AND
        decay_factor >= 0 AND
        decay_factor <= 1
    )
);

-- 创建索引
CREATE INDEX idx_commission_records_transaction_id ON commission_records(transaction_id);
CREATE INDEX idx_commission_records_creator_id ON commission_records(creator_id);
CREATE INDEX idx_commission_records_resource_id ON commission_records(resource_id);
CREATE INDEX idx_commission_records_created_at ON commission_records(created_at);
CREATE INDEX idx_commission_records_creator_type ON commission_records(creator_type);
CREATE INDEX idx_commission_records_sales_sequence ON commission_records(sales_sequence);

-- 创建唯一约束，确保每个交易只有一条分佣记录
CREATE UNIQUE INDEX idx_commission_records_unique_transaction 
    ON commission_records(transaction_id);

-- 添加注释
COMMENT ON TABLE commission_records IS '分佣记录表，记录阶梯分佣计算详情';
COMMENT ON COLUMN commission_records.sales_sequence IS '该资源的第几次销售';
COMMENT ON COLUMN commission_records.commission_rate IS '实际分佣比例（0-1）';
COMMENT ON COLUMN commission_records.creator_type IS '创作者类型：individual 或 enterprise';
COMMENT ON COLUMN commission_records.initial_rate IS '初始分佣比例';
COMMENT ON COLUMN commission_records.final_rate IS '最终分佣比例';
COMMENT ON COLUMN commission_records.decay_factor IS '衰减因子';
