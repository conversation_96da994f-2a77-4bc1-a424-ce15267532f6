# AIGC Service Hub 代码规范

## 📋 概览

本文档定义了AIGC Service Hub项目的代码规范和质量标准，确保代码的一致性、可维护性和高质量。

## 🛠️ 工具配置

### 代码格式化 - Prettier

**配置文件**: `.prettierrc.json`

```json
{
  "semi": true,
  "trailingComma": "es5",
  "singleQuote": true,
  "printWidth": 80,
  "tabWidth": 2,
  "useTabs": false
}
```

**使用方法**:

```bash
# 格式化所有代码
npm run format

# 检查格式化状态
npm run format:check

# VSCode保存时自动格式化 (已配置)
```

### 代码质量检查 - ESLint

**前端配置**: `frontend/.eslintrc.json`

- TypeScript支持
- React规则
- 无障碍性检查
- 导入排序
- 未使用导入清理

**后端配置**: `backend/.eslintrc.json`

- TypeScript支持
- Node.js规则
- 安全检查
- 导入管理

**使用方法**:

```bash
# 检查代码质量
npm run lint

# 自动修复问题
npm run lint:fix

# 分别检查前后端
npm run lint:frontend
npm run lint:backend
```

### Git提交规范 - Commitlint

**配置文件**: `.commitlintrc.json`

**提交格式**:

```
<type>(<scope>): <subject>

<body>

<footer>
```

**提交类型**:

- `feat`: 新功能
- `fix`: 修复bug
- `docs`: 文档更新
- `style`: 代码格式化
- `refactor`: 代码重构
- `perf`: 性能优化
- `test`: 测试相关
- `build`: 构建相关
- `ci`: CI/CD相关
- `chore`: 其他杂项

**示例**:

```bash
feat(auth): add OAuth2 login support
fix(api): resolve user data validation error
docs: update API documentation for v2.0
```

### Git Hooks - Husky

**预提交检查** (`.husky/pre-commit`):

- 运行lint-staged
- TypeScript类型检查
- 代码格式化

**提交信息检查** (`.husky/commit-msg`):

- 验证提交信息格式
- 确保符合约定式提交规范

### 暂存文件检查 - lint-staged

**配置文件**: `.lintstagedrc.json`

```json
{
  "*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"],
  "*.{json,md,yml,yaml}": ["prettier --write"]
}
```

## 📝 编码规范

### 命名约定

**文件命名**:

```
# 组件文件 (PascalCase)
UserProfile.tsx
UserProfile.test.tsx
UserProfile.stories.tsx

# 工具函数 (camelCase)
apiClient.ts
dateUtils.ts
validationHelpers.ts

# 常量文件 (UPPER_SNAKE_CASE)
API_ENDPOINTS.ts
ERROR_MESSAGES.ts

# 页面文件 (kebab-case)
user-profile.page.tsx
settings.page.tsx
```

**变量命名**:

```typescript
// 变量和函数 (camelCase)
const userName = 'john';
const getUserData = () => {};

// 常量 (UPPER_SNAKE_CASE)
const API_BASE_URL = 'https://api.example.com';
const MAX_RETRY_COUNT = 3;

// 类和接口 (PascalCase)
class UserService {}
interface UserData {}

// 类型别名 (PascalCase)
type ApiResponse<T> = {
  data: T;
  status: number;
};
```

### TypeScript 规范

**类型定义**:

```typescript
// 优先使用 interface 定义对象类型
interface User {
  id: string;
  name: string;
  email: string;
  createdAt: Date;
}

// 使用 type 定义联合类型、函数类型等
type Status = 'pending' | 'approved' | 'rejected';
type EventHandler = (event: Event) => void;

// 使用泛型提高代码复用性
interface ApiResponse<T> {
  data: T;
  message: string;
  success: boolean;
}
```

**导入规范**:

```typescript
// 类型导入使用 type 关键字
import type { User, ApiResponse } from './types';
import { getUserData, updateUser } from './api';

// 导入顺序 (ESLint 自动排序)
// 1. Node.js 内置模块
// 2. 第三方库
// 3. 内部模块
// 4. 相对路径导入
```

### React 组件规范

**函数组件**:

```typescript
import type { FC } from 'react';

interface UserProfileProps {
  userId: string;
  onEdit?: (user: User) => void;
}

const UserProfile: FC<UserProfileProps> = ({ userId, onEdit }) => {
  // 组件逻辑
  return (
    <div>
      {/* JSX 内容 */}
    </div>
  );
};

export default UserProfile;
```

**Hooks使用**:

```typescript
// 自定义Hook命名以use开头
const useUserData = (userId: string) => {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);

  // Hook逻辑

  return { user, loading };
};
```

### API设计规范

**控制器**:

```typescript
@Controller('users')
export class UsersController {
  constructor(private readonly usersService: UsersService) {}

  @Get(':id')
  async getUser(@Param('id') id: string): Promise<ApiResponse<User>> {
    const user = await this.usersService.findById(id);
    return {
      data: user,
      message: 'User retrieved successfully',
      success: true,
    };
  }
}
```

**服务类**:

```typescript
@Injectable()
export class UsersService {
  constructor(
    @InjectRepository(User)
    private readonly userRepository: Repository<User>
  ) {}

  async findById(id: string): Promise<User> {
    const user = await this.userRepository.findOne({ where: { id } });
    if (!user) {
      throw new NotFoundException('User not found');
    }
    return user;
  }
}
```

## 🧪 测试规范

### 测试文件组织

```
src/
├── components/
│   ├── UserProfile/
│   │   ├── UserProfile.tsx
│   │   ├── UserProfile.test.tsx
│   │   └── UserProfile.stories.tsx
│   └── ...
├── utils/
│   ├── dateUtils.ts
│   ├── dateUtils.test.ts
│   └── ...
└── __tests__/
    ├── integration/
    ├── e2e/
    └── fixtures/
```

### 测试命名规范

```typescript
describe('UserProfile Component', () => {
  it('should render user name correctly', () => {
    // 测试实现
  });

  it('should handle loading state', () => {
    // 测试实现
  });

  it('should call onEdit when edit button is clicked', () => {
    // 测试实现
  });
});
```

### 测试覆盖率要求

- **单元测试**: >= 80%
- **集成测试**: 核心业务流程
- **E2E测试**: 关键用户路径

## 🔍 代码质量检查

### 自动化检查

**预提交检查**:

```bash
# 通过 husky + lint-staged 自动运行
# 1. ESLint 检查和修复
# 2. Prettier 格式化
# 3. TypeScript 类型检查
```

**手动检查**:

```bash
# 完整代码质量检查
./scripts/check-code-quality.sh    # Linux/macOS
./scripts/check-code-quality.ps1   # Windows

# 单独检查项目
npm run lint              # ESLint检查
npm run type-check        # TypeScript类型检查
npm run test              # 运行测试
npm run format:check      # 格式化检查
```

### VSCode 集成

**自动配置**:

- 保存时自动格式化
- 实时ESLint检查
- TypeScript错误提示
- 导入自动排序

**推荐扩展**:

- ESLint
- Prettier
- TypeScript
- GitLens
- Auto Rename Tag

## 📊 质量指标

### 代码质量指标

- **ESLint错误**: 0个
- **TypeScript错误**: 0个
- **测试覆盖率**: >= 80%
- **安全漏洞**: 0个高危漏洞

### 性能指标

- **构建时间**: < 5分钟
- **测试运行时间**: < 2分钟
- **代码检查时间**: < 1分钟

## 🚀 最佳实践

### 开发流程

1. 创建功能分支
2. 编写代码和测试
3. 运行代码质量检查
4. 提交代码 (自动检查)
5. 创建Pull Request
6. 代码审查
7. 合并到主分支

### 代码审查清单

- [ ] 代码符合项目规范
- [ ] 包含适当的测试
- [ ] 文档已更新
- [ ] 无安全漏洞
- [ ] 性能影响可接受
- [ ] 向后兼容性

### 持续改进

- 定期更新依赖
- 监控代码质量指标
- 收集团队反馈
- 优化开发工具配置
- 分享最佳实践

## 🔧 故障排除

### 常见问题

**ESLint错误**:

```bash
# 自动修复大部分问题
npm run lint:fix

# 查看具体错误
npm run lint
```

**Prettier格式化问题**:

```bash
# 格式化所有文件
npm run format

# 检查格式化状态
npm run format:check
```

**TypeScript类型错误**:

```bash
# 运行类型检查
npm run type-check

# 在VSCode中查看详细错误信息
```

**Git提交被拒绝**:

```bash
# 检查提交信息格式
# 运行预提交检查
npm run lint:fix
npm run format
```

### 获取帮助

- 查看开发指南: `DEVELOPMENT_GUIDE.md`
- 查看API文档: `docs/api.md`
- 联系开发团队: <EMAIL>

---

**代码规范版本**: 1.0  
**最后更新**: 2025年7月17日  
**维护团队**: AIGC Service Hub 开发团队
