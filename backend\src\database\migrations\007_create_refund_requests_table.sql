-- 创建退款申请表
-- 支持7天内退款申请和管理员审核

CREATE TABLE refund_requests (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    transaction_id UUID NOT NULL REFERENCES transactions(id) ON DELETE RESTRICT,
    requester_id UUID NOT NULL REFERENCES users(id) ON DELETE RESTRICT,
    
    -- 退款信息
    reason TEXT NOT NULL,
    refund_amount DECIMAL(10,2) NOT NULL CHECK (refund_amount > 0),
    refund_type VARCHAR(20) DEFAULT 'full' CHECK (refund_type IN ('full', 'partial')),
    
    -- 审核信息
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN (
        'pending',
        'under_review',
        'approved',
        'rejected',
        'processed'
    )),
    admin_notes TEXT,
    rejection_reason TEXT,
    
    -- 处理信息
    processed_by UUID REFERENCES users(id),
    paypal_refund_id VARCHAR(255),
    points_refunded INTEGER CHECK (points_refunded >= 0),
    
    -- 时间戳
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    reviewed_at TIMESTAMP,
    processed_at TIMESTAMP,
    
    -- 约束
    CONSTRAINT valid_refund_timeframe CHECK (
        created_at <= (
            SELECT created_at + INTERVAL '7 days' 
            FROM transactions 
            WHERE transactions.id = refund_requests.transaction_id
        )
    ),
    CONSTRAINT valid_requester CHECK (
        requester_id = (
            SELECT buyer_id 
            FROM transactions 
            WHERE transactions.id = refund_requests.transaction_id
        )
    )
);

-- 创建索引
CREATE INDEX idx_refund_requests_transaction_id ON refund_requests(transaction_id);
CREATE INDEX idx_refund_requests_requester_id ON refund_requests(requester_id);
CREATE INDEX idx_refund_requests_status ON refund_requests(status);
CREATE INDEX idx_refund_requests_created_at ON refund_requests(created_at);
CREATE INDEX idx_refund_requests_processed_by ON refund_requests(processed_by) 
    WHERE processed_by IS NOT NULL;

-- 创建唯一约束，确保每个交易只能申请一次退款
CREATE UNIQUE INDEX idx_refund_requests_unique_transaction 
    ON refund_requests(transaction_id);

-- 创建更新时间戳触发器
CREATE TRIGGER update_refund_requests_updated_at 
    BEFORE UPDATE ON refund_requests 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- 创建审核时间自动设置触发器
CREATE OR REPLACE FUNCTION set_refund_reviewed_at()
RETURNS TRIGGER AS $$
BEGIN
    -- 当状态从pending变为under_review时，设置审核时间
    IF OLD.status IN ('pending') AND NEW.status IN ('approved', 'rejected') AND NEW.reviewed_at IS NULL THEN
        NEW.reviewed_at = CURRENT_TIMESTAMP;
    END IF;
    
    -- 当状态变为processed时，设置处理时间
    IF OLD.status != 'processed' AND NEW.status = 'processed' AND NEW.processed_at IS NULL THEN
        NEW.processed_at = CURRENT_TIMESTAMP;
    END IF;
    
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER set_refund_requests_reviewed_at 
    BEFORE UPDATE ON refund_requests 
    FOR EACH ROW 
    EXECUTE FUNCTION set_refund_reviewed_at();

-- 创建视图：退款统计
CREATE VIEW refund_statistics AS
SELECT 
    DATE_TRUNC('month', created_at) as month,
    COUNT(*) as total_requests,
    COUNT(CASE WHEN status = 'approved' THEN 1 END) as approved_count,
    COUNT(CASE WHEN status = 'rejected' THEN 1 END) as rejected_count,
    COUNT(CASE WHEN status = 'processed' THEN 1 END) as processed_count,
    SUM(CASE WHEN status = 'processed' THEN refund_amount ELSE 0 END) as total_refunded_amount,
    AVG(CASE WHEN processed_at IS NOT NULL THEN 
        EXTRACT(EPOCH FROM (processed_at - created_at))/3600 
    END) as avg_processing_hours
FROM refund_requests 
GROUP BY DATE_TRUNC('month', created_at)
ORDER BY month DESC;

-- 添加注释
COMMENT ON TABLE refund_requests IS '退款申请表，支持7天内退款申请';
COMMENT ON COLUMN refund_requests.refund_type IS '退款类型：full(全额) 或 partial(部分)';
COMMENT ON COLUMN refund_requests.status IS '退款状态：pending, under_review, approved, rejected, processed';
COMMENT ON COLUMN refund_requests.paypal_refund_id IS 'PayPal退款交易ID';
COMMENT ON COLUMN refund_requests.points_refunded IS '退还的积分数量';

COMMENT ON VIEW refund_statistics IS '退款统计视图，按月统计退款情况';
