# AIGC Service Hub Terraform Variables Example
# Copy this file to terraform.tfvars and customize for your environment

# Project Configuration
project_name = "aigc-service-hub"
environment  = "prod"

# AWS Configuration
aws_region         = "us-west-2"
availability_zones = ["us-west-2a", "us-west-2b", "us-west-2c"]

# Network Configuration
vpc_cidr = "10.0.0.0/16"

# Database Configuration
db_name                   = "aigc_service_hub"
db_username              = "aigc_admin"
db_instance_class        = "db.t3.micro"
db_allocated_storage     = 20
db_max_allocated_storage = 100
backup_retention_period  = 7
backup_window           = "03:00-04:00"
maintenance_window      = "sun:04:00-sun:05:00"

# Redis Configuration
redis_node_type       = "cache.t3.micro"
redis_num_cache_nodes = 1

# ECS Configuration
ecs_cpu           = 512
ecs_memory        = 1024
ecs_desired_count = 2
ecs_min_capacity  = 1
ecs_max_capacity  = 10

# Domain Configuration (optional)
domain_name     = ""  # e.g., "aigc-service-hub.com"
certificate_arn = ""  # ACM certificate ARN for HTTPS

# Application Configuration
app_port           = 3000
health_check_path  = "/api/health"

# S3 Configuration
s3_force_destroy = false  # Set to true only for development environments

# Monitoring Configuration
enable_detailed_monitoring = true
log_retention_days         = 30

# Security Configuration
allowed_cidr_blocks = ["0.0.0.0/0"]  # Restrict this in production

# Auto Scaling Configuration
enable_auto_scaling    = true
scale_up_threshold     = 70
scale_down_threshold   = 30

# Cost Optimization
enable_spot_instances = false  # Set to true for development to save costs

# Environment-specific Configuration
environment_config = {
  # Production overrides
  db_instance_class    = "db.t3.small"
  redis_node_type     = "cache.t3.small"
  ecs_desired_count   = 3
  enable_auto_scaling = true
}

# Development Environment Example
# Uncomment and modify for development environment
# environment = "dev"
# environment_config = {
#   db_instance_class     = "db.t3.micro"
#   redis_node_type      = "cache.t3.micro"
#   ecs_desired_count    = 1
#   enable_auto_scaling  = false
# }
# s3_force_destroy = true
# enable_spot_instances = true
# log_retention_days = 7
