# AIGC Service Hub Terraform Outputs

# VPC Outputs
output "vpc_id" {
  description = "ID of the VPC"
  value       = module.vpc.vpc_id
}

output "vpc_cidr_block" {
  description = "CIDR block of the VPC"
  value       = module.vpc.vpc_cidr_block
}

output "public_subnet_ids" {
  description = "IDs of the public subnets"
  value       = module.vpc.public_subnet_ids
}

output "private_subnet_ids" {
  description = "IDs of the private subnets"
  value       = module.vpc.private_subnet_ids
}

# Load Balancer Outputs
output "alb_dns_name" {
  description = "DNS name of the load balancer"
  value       = module.alb.alb_dns_name
}

output "alb_zone_id" {
  description = "Zone ID of the load balancer"
  value       = module.alb.alb_zone_id
}

output "alb_arn" {
  description = "ARN of the load balancer"
  value       = module.alb.alb_arn
}

# Database Outputs
output "db_endpoint" {
  description = "RDS instance endpoint"
  value       = module.rds.db_endpoint
  sensitive   = true
}

output "db_port" {
  description = "RDS instance port"
  value       = module.rds.db_port
}

output "db_name" {
  description = "Database name"
  value       = var.db_name
}

output "db_username" {
  description = "Database username"
  value       = var.db_username
  sensitive   = true
}

# Redis Outputs
output "redis_endpoint" {
  description = "Redis cluster endpoint"
  value       = module.redis.redis_endpoint
  sensitive   = true
}

output "redis_port" {
  description = "Redis port"
  value       = module.redis.redis_port
}

# S3 Outputs
output "s3_files_bucket_name" {
  description = "Name of the S3 files bucket"
  value       = module.s3.files_bucket_name
}

output "s3_files_bucket_arn" {
  description = "ARN of the S3 files bucket"
  value       = module.s3.files_bucket_arn
}

output "s3_backups_bucket_name" {
  description = "Name of the S3 backups bucket"
  value       = module.s3.backups_bucket_name
}

output "s3_logs_bucket_name" {
  description = "Name of the S3 logs bucket"
  value       = module.s3.logs_bucket_name
}

# CloudFront Outputs
output "cloudfront_domain_name" {
  description = "CloudFront distribution domain name"
  value       = module.cloudfront.cloudfront_domain
}

output "cloudfront_distribution_id" {
  description = "CloudFront distribution ID"
  value       = module.cloudfront.cloudfront_distribution_id
}

output "cloudfront_zone_id" {
  description = "CloudFront distribution zone ID"
  value       = module.cloudfront.cloudfront_zone_id
}

# ECS Outputs
output "ecs_cluster_name" {
  description = "Name of the ECS cluster"
  value       = module.ecs.cluster_name
}

output "ecs_cluster_arn" {
  description = "ARN of the ECS cluster"
  value       = module.ecs.cluster_arn
}

output "ecs_service_name" {
  description = "Name of the ECS service"
  value       = module.ecs.service_name
}

# IAM Outputs
output "ecs_task_role_arn" {
  description = "ARN of the ECS task role"
  value       = module.iam.ecs_task_role_arn
}

output "ecs_execution_role_arn" {
  description = "ARN of the ECS execution role"
  value       = module.iam.ecs_execution_role_arn
}

# Route53 Outputs (if domain is configured)
output "domain_name" {
  description = "Domain name"
  value       = var.domain_name != "" ? var.domain_name : null
}

output "hosted_zone_id" {
  description = "Route53 hosted zone ID"
  value       = var.domain_name != "" ? module.route53[0].hosted_zone_id : null
}

# Application URLs
output "application_url" {
  description = "Application URL"
  value       = var.domain_name != "" ? "https://${var.domain_name}" : "http://${module.alb.alb_dns_name}"
}

output "api_url" {
  description = "API URL"
  value       = var.domain_name != "" ? "https://${var.domain_name}/api" : "http://${module.alb.alb_dns_name}/api"
}

# Security Group IDs
output "alb_security_group_id" {
  description = "Security group ID for ALB"
  value       = module.security_groups.alb_security_group_id
}

output "ecs_security_group_id" {
  description = "Security group ID for ECS"
  value       = module.security_groups.ecs_security_group_id
}

output "rds_security_group_id" {
  description = "Security group ID for RDS"
  value       = module.security_groups.rds_security_group_id
}

output "redis_security_group_id" {
  description = "Security group ID for Redis"
  value       = module.security_groups.redis_security_group_id
}

# Monitoring Outputs
output "cloudwatch_log_group_name" {
  description = "CloudWatch log group name"
  value       = module.monitoring.log_group_name
}

# Connection Information
output "connection_info" {
  description = "Connection information for the application"
  value = {
    application_url = var.domain_name != "" ? "https://${var.domain_name}" : "http://${module.alb.alb_dns_name}"
    api_url        = var.domain_name != "" ? "https://${var.domain_name}/api" : "http://${module.alb.alb_dns_name}/api"
    cdn_url        = "https://${module.cloudfront.cloudfront_domain}"
    region         = var.aws_region
  }
  sensitive = false
}

# Environment Configuration
output "environment_config" {
  description = "Environment configuration for application deployment"
  value = {
    AWS_REGION                = var.aws_region
    AWS_S3_BUCKET            = module.s3.files_bucket_name
    AWS_CLOUDFRONT_DOMAIN    = module.cloudfront.cloudfront_domain
    DATABASE_URL             = "postgresql://${var.db_username}:${random_password.db_password.result}@${module.rds.db_endpoint}:${module.rds.db_port}/${var.db_name}"
    REDIS_URL                = "redis://${module.redis.redis_endpoint}:${module.redis.redis_port}"
    LOG_GROUP_NAME           = module.monitoring.log_group_name
  }
  sensitive = true
}
