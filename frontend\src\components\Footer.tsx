import React from 'react';
import {
  Box,
  Container,
  Grid,
  Typography,
  Link,
  IconButton,
  Divider,
  Stack,
} from '@mui/material';
import {
  CloudUpload,
  Twitter,
  LinkedIn,
  GitHub,
  Email,
  Phone,
  LocationOn,
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';

const Footer: React.FC = () => {
  const navigate = useNavigate();

  const handleLinkClick = (path: string) => {
    navigate(path);
  };

  return (
    <Box
      component="footer"
      sx={{
        bgcolor: 'grey.900',
        color: 'white',
        py: 6,
        mt: 'auto',
      }}
    >
      <Container maxWidth="lg">
        <Grid container spacing={4}>
          {/* 品牌信息 */}
          <Grid item xs={12} md={4}>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
              <CloudUpload sx={{ mr: 1, fontSize: 32 }} />
              <Typography variant="h6" fontWeight="bold">
                AIGC Service Hub
              </Typography>
            </Box>
            
            <Typography variant="body2" sx={{ mb: 3, lineHeight: 1.6 }}>
              全球领先的AI创作者服务平台，为AI创作者提供资源交易、技术分享和商业变现的综合服务，
              推动全球AI生态的繁荣发展。
            </Typography>
            
            <Stack direction="row" spacing={1}>
              <IconButton
                color="inherit"
                href="https://twitter.com"
                target="_blank"
                rel="noopener noreferrer"
              >
                <Twitter />
              </IconButton>
              <IconButton
                color="inherit"
                href="https://linkedin.com"
                target="_blank"
                rel="noopener noreferrer"
              >
                <LinkedIn />
              </IconButton>
              <IconButton
                color="inherit"
                href="https://github.com"
                target="_blank"
                rel="noopener noreferrer"
              >
                <GitHub />
              </IconButton>
            </Stack>
          </Grid>

          {/* 快速链接 */}
          <Grid item xs={12} sm={6} md={2}>
            <Typography variant="h6" fontWeight="bold" gutterBottom>
              平台
            </Typography>
            <Stack spacing={1}>
              <Link
                component="button"
                variant="body2"
                color="inherit"
                onClick={() => handleLinkClick('/marketplace')}
                sx={{ textAlign: 'left', textDecoration: 'none' }}
              >
                资源市场
              </Link>
              <Link
                component="button"
                variant="body2"
                color="inherit"
                onClick={() => handleLinkClick('/creators')}
                sx={{ textAlign: 'left', textDecoration: 'none' }}
              >
                创作者中心
              </Link>
              <Link
                component="button"
                variant="body2"
                color="inherit"
                onClick={() => handleLinkClick('/upload')}
                sx={{ textAlign: 'left', textDecoration: 'none' }}
              >
                上传资源
              </Link>
              <Link
                component="button"
                variant="body2"
                color="inherit"
                onClick={() => handleLinkClick('/pricing')}
                sx={{ textAlign: 'left', textDecoration: 'none' }}
              >
                定价方案
              </Link>
            </Stack>
          </Grid>

          {/* 资源分类 */}
          <Grid item xs={12} sm={6} md={2}>
            <Typography variant="h6" fontWeight="bold" gutterBottom>
              资源分类
            </Typography>
            <Stack spacing={1}>
              <Link
                component="button"
                variant="body2"
                color="inherit"
                onClick={() => handleLinkClick('/marketplace?category=fine_tuned_models')}
                sx={{ textAlign: 'left', textDecoration: 'none' }}
              >
                微调模型
              </Link>
              <Link
                component="button"
                variant="body2"
                color="inherit"
                onClick={() => handleLinkClick('/marketplace?category=lora')}
                sx={{ textAlign: 'left', textDecoration: 'none' }}
              >
                LoRA
              </Link>
              <Link
                component="button"
                variant="body2"
                color="inherit"
                onClick={() => handleLinkClick('/marketplace?category=workflows')}
                sx={{ textAlign: 'left', textDecoration: 'none' }}
              >
                工作流
              </Link>
              <Link
                component="button"
                variant="body2"
                color="inherit"
                onClick={() => handleLinkClick('/marketplace?category=prompts')}
                sx={{ textAlign: 'left', textDecoration: 'none' }}
              >
                提示词
              </Link>
              <Link
                component="button"
                variant="body2"
                color="inherit"
                onClick={() => handleLinkClick('/marketplace?category=tools')}
                sx={{ textAlign: 'left', textDecoration: 'none' }}
              >
                工具
              </Link>
            </Stack>
          </Grid>

          {/* 支持与帮助 */}
          <Grid item xs={12} sm={6} md={2}>
            <Typography variant="h6" fontWeight="bold" gutterBottom>
              支持
            </Typography>
            <Stack spacing={1}>
              <Link
                component="button"
                variant="body2"
                color="inherit"
                onClick={() => handleLinkClick('/docs')}
                sx={{ textAlign: 'left', textDecoration: 'none' }}
              >
                帮助文档
              </Link>
              <Link
                component="button"
                variant="body2"
                color="inherit"
                onClick={() => handleLinkClick('/api-docs')}
                sx={{ textAlign: 'left', textDecoration: 'none' }}
              >
                API文档
              </Link>
              <Link
                component="button"
                variant="body2"
                color="inherit"
                onClick={() => handleLinkClick('/contact')}
                sx={{ textAlign: 'left', textDecoration: 'none' }}
              >
                联系我们
              </Link>
              <Link
                component="button"
                variant="body2"
                color="inherit"
                onClick={() => handleLinkClick('/support')}
                sx={{ textAlign: 'left', textDecoration: 'none' }}
              >
                技术支持
              </Link>
            </Stack>
          </Grid>

          {/* 联系信息 */}
          <Grid item xs={12} sm={6} md={2}>
            <Typography variant="h6" fontWeight="bold" gutterBottom>
              联系我们
            </Typography>
            <Stack spacing={1}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <Email fontSize="small" />
                <Typography variant="body2">
                  <EMAIL>
                </Typography>
              </Box>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <Phone fontSize="small" />
                <Typography variant="body2">
                  +86 ************
                </Typography>
              </Box>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <LocationOn fontSize="small" />
                <Typography variant="body2">
                  北京市朝阳区
                </Typography>
              </Box>
            </Stack>
          </Grid>
        </Grid>

        <Divider sx={{ my: 4, borderColor: 'grey.700' }} />

        {/* 底部版权信息 */}
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} md={6}>
            <Typography variant="body2" color="grey.400">
              © 2024 AIGC Service Hub. 保留所有权利。
            </Typography>
          </Grid>
          
          <Grid item xs={12} md={6}>
            <Stack
              direction={{ xs: 'column', sm: 'row' }}
              spacing={2}
              justifyContent={{ md: 'flex-end' }}
            >
              <Link
                component="button"
                variant="body2"
                color="grey.400"
                onClick={() => handleLinkClick('/privacy')}
                sx={{ textDecoration: 'none' }}
              >
                隐私政策
              </Link>
              <Link
                component="button"
                variant="body2"
                color="grey.400"
                onClick={() => handleLinkClick('/terms')}
                sx={{ textDecoration: 'none' }}
              >
                服务条款
              </Link>
              <Link
                component="button"
                variant="body2"
                color="grey.400"
                onClick={() => handleLinkClick('/cookies')}
                sx={{ textDecoration: 'none' }}
              >
                Cookie政策
              </Link>
            </Stack>
          </Grid>
        </Grid>
      </Container>
    </Box>
  );
};

export default Footer;
