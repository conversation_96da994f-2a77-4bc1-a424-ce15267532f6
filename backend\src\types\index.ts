import { Request } from 'express';

// 用户相关类型定义
export interface User {
  id: string;
  email: string;
  passwordHash: string;
  username: string;
  displayName?: string;
  avatar?: string;
  bio?: string;
  userType: 'individual' | 'enterprise';
  status: 'pending' | 'active' | 'suspended' | 'banned';

  // 个人创作者字段
  firstName?: string;
  lastName?: string;
  phoneNumber?: string;
  country?: string;

  // 企业创作者字段
  companyName?: string;
  companyRegistrationNumber?: string;
  companyAddress?: string;
  taxId?: string;
  contactPersonName?: string;
  contactPersonTitle?: string;

  // PayPal相关字段
  paypalEmail?: string;
  paypalAccountId?: string;

  // 财务相关字段
  balance: number;
  frozenBalance: number;
  totalEarnings: number;
  totalSales: number;
  totalDownloads: number;
  totalLikes: number;

  // 积分系统
  points: number;
  lastSignInDate?: Date;
  consecutiveSignInDays: number;

  // 邮箱验证
  emailVerified: boolean;
  emailVerificationToken?: string;
  emailVerificationExpires?: Date;

  // 密码重置
  passwordResetToken?: string;
  passwordResetExpires?: Date;

  // 最后登录信息
  lastLoginAt?: Date;
  lastLoginIp?: string;

  // 账户设置
  emailNotifications: boolean;
  language: string;
  timezone: string;

  createdAt: Date;
  updatedAt: Date;
}

export interface UserRegistrationData {
  email: string;
  password: string;
  username?: string;
  displayName?: string;
  userType: 'individual' | 'enterprise';

  // 个人创作者字段
  firstName?: string;
  lastName?: string;
  phoneNumber?: string;
  country?: string;

  // 企业创作者字段
  companyName?: string;
  companyRegistrationNumber?: string;
  companyAddress?: string;
  taxId?: string;
  contactPersonName?: string;
  contactPersonTitle?: string;

  // 账户设置
  language?: string;
  timezone?: string;
}

export interface UserLoginData {
  email: string;
  password: string;
}

export interface JWTPayload {
  userId: string;
  email: string;
  userType: 'individual' | 'enterprise';
  iat?: number;
  exp?: number;
}

// 资源相关类型定义
export type ResourceCategory =
  | 'fine_tuned_models'
  | 'lora'
  | 'workflows'
  | 'prompts'
  | 'tools';

export interface Resource {
  id: string;
  creatorId: string;
  title: string;
  description: string;
  category: ResourceCategory;
  tags: string[];
  priceUsd: number;
  pricePoints: number;
  fileUrl: string;
  fileSize: number;
  coverImageUrl?: string;
  downloadCount: number;
  salesCount: number;
  status: 'active' | 'inactive' | 'pending_review';
  createdAt: Date;
  updatedAt: Date;
}

export interface ResourceCreateData {
  title: string;
  description: string;
  category: ResourceCategory;
  tags: string[];
  priceUsd: number;
  pricePoints: number;
  fileSize: number;
  coverImageUrl?: string;
}

// 交易相关类型定义
export interface Transaction {
  id: string;
  buyerId: string;
  sellerId: string;
  resourceId: string;
  amountUsd: number;
  platformFee: number;
  creatorEarnings: number;
  paymentMethod: 'paypal' | 'points';
  paypalTransactionId?: string;
  status: 'pending' | 'completed' | 'failed' | 'refunded';
  downloadUrl?: string;
  downloadExpiresAt?: Date;
  createdAt: Date;
  updatedAt: Date;
}

export interface TransactionCreateData {
  resourceId: string;
  paymentMethod: 'paypal' | 'points';
  paypalTransactionId?: string;
}

// 分佣记录类型定义
export interface CommissionRecord {
  id: string;
  transactionId: string;
  creatorId: string;
  resourceId: string;
  salesSequence: number;
  commissionRate: number;
  platformFee: number;
  creatorEarnings: number;
  createdAt: Date;
}

// 提现记录类型定义
export interface WithdrawalRequest {
  id: string;
  userId: string;
  amount: number;
  paypalFee: number;
  taxFee: number;
  netAmount: number;
  paypalEmail: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  paypalPayoutId?: string;
  processedAt?: Date;
  createdAt: Date;
}

export interface WithdrawalCreateData {
  amount: number;
  paypalEmail: string;
}

// 积分交易类型定义
export interface PointsTransaction {
  id: string;
  userId: string;
  points: number;
  type: 'earned' | 'spent' | 'expired';
  source: 'daily_checkin' | 'first_upload' | 'sales_milestone' | 'purchase';
  description: string;
  createdAt: Date;
}

// 退款申请类型定义
export interface RefundRequest {
  id: string;
  transactionId: string;
  requesterId: string;
  reason: string;
  adminNotes?: string;
  status: 'pending' | 'approved' | 'rejected';
  processedBy?: string;
  processedAt?: Date;
  createdAt: Date;
}

export interface RefundCreateData {
  transactionId: string;
  reason: string;
}

// API响应类型定义
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: {
    code: string;
    message: string;
    details?: any[];
  };
  message?: string;
  timestamp: string;
}

export interface PaginatedResponse<T> {
  items: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

// Express扩展类型
export interface AuthenticatedRequest extends Request {
  user?: JWTPayload;
}

// 数据库查询选项
export interface QueryOptions {
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  filters?: Record<string, any>;
}

// 文件上传类型
export interface FileUploadInfo {
  originalName: string;
  fileName: string;
  mimeType: string;
  size: number;
  url: string;
  key: string;
}

export interface ChunkUploadInfo {
  chunkNumber: number;
  totalChunks: number;
  fileName: string;
  fileId: string;
  chunkSize: number;
}

// 邮件类型
export interface EmailOptions {
  to: string;
  subject: string;
  text?: string;
  html?: string;
  template?: string;
  context?: Record<string, any>;
}

// 配置类型
export interface AppConfig {
  port: number;
  nodeEnv: string;
  jwtSecret: string;
  jwtExpiresIn: string;
  databaseUrl: string;
  corsOrigin: string;
  maxFileSize: number;
  chunkSize: number;
  allowedFileTypes: string[];
}

// PayPal相关类型
export interface PayPalOrderData {
  id: string;
  status: string;
  purchase_units: Array<{
    amount: {
      currency_code: string;
      value: string;
    };
    custom_id?: string;
  }>;
}

export interface PayPalWebhookEvent {
  id: string;
  event_type: string;
  resource_type: string;
  resource: any;
  create_time: string;
}

// 错误类型
export class AppError extends Error {
  public statusCode: number;
  public isOperational: boolean;
  public code: string | undefined;

  constructor(message: string, statusCode: number, code?: string) {
    super(message);
    this.statusCode = statusCode;
    this.isOperational = true;
    this.code = code;

    Error.captureStackTrace(this, this.constructor);
  }
}

// 排名相关类型
export interface CreatorRanking {
  userId: string;
  userName: string;
  userType: 'individual' | 'enterprise';
  totalSales: number;
  totalDownloads: number;
  totalEarnings: number;
  rank: number;
  period: 'week' | 'month' | 'all_time';
}
