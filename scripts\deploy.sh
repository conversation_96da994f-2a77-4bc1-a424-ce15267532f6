#!/bin/bash

# AIGC Service Hub Docker Deployment Script
# 用于自动化部署和管理Docker容器

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查Docker和Docker Compose
check_dependencies() {
    log_info "检查依赖..."
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安装，请先安装 Docker"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose 未安装，请先安装 Docker Compose"
        exit 1
    fi
    
    log_success "依赖检查通过"
}

# 检查环境变量文件
check_env_file() {
    if [ ! -f .env ]; then
        log_warning ".env 文件不存在，从模板创建..."
        cp .env.docker .env
        log_warning "请编辑 .env 文件并填入正确的配置值"
        exit 1
    fi
    log_success "环境变量文件检查通过"
}

# 构建镜像
build_images() {
    log_info "构建Docker镜像..."
    
    case $1 in
        "dev")
            docker-compose -f docker-compose.dev.yml build
            ;;
        "prod")
            docker-compose -f docker-compose.prod.yml build
            ;;
        *)
            log_error "无效的环境: $1"
            exit 1
            ;;
    esac
    
    log_success "镜像构建完成"
}

# 启动服务
start_services() {
    log_info "启动服务..."
    
    case $1 in
        "dev")
            docker-compose -f docker-compose.dev.yml up -d
            ;;
        "prod")
            docker-compose -f docker-compose.prod.yml up -d
            ;;
        *)
            log_error "无效的环境: $1"
            exit 1
            ;;
    esac
    
    log_success "服务启动完成"
}

# 停止服务
stop_services() {
    log_info "停止服务..."
    
    case $1 in
        "dev")
            docker-compose -f docker-compose.dev.yml down
            ;;
        "prod")
            docker-compose -f docker-compose.prod.yml down
            ;;
        *)
            log_error "无效的环境: $1"
            exit 1
            ;;
    esac
    
    log_success "服务停止完成"
}

# 查看服务状态
show_status() {
    log_info "服务状态:"
    
    case $1 in
        "dev")
            docker-compose -f docker-compose.dev.yml ps
            ;;
        "prod")
            docker-compose -f docker-compose.prod.yml ps
            ;;
        *)
            log_error "无效的环境: $1"
            exit 1
            ;;
    esac
}

# 查看日志
show_logs() {
    case $1 in
        "dev")
            docker-compose -f docker-compose.dev.yml logs -f $2
            ;;
        "prod")
            docker-compose -f docker-compose.prod.yml logs -f $2
            ;;
        *)
            log_error "无效的环境: $1"
            exit 1
            ;;
    esac
}

# 数据库迁移
run_migrations() {
    log_info "运行数据库迁移..."
    
    case $1 in
        "dev")
            docker-compose -f docker-compose.dev.yml exec backend npm run migrate
            ;;
        "prod")
            docker-compose -f docker-compose.prod.yml exec backend npm run migrate
            ;;
        *)
            log_error "无效的环境: $1"
            exit 1
            ;;
    esac
    
    log_success "数据库迁移完成"
}

# 创建种子数据
seed_database() {
    log_info "创建种子数据..."
    
    case $1 in
        "dev")
            docker-compose -f docker-compose.dev.yml exec backend npm run seed
            ;;
        "prod")
            log_warning "生产环境不建议运行种子数据"
            read -p "确定要在生产环境创建种子数据吗? (y/N): " confirm
            if [[ $confirm == [yY] ]]; then
                docker-compose -f docker-compose.prod.yml exec backend npm run seed
            fi
            ;;
        *)
            log_error "无效的环境: $1"
            exit 1
            ;;
    esac
    
    log_success "种子数据创建完成"
}

# 备份数据库
backup_database() {
    log_info "备份数据库..."
    
    timestamp=$(date +%Y%m%d_%H%M%S)
    backup_file="backup_${timestamp}.sql"
    
    case $1 in
        "dev")
            docker-compose -f docker-compose.dev.yml exec postgres pg_dump -U postgres aigc_service_hub_dev > $backup_file
            ;;
        "prod")
            docker-compose -f docker-compose.prod.yml exec postgres pg_dump -U postgres aigc_service_hub > $backup_file
            ;;
        *)
            log_error "无效的环境: $1"
            exit 1
            ;;
    esac
    
    log_success "数据库备份完成: $backup_file"
}

# 清理资源
cleanup() {
    log_info "清理Docker资源..."
    
    # 停止所有容器
    docker-compose -f docker-compose.dev.yml down 2>/dev/null || true
    docker-compose -f docker-compose.prod.yml down 2>/dev/null || true
    
    # 清理未使用的镜像
    docker image prune -f
    
    # 清理未使用的卷
    docker volume prune -f
    
    log_success "清理完成"
}

# 健康检查
health_check() {
    log_info "执行健康检查..."
    
    # 检查前端
    if curl -f http://localhost/health &>/dev/null; then
        log_success "前端服务健康"
    else
        log_error "前端服务异常"
    fi
    
    # 检查后端API
    if curl -f http://localhost:3000/api/health &>/dev/null; then
        log_success "后端API服务健康"
    else
        log_error "后端API服务异常"
    fi
    
    # 检查数据库
    case $1 in
        "dev")
            if docker-compose -f docker-compose.dev.yml exec postgres pg_isready -U postgres &>/dev/null; then
                log_success "数据库服务健康"
            else
                log_error "数据库服务异常"
            fi
            ;;
        "prod")
            if docker-compose -f docker-compose.prod.yml exec postgres pg_isready -U postgres &>/dev/null; then
                log_success "数据库服务健康"
            else
                log_error "数据库服务异常"
            fi
            ;;
    esac
}

# 显示帮助信息
show_help() {
    echo "AIGC Service Hub Docker 部署脚本"
    echo ""
    echo "用法: $0 <命令> <环境> [选项]"
    echo ""
    echo "环境:"
    echo "  dev     开发环境"
    echo "  prod    生产环境"
    echo ""
    echo "命令:"
    echo "  build           构建Docker镜像"
    echo "  start           启动服务"
    echo "  stop            停止服务"
    echo "  restart         重启服务"
    echo "  status          查看服务状态"
    echo "  logs [service]  查看日志"
    echo "  migrate         运行数据库迁移"
    echo "  seed            创建种子数据"
    echo "  backup          备份数据库"
    echo "  health          健康检查"
    echo "  cleanup         清理Docker资源"
    echo "  help            显示帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 start dev              # 启动开发环境"
    echo "  $0 logs prod backend      # 查看生产环境后端日志"
    echo "  $0 migrate dev            # 运行开发环境数据库迁移"
}

# 主函数
main() {
    case $1 in
        "build")
            check_dependencies
            check_env_file
            build_images $2
            ;;
        "start")
            check_dependencies
            check_env_file
            start_services $2
            ;;
        "stop")
            check_dependencies
            stop_services $2
            ;;
        "restart")
            check_dependencies
            check_env_file
            stop_services $2
            start_services $2
            ;;
        "status")
            check_dependencies
            show_status $2
            ;;
        "logs")
            check_dependencies
            show_logs $2 $3
            ;;
        "migrate")
            check_dependencies
            run_migrations $2
            ;;
        "seed")
            check_dependencies
            seed_database $2
            ;;
        "backup")
            check_dependencies
            backup_database $2
            ;;
        "health")
            check_dependencies
            health_check $2
            ;;
        "cleanup")
            check_dependencies
            cleanup
            ;;
        "help"|"--help"|"-h")
            show_help
            ;;
        *)
            log_error "无效的命令: $1"
            show_help
            exit 1
            ;;
    esac
}

# 检查参数
if [ $# -eq 0 ]; then
    show_help
    exit 1
fi

# 执行主函数
main "$@"
