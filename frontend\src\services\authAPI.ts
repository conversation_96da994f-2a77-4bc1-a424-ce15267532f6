import axios, { AxiosResponse } from 'axios';
import { User } from '../types';

// API基础URL
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3000/api';

// 创建axios实例
const apiClient = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// 请求拦截器 - 添加认证token
apiClient.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('authToken');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 响应拦截器 - 处理认证错误
apiClient.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      // Token过期或无效，清除本地存储
      localStorage.removeItem('authToken');
      // 可以在这里触发登出或重定向到登录页
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

// API响应类型
interface APIResponse<T> {
  success: boolean;
  data: T;
  message?: string;
}

// 登录响应类型
interface LoginResponse {
  user: User;
  token: string;
  message: string;
}

// 注册请求类型
interface RegisterRequest {
  email: string;
  password: string;
  username?: string;
  displayName?: string;
  userType: 'individual' | 'enterprise';
  firstName?: string;
  lastName?: string;
  phoneNumber?: string;
  country?: string;
  companyName?: string;
  companyRegistrationNumber?: string;
  companyAddress?: string;
  taxId?: string;
  contactPersonName?: string;
  contactPersonTitle?: string;
  language?: string;
  timezone?: string;
}

// 认证API类
export class AuthAPI {
  // 用户登录
  async login(email: string, password: string): Promise<APIResponse<LoginResponse>> {
    const response: AxiosResponse<APIResponse<LoginResponse>> = await apiClient.post('/auth/login', {
      email,
      password,
    });
    return response.data;
  }

  // 用户注册
  async register(userData: RegisterRequest): Promise<APIResponse<LoginResponse>> {
    const response: AxiosResponse<APIResponse<LoginResponse>> = await apiClient.post('/auth/register', userData);
    return response.data;
  }

  // 获取当前用户信息
  async getCurrentUser(token?: string): Promise<User> {
    const headers = token ? { Authorization: `Bearer ${token}` } : {};
    const response: AxiosResponse<APIResponse<{ user: User }>> = await apiClient.get('/auth/me', { headers });
    return response.data.data.user;
  }

  // 用户登出
  async logout(token: string): Promise<void> {
    await apiClient.post('/auth/logout', {}, {
      headers: { Authorization: `Bearer ${token}` }
    });
  }

  // 更新用户资料
  async updateProfile(updates: Partial<User>): Promise<APIResponse<{ user: User }>> {
    const response: AxiosResponse<APIResponse<{ user: User }>> = await apiClient.put('/auth/profile', updates);
    return response.data;
  }

  // 更改密码
  async changePassword(currentPassword: string, newPassword: string, confirmPassword: string): Promise<APIResponse<{ message: string }>> {
    const response: AxiosResponse<APIResponse<{ message: string }>> = await apiClient.put('/auth/change-password', {
      currentPassword,
      newPassword,
      confirmPassword,
    });
    return response.data;
  }

  // 邮箱验证
  async verifyEmail(token: string): Promise<APIResponse<{ message: string }>> {
    const response: AxiosResponse<APIResponse<{ message: string }>> = await apiClient.get(`/auth/verify-email/${token}`);
    return response.data;
  }

  // 重新发送验证邮件
  async resendVerificationEmail(email: string): Promise<APIResponse<{ message: string }>> {
    const response: AxiosResponse<APIResponse<{ message: string }>> = await apiClient.post('/auth/resend-verification', {
      email,
    });
    return response.data;
  }

  // 忘记密码
  async forgotPassword(email: string): Promise<APIResponse<{ message: string }>> {
    const response: AxiosResponse<APIResponse<{ message: string }>> = await apiClient.post('/auth/forgot-password', {
      email,
    });
    return response.data;
  }

  // 重置密码
  async resetPassword(token: string, password: string, confirmPassword: string): Promise<APIResponse<{ message: string }>> {
    const response: AxiosResponse<APIResponse<{ message: string }>> = await apiClient.post(`/auth/reset-password/${token}`, {
      password,
      confirmPassword,
    });
    return response.data;
  }
}

// 导出API实例
export const authAPI = new AuthAPI();

// 导出类型
export type { RegisterRequest, LoginResponse, APIResponse };
