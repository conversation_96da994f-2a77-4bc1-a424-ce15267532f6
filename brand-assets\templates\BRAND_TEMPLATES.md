# AIGC Service Hub 品牌模板库

## 📋 模板概览

本文档提供AIGC Service Hub品牌应用的各种模板，确保品牌在不同场景下的一致性表达。

## 🌐 网页模板

### 1. 网站头部模板

```html
<!-- 标准网站头部 -->
<header class="site-header">
  <div class="container">
    <div class="header-content">
      <!-- Logo区域 -->
      <div class="logo-area">
        <img
          src="/assets/logo-full-color.svg"
          alt="AIGC Service Hub"
          class="site-logo"
          height="40"
        />
      </div>

      <!-- 导航区域 -->
      <nav class="main-nav">
        <ul class="nav-list">
          <li><a href="/" class="nav-link">首页</a></li>
          <li><a href="/marketplace" class="nav-link">市场</a></li>
          <li><a href="/creators" class="nav-link">创作者</a></li>
          <li><a href="/docs" class="nav-link">文档</a></li>
        </ul>
      </nav>

      <!-- 用户区域 -->
      <div class="user-area">
        <button class="btn btn-primary">登录</button>
      </div>
    </div>
  </div>
</header>
```

### 2. 页脚模板

```html
<!-- 标准页脚 -->
<footer class="site-footer">
  <div class="container">
    <div class="footer-content">
      <!-- 品牌区域 -->
      <div class="footer-brand">
        <img
          src="/assets/logo-simple-white.svg"
          alt="AIGC Service Hub"
          class="footer-logo"
          height="32"
        />
        <p class="brand-description">专业的AI创作者服务平台</p>
      </div>

      <!-- 链接区域 -->
      <div class="footer-links">
        <div class="link-group">
          <h4>产品</h4>
          <ul>
            <li><a href="/marketplace">资源市场</a></li>
            <li><a href="/tools">创作工具</a></li>
            <li><a href="/api">API服务</a></li>
          </ul>
        </div>

        <div class="link-group">
          <h4>支持</h4>
          <ul>
            <li><a href="/docs">文档中心</a></li>
            <li><a href="/help">帮助中心</a></li>
            <li><a href="/contact">联系我们</a></li>
          </ul>
        </div>
      </div>
    </div>

    <!-- 版权信息 -->
    <div class="footer-bottom">
      <p>&copy; 2025 AIGC Service Hub. All rights reserved.</p>
    </div>
  </div>
</footer>
```

## 📱 移动应用模板

### 1. 启动屏幕

```jsx
// React Native 启动屏幕
const SplashScreen = () => {
  return (
    <View style={styles.container}>
      <View style={styles.logoContainer}>
        <Image
          source={require('../assets/logo-full-color.png')}
          style={styles.logo}
          resizeMode='contain'
        />
      </View>
      <Text style={styles.tagline}>AI创作者的专业平台</Text>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
    justifyContent: 'center',
    alignItems: 'center',
  },
  logoContainer: {
    marginBottom: 24,
  },
  logo: {
    width: 120,
    height: 60,
  },
  tagline: {
    fontSize: 16,
    color: '#6B7280',
    textAlign: 'center',
  },
});
```

### 2. 导航栏模板

```jsx
// 底部导航栏
const TabNavigator = () => {
  return (
    <Tab.Navigator
      screenOptions={{
        tabBarActiveTintColor: '#2563EB',
        tabBarInactiveTintColor: '#6B7280',
        tabBarStyle: {
          backgroundColor: '#FFFFFF',
          borderTopColor: '#E5E7EB',
        },
      }}
    >
      <Tab.Screen
        name='Home'
        component={HomeScreen}
        options={{
          tabBarIcon: ({ color, size }) => <Icon name='home' size={size} color={color} />,
          title: '首页',
        }}
      />
      <Tab.Screen
        name='Market'
        component={MarketScreen}
        options={{
          tabBarIcon: ({ color, size }) => <Icon name='shopping-bag' size={size} color={color} />,
          title: '市场',
        }}
      />
    </Tab.Navigator>
  );
};
```

## 📄 文档模板

### 1. 技术文档模板

````markdown
# [文档标题]

## 概览

简要描述文档内容和目标读者。

## 快速开始

提供最基本的使用方法。

## 详细说明

深入的技术细节和实现方法。

## 示例代码

```javascript
// 代码示例
const example = 'Hello AIGC Service Hub';
```
````

## 常见问题

Q: 问题描述？A: 解答内容。

## 相关链接

- [相关文档1](link1)
- [相关文档2](link2)

---

**文档维护**: AIGC Service Hub 技术团队  
**最后更新**: 2025年7月17日

````

### 2. API文档模板
```markdown
# API 接口文档

## 接口概述
接口的基本信息和用途说明。

## 请求参数
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| param1 | string | 是 | 参数说明 |

## 响应格式
```json
{
  "code": 200,
  "message": "success",
  "data": {}
}
````

## 错误码

| 错误码 | 说明         |
| ------ | ------------ |
| 400    | 请求参数错误 |
| 401    | 未授权       |

## 示例

### 请求示例

```bash
curl -X POST https://api.aigc-service-hub.com/v1/example \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"param1": "value1"}'
```

### 响应示例

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "result": "example"
  }
}
```

````

## 📧 邮件模板

### 1. 欢迎邮件模板
```html
<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>欢迎加入 AIGC Service Hub</title>
  <style>
    .email-container {
      max-width: 600px;
      margin: 0 auto;
      font-family: 'Inter', sans-serif;
    }
    .header {
      background: linear-gradient(135deg, #2563EB, #7C3AED);
      padding: 40px 20px;
      text-align: center;
    }
    .logo {
      height: 40px;
    }
    .content {
      padding: 40px 20px;
      background: #FFFFFF;
    }
    .button {
      display: inline-block;
      background: #2563EB;
      color: #FFFFFF;
      padding: 12px 24px;
      text-decoration: none;
      border-radius: 6px;
      margin: 20px 0;
    }
  </style>
</head>
<body>
  <div class="email-container">
    <div class="header">
      <img src="https://cdn.aigc-service-hub.com/logo-full-white.png"
           alt="AIGC Service Hub" class="logo">
    </div>

    <div class="content">
      <h1>欢迎加入 AIGC Service Hub！</h1>

      <p>感谢您注册我们的平台。作为AI创作者，您现在可以：</p>

      <ul>
        <li>浏览和购买高质量的AI资源</li>
        <li>上传和销售您的创作成果</li>
        <li>与其他创作者交流合作</li>
        <li>获得专业的技术支持</li>
      </ul>

      <a href="https://aigc-service-hub.com/dashboard" class="button">
        开始探索
      </a>

      <p>如有任何问题，请随时联系我们的支持团队。</p>

      <p>祝您创作愉快！<br>
      AIGC Service Hub 团队</p>
    </div>
  </div>
</body>
</html>
````

## 📱 社交媒体模板

### 1. Twitter发布模板

```
🚀 [产品/功能名称] 现已上线！

✨ 主要特性：
• 特性1
• 特性2
• 特性3

🔗 了解更多：[链接]

#AIGC #AI #创作者 #人工智能

[配图：产品截图或宣传图]
```

### 2. LinkedIn发布模板

```
我们很高兴地宣布 [产品/功能名称] 正式发布！

作为专业的AI创作者服务平台，AIGC Service Hub 致力于为创作者提供：

🎯 高质量的AI资源市场
🛠️ 专业的创作工具
🤝 活跃的创作者社区
💰 公平的收益分配

这次更新将帮助更多创作者实现商业价值，推动AI创作生态的发展。

欢迎访问我们的平台体验新功能：[链接]

#AI #AIGC #创作者经济 #人工智能 #创新
```

## 🎨 设计素材模板

### 1. 海报设计模板

```
设计规格：
- 尺寸：1080x1350px (Instagram Post)
- 分辨率：300 DPI
- 色彩模式：RGB

设计元素：
- 主标题：使用 Inter Bold, 48px
- 副标题：使用 Inter Medium, 24px
- 正文：使用 Inter Regular, 16px
- Logo：右下角，高度 40px
- 背景：品牌渐变色或纯色

布局结构：
- 顶部：主标题 (20% 区域)
- 中部：核心内容 (60% 区域)
- 底部：Logo和标语 (20% 区域)
```

### 2. 名片设计模板

```
设计规格：
- 尺寸：90x54mm (标准名片)
- 分辨率：300 DPI
- 色彩模式：CMYK
- 出血：3mm

正面设计：
- Logo：左上角
- 姓名：Inter Semibold, 18pt
- 职位：Inter Regular, 12pt
- 联系方式：Inter Regular, 10pt

背面设计：
- 品牌图案或纯色背景
- 公司标语
- 网站地址
```

## ✅ 模板使用检查清单

### 品牌一致性

- [ ] 是否使用了正确的Logo版本
- [ ] 颜色是否符合品牌规范
- [ ] 字体是否使用指定字体
- [ ] 整体风格是否与品牌调性一致

### 内容质量

- [ ] 文案是否准确无误
- [ ] 信息是否完整
- [ ] 联系方式是否正确
- [ ] 链接是否有效

### 技术规范

- [ ] 文件格式是否正确
- [ ] 分辨率是否符合要求
- [ ] 文件大小是否合理
- [ ] 是否适配不同设备

---

**模板库版本**: 1.0  
**最后更新**: 2025年7月17日  
**维护团队**: AIGC Service Hub 设计团队
