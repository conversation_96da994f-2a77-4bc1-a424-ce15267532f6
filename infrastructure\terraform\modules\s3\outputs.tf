# S3 Module Outputs

output "files_bucket_name" {
  description = "Name of the files S3 bucket"
  value       = aws_s3_bucket.files.bucket
}

output "files_bucket_id" {
  description = "ID of the files S3 bucket"
  value       = aws_s3_bucket.files.id
}

output "files_bucket_arn" {
  description = "ARN of the files S3 bucket"
  value       = aws_s3_bucket.files.arn
}

output "files_bucket_domain_name" {
  description = "Domain name of the files S3 bucket"
  value       = aws_s3_bucket.files.bucket_domain_name
}

output "files_bucket_regional_domain_name" {
  description = "Regional domain name of the files S3 bucket"
  value       = aws_s3_bucket.files.bucket_regional_domain_name
}

output "backups_bucket_name" {
  description = "Name of the backups S3 bucket"
  value       = aws_s3_bucket.backups.bucket
}

output "backups_bucket_id" {
  description = "ID of the backups S3 bucket"
  value       = aws_s3_bucket.backups.id
}

output "backups_bucket_arn" {
  description = "ARN of the backups S3 bucket"
  value       = aws_s3_bucket.backups.arn
}

output "logs_bucket_name" {
  description = "Name of the logs S3 bucket"
  value       = aws_s3_bucket.logs.bucket
}

output "logs_bucket_id" {
  description = "ID of the logs S3 bucket"
  value       = aws_s3_bucket.logs.id
}

output "logs_bucket_arn" {
  description = "ARN of the logs S3 bucket"
  value       = aws_s3_bucket.logs.arn
}
