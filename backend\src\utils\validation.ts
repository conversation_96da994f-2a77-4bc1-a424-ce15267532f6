import { UserRegistrationData, UserLoginData } from '@/types';

export interface ValidationResult {
  isValid: boolean;
  errors: Array<{
    field: string;
    message: string;
  }>;
}

// 邮箱格式验证
export function isValidEmail(email: string): boolean {
  const emailRegex = /^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$/;
  return emailRegex.test(email);
}

// 密码强度验证
export function isValidPassword(password: string): boolean {
  // 至少8位，包含大小写字母和数字
  const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[A-Za-z\d@$!%*?&]{8,}$/;
  return passwordRegex.test(password);
}

// 用户名格式验证
export function isValidUsername(username: string): boolean {
  // 3-30位，只允许字母、数字、下划线、连字符
  const usernameRegex = /^[A-Za-z0-9_-]{3,30}$/;
  return usernameRegex.test(username);
}

// 电话号码格式验证
export function isValidPhoneNumber(phone: string): boolean {
  // 简单的国际电话号码格式验证
  const phoneRegex = /^\+?[1-9]\d{1,14}$/;
  return phoneRegex.test(phone.replace(/[\s-()]/g, ''));
}

// 验证用户注册数据
export function validateRegistrationData(data: UserRegistrationData): ValidationResult {
  const errors: Array<{ field: string; message: string }> = [];

  // 验证邮箱
  if (!data.email) {
    errors.push({ field: 'email', message: '邮箱地址不能为空' });
  } else if (!isValidEmail(data.email)) {
    errors.push({ field: 'email', message: '邮箱格式不正确' });
  }

  // 验证密码
  if (!data.password) {
    errors.push({ field: 'password', message: '密码不能为空' });
  } else if (!isValidPassword(data.password)) {
    errors.push({ 
      field: 'password', 
      message: '密码必须至少8位，包含大小写字母和数字' 
    });
  }

  // 验证用户名（如果提供）
  if (data.username && !isValidUsername(data.username)) {
    errors.push({ 
      field: 'username', 
      message: '用户名必须3-30位，只能包含字母、数字、下划线、连字符' 
    });
  }

  // 验证用户类型
  if (!data.userType) {
    errors.push({ field: 'userType', message: '用户类型不能为空' });
  } else if (!['individual', 'enterprise'].includes(data.userType)) {
    errors.push({ field: 'userType', message: '用户类型必须是individual或enterprise' });
  }

  // 验证个人用户必填字段
  if (data.userType === 'individual') {
    if (!data.firstName) {
      errors.push({ field: 'firstName', message: '个人用户必须提供名字' });
    }
    if (!data.lastName) {
      errors.push({ field: 'lastName', message: '个人用户必须提供姓氏' });
    }
  }

  // 验证企业用户必填字段
  if (data.userType === 'enterprise') {
    if (!data.companyName) {
      errors.push({ field: 'companyName', message: '企业用户必须提供公司名称' });
    }
    if (!data.contactPersonName) {
      errors.push({ field: 'contactPersonName', message: '企业用户必须提供联系人姓名' });
    }
  }

  // 验证电话号码（如果提供）
  if (data.phoneNumber && !isValidPhoneNumber(data.phoneNumber)) {
    errors.push({ field: 'phoneNumber', message: '电话号码格式不正确' });
  }

  // 验证语言代码（如果提供）
  if (data.language && !/^[a-z]{2}(-[A-Z]{2})?$/.test(data.language)) {
    errors.push({ field: 'language', message: '语言代码格式不正确' });
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

// 验证用户登录数据
export function validateLoginData(data: UserLoginData): ValidationResult {
  const errors: Array<{ field: string; message: string }> = [];

  // 验证邮箱
  if (!data.email) {
    errors.push({ field: 'email', message: '邮箱地址不能为空' });
  } else if (!isValidEmail(data.email)) {
    errors.push({ field: 'email', message: '邮箱格式不正确' });
  }

  // 验证密码
  if (!data.password) {
    errors.push({ field: 'password', message: '密码不能为空' });
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

// 验证邮箱更新数据
export function validateEmailUpdate(email: string): ValidationResult {
  const errors: Array<{ field: string; message: string }> = [];

  if (!email) {
    errors.push({ field: 'email', message: '邮箱地址不能为空' });
  } else if (!isValidEmail(email)) {
    errors.push({ field: 'email', message: '邮箱格式不正确' });
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

// 验证密码重置数据
export function validatePasswordReset(password: string, confirmPassword: string): ValidationResult {
  const errors: Array<{ field: string; message: string }> = [];

  if (!password) {
    errors.push({ field: 'password', message: '新密码不能为空' });
  } else if (!isValidPassword(password)) {
    errors.push({ 
      field: 'password', 
      message: '密码必须至少8位，包含大小写字母和数字' 
    });
  }

  if (!confirmPassword) {
    errors.push({ field: 'confirmPassword', message: '确认密码不能为空' });
  } else if (password !== confirmPassword) {
    errors.push({ field: 'confirmPassword', message: '两次输入的密码不一致' });
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

// 验证用户资料更新数据
export function validateProfileUpdate(data: Partial<UserRegistrationData>): ValidationResult {
  const errors: Array<{ field: string; message: string }> = [];

  // 验证用户名（如果提供）
  if (data.username !== undefined && !isValidUsername(data.username)) {
    errors.push({ 
      field: 'username', 
      message: '用户名必须3-30位，只能包含字母、数字、下划线、连字符' 
    });
  }

  // 验证电话号码（如果提供）
  if (data.phoneNumber !== undefined && data.phoneNumber && !isValidPhoneNumber(data.phoneNumber)) {
    errors.push({ field: 'phoneNumber', message: '电话号码格式不正确' });
  }

  // 验证语言代码（如果提供）
  if (data.language !== undefined && data.language && !/^[a-z]{2}(-[A-Z]{2})?$/.test(data.language)) {
    errors.push({ field: 'language', message: '语言代码格式不正确' });
  }

  // 验证显示名称长度（如果提供）
  if (data.displayName !== undefined && data.displayName && data.displayName.length > 100) {
    errors.push({ field: 'displayName', message: '显示名称不能超过100个字符' });
  }

  // 验证个人简介长度（如果提供）
  if (data.bio !== undefined && data.bio && data.bio.length > 500) {
    errors.push({ field: 'bio', message: '个人简介不能超过500个字符' });
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}
