import React, { useState } from 'react';
import {
  A<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Button,
  Box,
  IconButton,
  Menu,
  MenuItem,
  Avatar,
  Divider,
  Badge,
  InputBase,
  alpha,
} from '@mui/material';
import {
  Search,
  AccountCircle,
  Notifications,
  ShoppingCart,
  Menu as MenuIcon,
  CloudUpload,
  Dashboard,
  Settings,
  ExitToApp,
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';

// 模拟用户状态
const mockUser = {
  id: '1',
  name: '测试用户',
  email: '<EMAIL>',
  avatar: '',
  isLoggedIn: true,
};

const Header: React.FC = () => {
  const navigate = useNavigate();
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [searchQuery, setSearchQuery] = useState('');

  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  const handleSearch = (event: React.FormEvent) => {
    event.preventDefault();
    if (searchQuery.trim()) {
      navigate(`/marketplace?search=${encodeURIComponent(searchQuery)}`);
    }
  };

  const handleLogout = () => {
    // 这里实现登出逻辑
    handleMenuClose();
    navigate('/');
  };

  return (
    <AppBar position="sticky" sx={{ bgcolor: 'white', color: 'text.primary', boxShadow: 1 }}>
      <Toolbar sx={{ justifyContent: 'space-between' }}>
        {/* Logo和品牌 */}
        <Box sx={{ display: 'flex', alignItems: 'center', cursor: 'pointer' }} onClick={() => navigate('/')}>
          <CloudUpload sx={{ mr: 1, color: 'primary.main' }} />
          <Typography
            variant="h6"
            component="div"
            sx={{
              fontWeight: 'bold',
              color: 'primary.main',
              display: { xs: 'none', sm: 'block' },
            }}
          >
            AIGC Service Hub
          </Typography>
        </Box>

        {/* 主导航菜单 */}
        <Box sx={{ display: { xs: 'none', md: 'flex' }, gap: 2 }}>
          <Button
            color="inherit"
            onClick={() => navigate('/')}
            sx={{ fontWeight: 500 }}
          >
            首页
          </Button>
          <Button
            color="inherit"
            onClick={() => navigate('/marketplace')}
            sx={{ fontWeight: 500 }}
          >
            市场
          </Button>
          <Button
            color="inherit"
            onClick={() => navigate('/creators')}
            sx={{ fontWeight: 500 }}
          >
            创作者
          </Button>
          <Button
            color="inherit"
            onClick={() => navigate('/docs')}
            sx={{ fontWeight: 500 }}
          >
            文档
          </Button>
        </Box>

        {/* 搜索框 */}
        <Box
          component="form"
          onSubmit={handleSearch}
          sx={{
            position: 'relative',
            borderRadius: 1,
            backgroundColor: alpha('#000', 0.05),
            '&:hover': {
              backgroundColor: alpha('#000', 0.08),
            },
            marginLeft: { xs: 1, sm: 2 },
            width: { xs: 'auto', sm: '300px' },
            display: { xs: 'none', sm: 'block' },
          }}
        >
          <Box
            sx={{
              padding: '0 16px',
              height: '100%',
              position: 'absolute',
              pointerEvents: 'none',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
            }}
          >
            <Search />
          </Box>
          <InputBase
            placeholder="搜索AI资源..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            sx={{
              color: 'inherit',
              width: '100%',
              '& .MuiInputBase-input': {
                padding: '8px 8px 8px 48px',
                transition: 'width 0.3s',
              },
            }}
          />
        </Box>

        {/* 右侧操作区域 */}
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          {mockUser.isLoggedIn ? (
            <>
              {/* 上传按钮 */}
              <Button
                variant="contained"
                startIcon={<CloudUpload />}
                onClick={() => navigate('/upload')}
                sx={{ display: { xs: 'none', sm: 'flex' } }}
              >
                上传
              </Button>

              {/* 通知 */}
              <IconButton color="inherit">
                <Badge badgeContent={3} color="error">
                  <Notifications />
                </Badge>
              </IconButton>

              {/* 购物车 */}
              <IconButton color="inherit" onClick={() => navigate('/cart')}>
                <Badge badgeContent={2} color="error">
                  <ShoppingCart />
                </Badge>
              </IconButton>

              {/* 用户菜单 */}
              <IconButton
                size="large"
                edge="end"
                aria-label="account of current user"
                aria-controls="primary-search-account-menu"
                aria-haspopup="true"
                onClick={handleMenuOpen}
                color="inherit"
              >
                {mockUser.avatar ? (
                  <Avatar src={mockUser.avatar} sx={{ width: 32, height: 32 }} />
                ) : (
                  <Avatar sx={{ width: 32, height: 32, bgcolor: 'primary.main' }}>
                    {mockUser.name.charAt(0)}
                  </Avatar>
                )}
              </IconButton>

              {/* 用户下拉菜单 */}
              <Menu
                anchorEl={anchorEl}
                anchorOrigin={{
                  vertical: 'bottom',
                  horizontal: 'right',
                }}
                keepMounted
                transformOrigin={{
                  vertical: 'top',
                  horizontal: 'right',
                }}
                open={Boolean(anchorEl)}
                onClose={handleMenuClose}
              >
                <Box sx={{ px: 2, py: 1 }}>
                  <Typography variant="subtitle2" fontWeight="bold">
                    {mockUser.name}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {mockUser.email}
                  </Typography>
                </Box>
                <Divider />
                <MenuItem onClick={() => { navigate('/profile'); handleMenuClose(); }}>
                  <AccountCircle sx={{ mr: 1 }} />
                  个人中心
                </MenuItem>
                <MenuItem onClick={() => { navigate('/resources'); handleMenuClose(); }}>
                  <Dashboard sx={{ mr: 1 }} />
                  资源管理
                </MenuItem>
                <MenuItem onClick={() => { navigate('/settings'); handleMenuClose(); }}>
                  <Settings sx={{ mr: 1 }} />
                  账户设置
                </MenuItem>
                <Divider />
                <MenuItem onClick={handleLogout}>
                  <ExitToApp sx={{ mr: 1 }} />
                  退出登录
                </MenuItem>
              </Menu>
            </>
          ) : (
            <>
              {/* 未登录状态 */}
              <Button
                color="inherit"
                onClick={() => navigate('/login')}
                sx={{ fontWeight: 500 }}
              >
                登录
              </Button>
              <Button
                variant="contained"
                onClick={() => navigate('/register')}
                sx={{ ml: 1 }}
              >
                注册
              </Button>
            </>
          )}

          {/* 移动端菜单按钮 */}
          <IconButton
            size="large"
            edge="start"
            color="inherit"
            aria-label="menu"
            sx={{ display: { md: 'none' } }}
          >
            <MenuIcon />
          </IconButton>
        </Box>
      </Toolbar>
    </AppBar>
  );
};

export default Header;
