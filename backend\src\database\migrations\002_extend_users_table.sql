-- 扩展用户表，添加更多字段以支持完整的用户管理功能
-- 这个迁移文件添加了个人信息、企业信息、账户设置等字段

-- 添加基本用户信息字段
ALTER TABLE users ADD COLUMN IF NOT EXISTS username VARCHAR(100) UNIQUE;
ALTER TABLE users ADD COLUMN IF NOT EXISTS display_name VARCHAR(100);
ALTER TABLE users ADD COLUMN IF NOT EXISTS avatar TEXT;
ALTER TABLE users ADD COLUMN IF NOT EXISTS bio TEXT;

-- 添加用户状态字段
ALTER TABLE users ADD COLUMN IF NOT EXISTS status VARCHAR(20) DEFAULT 'pending' 
    CHECK (status IN ('pending', 'active', 'suspended', 'banned'));

-- 添加个人创作者字段
ALTER TABLE users ADD COLUMN IF NOT EXISTS first_name VARCHAR(100);
ALTER TABLE users ADD COLUMN IF NOT EXISTS last_name VARCHA<PERSON>(100);
ALTER TABLE users ADD COLUMN IF NOT EXISTS phone_number VARCHAR(20);
ALTER TABLE users ADD COLUMN IF NOT EXISTS country VARCHAR(100);

-- 添加企业创作者字段
ALTER TABLE users ADD COLUMN IF NOT EXISTS company_registration_number VARCHAR(100);
ALTER TABLE users ADD COLUMN IF NOT EXISTS company_address TEXT;
ALTER TABLE users ADD COLUMN IF NOT EXISTS tax_id VARCHAR(100);
ALTER TABLE users ADD COLUMN IF NOT EXISTS contact_person_name VARCHAR(100);
ALTER TABLE users ADD COLUMN IF NOT EXISTS contact_person_title VARCHAR(100);

-- 添加PayPal相关字段
ALTER TABLE users ADD COLUMN IF NOT EXISTS paypal_account_id VARCHAR(255);

-- 重命名和添加财务字段
ALTER TABLE users RENAME COLUMN available_balance TO balance;
ALTER TABLE users ADD COLUMN IF NOT EXISTS total_likes INTEGER DEFAULT 0;

-- 重命名积分相关字段
ALTER TABLE users RENAME COLUMN points_balance TO points;
ALTER TABLE users RENAME COLUMN last_checkin_date TO last_sign_in_date;
ALTER TABLE users RENAME COLUMN checkin_streak TO consecutive_sign_in_days;

-- 重命名邮箱验证字段
ALTER TABLE users RENAME COLUMN is_verified TO email_verified;
ALTER TABLE users RENAME COLUMN email_verification_expires_at TO email_verification_expires;

-- 添加密码重置字段
ALTER TABLE users ADD COLUMN IF NOT EXISTS password_reset_token VARCHAR(255);
ALTER TABLE users ADD COLUMN IF NOT EXISTS password_reset_expires TIMESTAMP;

-- 添加最后登录信息
ALTER TABLE users ADD COLUMN IF NOT EXISTS last_login_at TIMESTAMP;
ALTER TABLE users ADD COLUMN IF NOT EXISTS last_login_ip INET;

-- 添加账户设置
ALTER TABLE users ADD COLUMN IF NOT EXISTS email_notifications BOOLEAN DEFAULT TRUE;
ALTER TABLE users ADD COLUMN IF NOT EXISTS language VARCHAR(10) DEFAULT 'en';
ALTER TABLE users ADD COLUMN IF NOT EXISTS timezone VARCHAR(50) DEFAULT 'UTC';

-- 为username生成默认值（基于email）
UPDATE users SET username = split_part(email, '@', 1) WHERE username IS NULL;

-- 添加新的索引
CREATE INDEX IF NOT EXISTS idx_users_username ON users(username);
CREATE INDEX IF NOT EXISTS idx_users_status ON users(status);
CREATE INDEX IF NOT EXISTS idx_users_total_earnings ON users(total_earnings);
CREATE INDEX IF NOT EXISTS idx_users_total_sales ON users(total_sales);
CREATE INDEX IF NOT EXISTS idx_users_points ON users(points);
CREATE INDEX IF NOT EXISTS idx_users_first_name ON users(first_name);
CREATE INDEX IF NOT EXISTS idx_users_last_name ON users(last_name);
CREATE INDEX IF NOT EXISTS idx_users_company_name ON users(company_name);

-- 添加新的约束
ALTER TABLE users ADD CONSTRAINT IF NOT EXISTS check_balance_non_negative 
    CHECK (balance >= 0);

ALTER TABLE users ADD CONSTRAINT IF NOT EXISTS check_total_likes_non_negative 
    CHECK (total_likes >= 0);

-- 更新企业用户必填字段约束
ALTER TABLE users DROP CONSTRAINT IF EXISTS valid_company_name;
ALTER TABLE users ADD CONSTRAINT check_enterprise_required_fields
    CHECK (
        user_type != 'enterprise' OR 
        (company_name IS NOT NULL AND contact_person_name IS NOT NULL)
    );

-- 添加个人用户必填字段约束
ALTER TABLE users ADD CONSTRAINT IF NOT EXISTS check_individual_required_fields
    CHECK (
        user_type != 'individual' OR 
        (first_name IS NOT NULL AND last_name IS NOT NULL)
    );

-- 添加用户名格式验证
ALTER TABLE users ADD CONSTRAINT IF NOT EXISTS check_username_format
    CHECK (username IS NULL OR username ~* '^[A-Za-z0-9_-]{3,30}$');

-- 添加PayPal邮箱格式验证
ALTER TABLE users ADD CONSTRAINT IF NOT EXISTS check_paypal_email_format
    CHECK (paypal_email IS NULL OR paypal_email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$');

-- 更新注释
COMMENT ON COLUMN users.username IS '用户名，唯一标识符';
COMMENT ON COLUMN users.display_name IS '显示名称';
COMMENT ON COLUMN users.avatar IS '头像URL';
COMMENT ON COLUMN users.bio IS '个人简介';
COMMENT ON COLUMN users.status IS '用户状态：pending(待验证), active(活跃), suspended(暂停), banned(封禁)';
COMMENT ON COLUMN users.first_name IS '名字（个人用户）';
COMMENT ON COLUMN users.last_name IS '姓氏（个人用户）';
COMMENT ON COLUMN users.phone_number IS '电话号码';
COMMENT ON COLUMN users.country IS '国家/地区';
COMMENT ON COLUMN users.company_registration_number IS '公司注册号（企业用户）';
COMMENT ON COLUMN users.company_address IS '公司地址（企业用户）';
COMMENT ON COLUMN users.tax_id IS '税务ID（企业用户）';
COMMENT ON COLUMN users.contact_person_name IS '联系人姓名（企业用户）';
COMMENT ON COLUMN users.contact_person_title IS '联系人职位（企业用户）';
COMMENT ON COLUMN users.paypal_account_id IS 'PayPal账户ID';
COMMENT ON COLUMN users.balance IS '用户可用余额';
COMMENT ON COLUMN users.total_likes IS '获得的总点赞数';
COMMENT ON COLUMN users.points IS '积分余额';
COMMENT ON COLUMN users.last_sign_in_date IS '最后签到日期';
COMMENT ON COLUMN users.consecutive_sign_in_days IS '连续签到天数';
COMMENT ON COLUMN users.email_verified IS '邮箱是否已验证';
COMMENT ON COLUMN users.password_reset_token IS '密码重置令牌';
COMMENT ON COLUMN users.password_reset_expires IS '密码重置令牌过期时间';
COMMENT ON COLUMN users.last_login_at IS '最后登录时间';
COMMENT ON COLUMN users.last_login_ip IS '最后登录IP地址';
COMMENT ON COLUMN users.email_notifications IS '是否接收邮件通知';
COMMENT ON COLUMN users.language IS '用户界面语言';
COMMENT ON COLUMN users.timezone IS '用户时区';
