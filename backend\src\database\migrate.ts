import fs from 'fs';
import path from 'path';
import { pool, query } from './connection';

// 迁移记录表
const createMigrationsTable = async (): Promise<void> => {
  const sql = `
    CREATE TABLE IF NOT EXISTS migrations (
      id SERIAL PRIMARY KEY,
      filename VARCHAR(255) UNIQUE NOT NULL,
      executed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );
  `;

  await query(sql);
  console.log('✅ Migrations table created or already exists');
};

// 获取已执行的迁移
const getExecutedMigrations = async (): Promise<string[]> => {
  const result = await query('SELECT filename FROM migrations ORDER BY id');
  return result.rows.map((row: any) => row.filename);
};

// 记录迁移执行
const recordMigration = async (filename: string): Promise<void> => {
  await query('INSERT INTO migrations (filename) VALUES ($1)', [filename]);
};

// 执行单个迁移文件
const executeMigration = async (
  filename: string,
  filepath: string
): Promise<void> => {
  console.log(`📄 Executing migration: ${filename}`);

  try {
    const sql = fs.readFileSync(filepath, 'utf8');
    await query(sql);
    await recordMigration(filename);
    console.log(`✅ Migration completed: ${filename}`);
  } catch (error) {
    console.error(`❌ Migration failed: ${filename}`);
    console.error(error);
    throw error;
  }
};

// 主迁移函数
const runMigrations = async (): Promise<void> => {
  try {
    console.log('🚀 Starting database migrations...');

    // 创建迁移记录表
    await createMigrationsTable();

    // 获取迁移文件目录
    const migrationsDir = path.join(__dirname, 'migrations');

    if (!fs.existsSync(migrationsDir)) {
      console.log('📁 No migrations directory found');
      return;
    }

    // 读取所有迁移文件
    const migrationFiles = fs
      .readdirSync(migrationsDir)
      .filter(file => file.endsWith('.sql'))
      .sort(); // 按文件名排序确保执行顺序

    if (migrationFiles.length === 0) {
      console.log('📄 No migration files found');
      return;
    }

    // 获取已执行的迁移
    const executedMigrations = await getExecutedMigrations();

    // 执行未执行的迁移
    let executedCount = 0;
    for (const filename of migrationFiles) {
      if (!executedMigrations.includes(filename)) {
        const filepath = path.join(migrationsDir, filename);
        await executeMigration(filename, filepath);
        executedCount++;
      } else {
        console.log(`⏭️  Skipping already executed migration: ${filename}`);
      }
    }

    if (executedCount === 0) {
      console.log('✨ All migrations are up to date');
    } else {
      console.log(`✅ Successfully executed ${executedCount} migration(s)`);
    }
  } catch (error) {
    console.error('❌ Migration process failed:', error);
    throw error;
  }
};

// 回滚迁移（简单实现）
const rollbackLastMigration = async (): Promise<void> => {
  try {
    console.log('🔄 Rolling back last migration...');

    const result = await query(
      'SELECT filename FROM migrations ORDER BY id DESC LIMIT 1'
    );

    if (result.rows.length === 0) {
      console.log('📄 No migrations to rollback');
      return;
    }

    const lastMigration = result.rows[0].filename;
    console.log(`⚠️  Warning: Rolling back migration: ${lastMigration}`);
    console.log(
      '⚠️  Note: This is a simple rollback that only removes the migration record.'
    );
    console.log('⚠️  You may need to manually revert database changes.');

    await query('DELETE FROM migrations WHERE filename = $1', [lastMigration]);
    console.log(`✅ Migration record removed: ${lastMigration}`);
  } catch (error) {
    console.error('❌ Rollback failed:', error);
    throw error;
  }
};

// 显示迁移状态
const showMigrationStatus = async (): Promise<void> => {
  try {
    console.log('📊 Migration Status:');

    const migrationsDir = path.join(__dirname, 'migrations');
    const migrationFiles = fs.existsSync(migrationsDir)
      ? fs
          .readdirSync(migrationsDir)
          .filter(file => file.endsWith('.sql'))
          .sort()
      : [];

    const executedMigrations = await getExecutedMigrations();

    console.log('\n📄 Available Migrations:');
    migrationFiles.forEach(filename => {
      const status = executedMigrations.includes(filename)
        ? '✅ Executed'
        : '⏳ Pending';
      console.log(`  ${status} - ${filename}`);
    });

    console.log(
      `\n📈 Summary: ${executedMigrations.length}/${migrationFiles.length} migrations executed`
    );
  } catch (error) {
    console.error('❌ Failed to show migration status:', error);
    throw error;
  }
};

// 命令行接口
const main = async (): Promise<void> => {
  const command = process.argv[2];

  try {
    switch (command) {
      case 'up':
      case undefined:
        await runMigrations();
        break;
      case 'rollback':
        await rollbackLastMigration();
        break;
      case 'status':
        await showMigrationStatus();
        break;
      default:
        console.log('Usage: npm run migrate [up|rollback|status]');
        console.log('  up (default) - Run pending migrations');
        console.log('  rollback     - Rollback last migration');
        console.log('  status       - Show migration status');
        process.exit(1);
    }
  } catch (error) {
    console.error('❌ Migration command failed:', error);
    process.exit(1);
  } finally {
    await pool.end();
  }
};

// 如果直接运行此文件
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}

export { runMigrations, rollbackLastMigration, showMigrationStatus };
