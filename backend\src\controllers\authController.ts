import { UserModel } from '@/models/User';
import { UserLoginData, UserRegistrationData } from '@/types';
import { generateToken } from '@/utils/jwt';
import {
  validateLoginData,
  validatePasswordReset,
  validateProfileUpdate,
  validateRegistrationData,
} from '@/utils/validation';
import bcrypt from 'bcryptjs';
import { Request, Response } from 'express';
import { v4 as uuidv4 } from 'uuid';

export class AuthController {
  // 用户注册
  static async register(req: Request, res: Response) {
    try {
      const registrationData: UserRegistrationData = req.body;

      // 验证输入数据
      const validation = validateRegistrationData(registrationData);
      if (!validation.isValid) {
        return res.status(400).json({
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: '输入数据验证失败',
            details: validation.errors,
          },
        });
      }

      // 检查邮箱是否已存在
      const existingUser = await UserModel.findByEmail(registrationData.email);
      if (existingUser) {
        return res.status(409).json({
          success: false,
          error: {
            code: 'EMAIL_EXISTS',
            message: '该邮箱已被注册',
          },
        });
      }

      // 检查用户名是否已存在（如果提供了用户名）
      if (registrationData.username) {
        const existingUsername = await UserModel.findByUsername(
          registrationData.username
        );
        if (existingUsername) {
          return res.status(409).json({
            success: false,
            error: {
              code: 'USERNAME_EXISTS',
              message: '该用户名已被使用',
            },
          });
        }
      }

      // 生成邮箱验证令牌
      const emailVerificationToken = uuidv4();
      const emailVerificationExpires = new Date(
        Date.now() + 24 * 60 * 60 * 1000
      ); // 24小时后过期

      // 创建用户
      const userData = {
        ...registrationData,
        emailVerificationToken,
        emailVerificationExpires,
      };

      const user = await UserModel.create(userData);

      // 发送验证邮件
      await EmailService.sendVerification(user.email, emailVerificationToken);

      // 生成JWT令牌
      const token = generateToken({
        userId: user.id,
        email: user.email,
        userType: user.userType,
      });

      // 返回成功响应（不包含敏感信息）
      const { passwordHash, emailVerificationToken: _, ...userResponse } = user;

      res.status(201).json({
        success: true,
        data: {
          user: userResponse,
          token,
          message: '注册成功，请查看邮箱完成验证',
        },
      });
    } catch (error) {
      console.error('Registration error:', error);
      res.status(500).json({
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: '注册失败，请稍后重试',
        },
      });
    }
  }

  // 用户登录
  static async login(req: Request, res: Response) {
    try {
      const loginData: UserLoginData = req.body;

      // 验证输入数据
      const validation = validateLoginData(loginData);
      if (!validation.isValid) {
        return res.status(400).json({
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: '输入数据验证失败',
            details: validation.errors,
          },
        });
      }

      // 查找用户
      const user = await UserModel.findByEmail(loginData.email);
      if (!user) {
        return res.status(401).json({
          success: false,
          error: {
            code: 'INVALID_CREDENTIALS',
            message: '邮箱或密码错误',
          },
        });
      }

      // 验证密码
      const isPasswordValid = await UserModel.validatePassword(
        user,
        loginData.password
      );
      if (!isPasswordValid) {
        return res.status(401).json({
          success: false,
          error: {
            code: 'INVALID_CREDENTIALS',
            message: '邮箱或密码错误',
          },
        });
      }

      // 检查用户状态
      if (user.status === 'banned') {
        return res.status(403).json({
          success: false,
          error: {
            code: 'ACCOUNT_BANNED',
            message: '账户已被封禁',
          },
        });
      }

      if (user.status === 'suspended') {
        return res.status(403).json({
          success: false,
          error: {
            code: 'ACCOUNT_SUSPENDED',
            message: '账户已被暂停',
          },
        });
      }

      // 更新最后登录信息
      await UserModel.update(user.id, {
        lastLoginAt: new Date(),
        lastLoginIp: req.ip,
      });

      // 生成JWT令牌
      const token = generateToken({
        userId: user.id,
        email: user.email,
        userType: user.userType,
      });

      // 返回成功响应（不包含敏感信息）
      const {
        passwordHash,
        emailVerificationToken,
        passwordResetToken,
        ...userResponse
      } = user;

      res.json({
        success: true,
        data: {
          user: userResponse,
          token,
          message: '登录成功',
        },
      });
    } catch (error) {
      console.error('Login error:', error);
      res.status(500).json({
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: '登录失败，请稍后重试',
        },
      });
    }
  }

  // 邮箱验证
  static async verifyEmail(req: Request, res: Response) {
    try {
      const { token } = req.params;

      if (!token) {
        return res.status(400).json({
          success: false,
          error: {
            code: 'MISSING_TOKEN',
            message: '验证令牌缺失',
          },
        });
      }

      // 查找用户
      const user = await UserModel.findByEmailVerificationToken(token);
      if (!user) {
        return res.status(404).json({
          success: false,
          error: {
            code: 'INVALID_TOKEN',
            message: '验证令牌无效或已过期',
          },
        });
      }

      // 检查令牌是否过期
      if (
        user.emailVerificationExpires &&
        user.emailVerificationExpires < new Date()
      ) {
        return res.status(400).json({
          success: false,
          error: {
            code: 'TOKEN_EXPIRED',
            message: '验证令牌已过期',
          },
        });
      }

      // 更新用户状态
      await UserModel.update(user.id, {
        emailVerified: true,
        status: 'active',
        emailVerificationToken: null,
        emailVerificationExpires: null,
      });

      res.json({
        success: true,
        data: {
          message: '邮箱验证成功',
        },
      });
    } catch (error) {
      console.error('Email verification error:', error);
      res.status(500).json({
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: '邮箱验证失败，请稍后重试',
        },
      });
    }
  }

  // 重新发送验证邮件
  static async resendVerificationEmail(req: Request, res: Response) {
    try {
      const { email } = req.body;

      if (!email) {
        return res.status(400).json({
          success: false,
          error: {
            code: 'MISSING_EMAIL',
            message: '邮箱地址缺失',
          },
        });
      }

      // 查找用户
      const user = await UserModel.findByEmail(email);
      if (!user) {
        return res.status(404).json({
          success: false,
          error: {
            code: 'USER_NOT_FOUND',
            message: '用户不存在',
          },
        });
      }

      // 检查是否已验证
      if (user.emailVerified) {
        return res.status(400).json({
          success: false,
          error: {
            code: 'ALREADY_VERIFIED',
            message: '邮箱已验证',
          },
        });
      }

      // 生成新的验证令牌
      const emailVerificationToken = uuidv4();
      const emailVerificationExpires = new Date(
        Date.now() + 24 * 60 * 60 * 1000
      );

      // 更新用户
      await UserModel.update(user.id, {
        emailVerificationToken,
        emailVerificationExpires,
      });

      // 发送验证邮件
      await EmailService.sendVerification(user.email, emailVerificationToken);

      res.json({
        success: true,
        data: {
          message: '验证邮件已重新发送',
        },
      });
    } catch (error) {
      console.error('Resend verification email error:', error);
      res.status(500).json({
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: '发送验证邮件失败，请稍后重试',
        },
      });
    }
  }

  // 获取当前用户信息
  static async getCurrentUser(req: Request, res: Response) {
    try {
      if (!req.user) {
        return res.status(401).json({
          success: false,
          error: {
            code: 'AUTHENTICATION_REQUIRED',
            message: '需要登录',
          },
        });
      }

      // 获取完整用户信息
      const user = await UserModel.findById(req.user.id);
      if (!user) {
        return res.status(404).json({
          success: false,
          error: {
            code: 'USER_NOT_FOUND',
            message: '用户不存在',
          },
        });
      }

      // 返回用户信息（不包含敏感信息）
      const {
        passwordHash,
        emailVerificationToken,
        passwordResetToken,
        ...userResponse
      } = user;

      res.json({
        success: true,
        data: {
          user: userResponse,
        },
      });
    } catch (error) {
      console.error('Get current user error:', error);
      res.status(500).json({
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: '获取用户信息失败',
        },
      });
    }
  }

  // 更新用户资料
  static async updateProfile(req: Request, res: Response) {
    try {
      if (!req.user) {
        return res.status(401).json({
          success: false,
          error: {
            code: 'AUTHENTICATION_REQUIRED',
            message: '需要登录',
          },
        });
      }

      const updates = req.body;

      // 验证更新数据
      const validation = validateProfileUpdate(updates);
      if (!validation.isValid) {
        return res.status(400).json({
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: '输入数据验证失败',
            details: validation.errors,
          },
        });
      }

      // 检查用户名是否已被使用（如果要更新用户名）
      if (updates.username) {
        const existingUser = await UserModel.findByUsername(updates.username);
        if (existingUser && existingUser.id !== req.user.id) {
          return res.status(409).json({
            success: false,
            error: {
              code: 'USERNAME_EXISTS',
              message: '该用户名已被使用',
            },
          });
        }
      }

      // 更新用户信息
      const updatedUser = await UserModel.update(req.user.id, updates);
      if (!updatedUser) {
        return res.status(404).json({
          success: false,
          error: {
            code: 'USER_NOT_FOUND',
            message: '用户不存在',
          },
        });
      }

      // 返回更新后的用户信息（不包含敏感信息）
      const {
        passwordHash,
        emailVerificationToken,
        passwordResetToken,
        ...userResponse
      } = updatedUser;

      res.json({
        success: true,
        data: {
          user: userResponse,
          message: '用户资料更新成功',
        },
      });
    } catch (error) {
      console.error('Update profile error:', error);
      res.status(500).json({
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: '更新用户资料失败',
        },
      });
    }
  }

  // 更改密码
  static async changePassword(req: Request, res: Response) {
    try {
      if (!req.user) {
        return res.status(401).json({
          success: false,
          error: {
            code: 'AUTHENTICATION_REQUIRED',
            message: '需要登录',
          },
        });
      }

      const { currentPassword, newPassword, confirmPassword } = req.body;

      // 验证输入
      if (!currentPassword || !newPassword || !confirmPassword) {
        return res.status(400).json({
          success: false,
          error: {
            code: 'MISSING_FIELDS',
            message: '请提供当前密码、新密码和确认密码',
          },
        });
      }

      if (newPassword !== confirmPassword) {
        return res.status(400).json({
          success: false,
          error: {
            code: 'PASSWORD_MISMATCH',
            message: '新密码和确认密码不一致',
          },
        });
      }

      // 验证新密码强度
      const validation = validatePasswordReset(newPassword, confirmPassword);
      if (!validation.isValid) {
        return res.status(400).json({
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: '密码验证失败',
            details: validation.errors,
          },
        });
      }

      // 获取用户信息
      const user = await UserModel.findById(req.user.id);
      if (!user) {
        return res.status(404).json({
          success: false,
          error: {
            code: 'USER_NOT_FOUND',
            message: '用户不存在',
          },
        });
      }

      // 验证当前密码
      const isCurrentPasswordValid = await UserModel.validatePassword(
        user,
        currentPassword
      );
      if (!isCurrentPasswordValid) {
        return res.status(400).json({
          success: false,
          error: {
            code: 'INVALID_CURRENT_PASSWORD',
            message: '当前密码错误',
          },
        });
      }

      // 更新密码
      const hashedNewPassword = await bcrypt.hash(newPassword, 12);
      await UserModel.update(req.user.id, {
        passwordHash: hashedNewPassword,
      });

      res.json({
        success: true,
        data: {
          message: '密码更新成功',
        },
      });
    } catch (error) {
      console.error('Change password error:', error);
      res.status(500).json({
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: '更改密码失败',
        },
      });
    }
  }
}
