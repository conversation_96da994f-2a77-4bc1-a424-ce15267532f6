import {
  AccountCircle,
  Assessment,
  AttachMoney,
  CloudUpload,
  Download,
  Edit,
  ExitToApp,
  Favorite,
  Settings,
} from '@mui/icons-material';
import {
  Alert,
  Avatar,
  Box,
  Button,
  Card,
  CardContent,
  Chip,
  Container,
  Divider,
  Grid,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
  Paper,
  Tab,
  Tabs,
  Typography,
} from '@mui/material';
import React, { useState } from 'react';

// 模拟用户数据
const mockUser = {
  id: '1',
  username: 'testuser',
  displayName: '测试用户',
  email: '<EMAIL>',
  userType: 'individual' as const,
  status: 'active' as const,
  emailVerified: true,
  bio: '这是一个测试用户的个人简介',
  avatar: '',
  totalEarnings: 1234.56,
  totalSales: 45,
  totalDownloads: 678,
  totalLikes: 234,
};

// 模拟资源数据
const mockResources = [
  {
    id: '1',
    title: 'AI人像生成LoRA模型',
    category: 'LoRA',
    price: 29.99,
    downloads: 156,
    likes: 89,
    revenue: 4678.44,
    status: 'active' as const,
    createdAt: '2024-01-15',
  },
  {
    id: '2',
    title: 'Stable Diffusion工作流',
    category: '工作流',
    price: 19.99,
    downloads: 234,
    likes: 145,
    revenue: 4677.66,
    status: 'active' as const,
    createdAt: '2024-01-10',
  },
  {
    id: '3',
    title: '商业摄影提示词集',
    category: '提示词',
    price: 9.99,
    downloads: 89,
    likes: 67,
    revenue: 889.11,
    status: 'pending' as const,
    createdAt: '2024-01-08',
  },
];

// Tab面板组件
interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role='tabpanel'
      hidden={value !== index}
      id={`profile-tabpanel-${index}`}
      aria-labelledby={`profile-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ py: 3 }}>{children}</Box>}
    </div>
  );
}

const SimpleProfilePage: React.FC = () => {
  const [tabValue, setTabValue] = useState(0);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const handleLogout = () => {
    window.location.href = '/';
  };

  // 获取资源状态颜色
  const getResourceStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'success';
      case 'pending':
        return 'warning';
      case 'rejected':
        return 'error';
      case 'draft':
        return 'default';
      default:
        return 'default';
    }
  };

  // 获取资源状态文本
  const getResourceStatusText = (status: string) => {
    switch (status) {
      case 'active':
        return '已发布';
      case 'pending':
        return '审核中';
      case 'rejected':
        return '已拒绝';
      case 'draft':
        return '草稿';
      default:
        return status;
    }
  };

  // 计算统计数据
  const totalRevenue = mockResources.reduce(
    (sum, resource) => sum + resource.revenue,
    0
  );
  const totalDownloads = mockResources.reduce(
    (sum, resource) => sum + resource.downloads,
    0
  );
  const totalLikes = mockResources.reduce(
    (sum, resource) => sum + resource.likes,
    0
  );
  const activeResources = mockResources.filter(
    r => r.status === 'active'
  ).length;

  return (
    <Container maxWidth='lg' sx={{ py: 4 }}>
      {/* 页面标题和用户信息 */}
      <Box sx={{ mb: 4 }}>
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            mb: 2,
          }}
        >
          <Typography variant='h4' component='h1' fontWeight='bold'>
            个人中心
          </Typography>
          <Button
            variant='outlined'
            startIcon={<ExitToApp />}
            onClick={handleLogout}
            color='inherit'
          >
            退出登录
          </Button>
        </Box>

        <Paper sx={{ p: 3 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 3 }}>
            <Avatar
              sx={{
                width: 80,
                height: 80,
                bgcolor: 'primary.main',
                fontSize: '2rem',
              }}
            >
              <AccountCircle sx={{ fontSize: '3rem' }} />
            </Avatar>

            <Box sx={{ flex: 1 }}>
              <Box
                sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 1 }}
              >
                <Typography variant='h5' fontWeight='bold'>
                  {mockUser.displayName}
                </Typography>
                <Button size='small' startIcon={<Edit />}>
                  编辑
                </Button>
              </Box>

              <Box sx={{ display: 'flex', gap: 1, mb: 1 }}>
                <Chip label='个人创作者' color='primary' size='small' />
                <Chip label='活跃' color='success' size='small' />
                <Chip label='邮箱已验证' color='success' size='small' />
              </Box>

              <Typography variant='body2' color='text.secondary'>
                {mockUser.email}
              </Typography>

              <Typography variant='body2' sx={{ mt: 1 }}>
                {mockUser.bio}
              </Typography>
            </Box>
          </Box>
        </Paper>
      </Box>

      {/* 统计数据 */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <AttachMoney color='primary' />
                <Box>
                  <Typography variant='h6' fontWeight='bold'>
                    ${totalRevenue.toFixed(2)}
                  </Typography>
                  <Typography variant='body2' color='text.secondary'>
                    总收入
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <CloudUpload color='info' />
                <Box>
                  <Typography variant='h6' fontWeight='bold'>
                    {activeResources}
                  </Typography>
                  <Typography variant='body2' color='text.secondary'>
                    发布资源
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <Download color='success' />
                <Box>
                  <Typography variant='h6' fontWeight='bold'>
                    {totalDownloads}
                  </Typography>
                  <Typography variant='body2' color='text.secondary'>
                    总下载量
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <Favorite color='error' />
                <Box>
                  <Typography variant='h6' fontWeight='bold'>
                    {totalLikes}
                  </Typography>
                  <Typography variant='body2' color='text.secondary'>
                    获得点赞
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Tab导航和内容 */}
      <Paper sx={{ mb: 4 }}>
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs
            value={tabValue}
            onChange={handleTabChange}
            aria-label='个人中心导航'
          >
            <Tab label='我的资源' />
            <Tab label='收入明细' />
            <Tab label='下载记录' />
            <Tab label='账户设置' />
          </Tabs>
        </Box>

        {/* 我的资源 */}
        <TabPanel value={tabValue} index={0}>
          <Box
            sx={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              mb: 3,
            }}
          >
            <Typography variant='h6' fontWeight='bold'>
              我的资源 ({mockResources.length})
            </Typography>
            <Button variant='contained' startIcon={<CloudUpload />}>
              上传新资源
            </Button>
          </Box>

          <List>
            {mockResources.map(resource => (
              <Card key={resource.id} sx={{ mb: 2 }}>
                <CardContent>
                  <ListItem sx={{ px: 0 }}>
                    <ListItemAvatar>
                      <Avatar
                        variant='rounded'
                        sx={{ width: 60, height: 60, bgcolor: 'grey.200' }}
                      >
                        <CloudUpload />
                      </Avatar>
                    </ListItemAvatar>

                    <ListItemText
                      primary={
                        <Box
                          sx={{ display: 'flex', alignItems: 'center', gap: 1 }}
                        >
                          <Typography variant='subtitle1' fontWeight='bold'>
                            {resource.title}
                          </Typography>
                          <Chip
                            label={getResourceStatusText(resource.status)}
                            color={
                              getResourceStatusColor(resource.status) as any
                            }
                            size='small'
                          />
                        </Box>
                      }
                      secondary={
                        <Box sx={{ mt: 1 }}>
                          <Typography variant='body2' color='text.secondary'>
                            分类: {resource.category} | 价格: ${resource.price}{' '}
                            | 创建时间: {resource.createdAt}
                          </Typography>
                          <Box sx={{ display: 'flex', gap: 3, mt: 1 }}>
                            <Box
                              sx={{
                                display: 'flex',
                                alignItems: 'center',
                                gap: 0.5,
                              }}
                            >
                              <Download fontSize='small' color='action' />
                              <Typography variant='body2'>
                                {resource.downloads}
                              </Typography>
                            </Box>
                            <Box
                              sx={{
                                display: 'flex',
                                alignItems: 'center',
                                gap: 0.5,
                              }}
                            >
                              <Favorite fontSize='small' color='action' />
                              <Typography variant='body2'>
                                {resource.likes}
                              </Typography>
                            </Box>
                            <Box
                              sx={{
                                display: 'flex',
                                alignItems: 'center',
                                gap: 0.5,
                              }}
                            >
                              <AttachMoney fontSize='small' color='action' />
                              <Typography variant='body2'>
                                ${resource.revenue.toFixed(2)}
                              </Typography>
                            </Box>
                          </Box>
                        </Box>
                      }
                    />
                  </ListItem>
                </CardContent>
              </Card>
            ))}
          </List>
        </TabPanel>

        {/* 其他Tab内容 */}
        <TabPanel value={tabValue} index={1}>
          <Typography variant='h6' fontWeight='bold' gutterBottom>
            收入明细
          </Typography>
          <Alert severity='info'>收入明细功能正在开发中，敬请期待。</Alert>
        </TabPanel>

        <TabPanel value={tabValue} index={2}>
          <Typography variant='h6' fontWeight='bold' gutterBottom>
            下载记录
          </Typography>
          <Alert severity='info'>下载记录功能正在开发中，敬请期待。</Alert>
        </TabPanel>

        <TabPanel value={tabValue} index={3}>
          <Typography variant='h6' fontWeight='bold' gutterBottom>
            账户设置
          </Typography>
          <Alert severity='info'>账户设置功能正在开发中，敬请期待。</Alert>
        </TabPanel>
      </Paper>

      {/* 快速操作 */}
      <Paper sx={{ p: 3 }}>
        <Typography variant='h6' fontWeight='bold' gutterBottom>
          快速操作
        </Typography>
        <Divider sx={{ mb: 3 }} />

        <Grid container spacing={2}>
          <Grid item xs={12} sm={6} md={3}>
            <Button
              fullWidth
              variant='contained'
              startIcon={<CloudUpload />}
              size='large'
              sx={{ py: 2 }}
            >
              上传资源
            </Button>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Button
              fullWidth
              variant='outlined'
              startIcon={<Assessment />}
              size='large'
              sx={{ py: 2 }}
            >
              数据分析
            </Button>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Button
              fullWidth
              variant='outlined'
              startIcon={<AttachMoney />}
              size='large'
              sx={{ py: 2 }}
            >
              财务管理
            </Button>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Button
              fullWidth
              variant='outlined'
              startIcon={<Settings />}
              size='large'
              sx={{ py: 2 }}
            >
              账户设置
            </Button>
          </Grid>
        </Grid>
      </Paper>
    </Container>
  );
};

export default SimpleProfilePage;
