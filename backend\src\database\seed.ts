import bcrypt from 'bcryptjs';
import { v4 as uuidv4 } from 'uuid';
import { pool, query, transaction } from './connection';

// 创建测试用户
const createTestUsers = async (): Promise<{ [key: string]: string }> => {
  console.log('👥 Creating test users...');

  const users = {
    admin: uuidv4(),
    individual1: uuidv4(),
    individual2: uuidv4(),
    enterprise1: uuidv4(),
    enterprise2: uuidv4(),
  };

  const hashedPassword = await bcrypt.hash('password123', 10);

  const userInserts = [
    {
      id: users.admin,
      email: '<EMAIL>',
      password_hash: hashedPassword,
      user_type: 'individual',
      is_verified: true,
      points_balance: 1000,
    },
    {
      id: users.individual1,
      email: '<EMAIL>',
      password_hash: hashedPassword,
      user_type: 'individual',
      is_verified: true,
      points_balance: 500,
      total_earnings: 150.0,
      available_balance: 120.0,
      frozen_balance: 30.0,
    },
    {
      id: users.individual2,
      email: '<EMAIL>',
      password_hash: hashedPassword,
      user_type: 'individual',
      is_verified: true,
      points_balance: 300,
      total_earnings: 75.5,
      available_balance: 75.5,
    },
    {
      id: users.enterprise1,
      email: '<EMAIL>',
      password_hash: hashedPassword,
      user_type: 'enterprise',
      company_name: 'AI Innovations Inc.',
      is_verified: true,
      points_balance: 2000,
      total_earnings: 500.0,
      available_balance: 450.0,
      frozen_balance: 50.0,
    },
    {
      id: users.enterprise2,
      email: '<EMAIL>',
      password_hash: hashedPassword,
      user_type: 'enterprise',
      company_name: 'ML Solutions Ltd.',
      is_verified: false,
      points_balance: 100,
    },
  ];

  for (const user of userInserts) {
    await query(
      `
      INSERT INTO users (
        id, email, password_hash, user_type, company_name, is_verified,
        points_balance, total_earnings, available_balance, frozen_balance
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
      ON CONFLICT (email) DO NOTHING
    `,
      [
        user.id,
        user.email,
        user.password_hash,
        user.user_type,
        user.company_name || null,
        user.is_verified,
        user.points_balance || 0,
        user.total_earnings || 0,
        user.available_balance || 0,
        user.frozen_balance || 0,
      ]
    );
  }

  console.log('✅ Test users created');
  return users;
};

// 创建测试资源
const createTestResources = async (users: {
  [key: string]: string;
}): Promise<{ [key: string]: string }> => {
  console.log('📦 Creating test resources...');

  const resources = {
    model1: uuidv4(),
    lora1: uuidv4(),
    workflow1: uuidv4(),
    prompt1: uuidv4(),
    tool1: uuidv4(),
  };

  const resourceInserts = [
    {
      id: resources.model1,
      creator_id: users.individual1,
      title: 'Advanced Text Generation Model',
      description:
        'A fine-tuned GPT model for creative writing and content generation.',
      category: 'fine_tuned_models',
      tags: ['text-generation', 'creative-writing', 'gpt'],
      price_usd: 29.99,
      price_points: 2999,
      file_size: 1024000000, // 1GB
      status: 'active',
      sales_count: 15,
      download_count: 18,
      view_count: 150,
      rating_average: 4.5,
      rating_count: 12,
    },
    {
      id: resources.lora1,
      creator_id: users.enterprise1,
      title: 'Anime Style LoRA',
      description: 'High-quality LoRA for generating anime-style images.',
      category: 'lora',
      tags: ['anime', 'style-transfer', 'stable-diffusion'],
      price_usd: 9.99,
      price_points: 999,
      file_size: 50000000, // 50MB
      status: 'active',
      sales_count: 45,
      download_count: 50,
      view_count: 320,
      rating_average: 4.8,
      rating_count: 38,
    },
    {
      id: resources.workflow1,
      creator_id: users.individual2,
      title: 'ComfyUI Image Enhancement Workflow',
      description: 'Complete workflow for image upscaling and enhancement.',
      category: 'workflows',
      tags: ['comfyui', 'upscaling', 'enhancement'],
      price_usd: 15.99,
      price_points: 1599,
      file_size: 5000000, // 5MB
      status: 'active',
      sales_count: 8,
      download_count: 10,
      view_count: 85,
      rating_average: 4.2,
      rating_count: 6,
    },
    {
      id: resources.prompt1,
      creator_id: users.individual1,
      title: 'Professional Photography Prompts',
      description:
        'Collection of 100+ prompts for professional photography styles.',
      category: 'prompts',
      tags: ['photography', 'prompts', 'midjourney'],
      price_usd: 4.99,
      price_points: 499,
      file_size: 100000, // 100KB
      status: 'active',
      sales_count: 120,
      download_count: 125,
      view_count: 800,
      rating_average: 4.7,
      rating_count: 95,
    },
    {
      id: resources.tool1,
      creator_id: users.enterprise2,
      title: 'AI Model Optimizer Tool',
      description: 'Python tool for optimizing AI model performance and size.',
      category: 'tools',
      tags: ['optimization', 'python', 'performance'],
      price_usd: 19.99,
      price_points: 1999,
      file_size: 10000000, // 10MB
      status: 'pending_review',
      sales_count: 0,
      download_count: 0,
      view_count: 25,
      rating_average: 0,
      rating_count: 0,
    },
  ];

  for (const resource of resourceInserts) {
    await query(
      `
      INSERT INTO resources (
        id, creator_id, title, description, category, tags,
        price_usd, price_points, file_size, status,
        sales_count, download_count, view_count,
        rating_average, rating_count, published_at
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16)
      ON CONFLICT (id) DO NOTHING
    `,
      [
        resource.id,
        resource.creator_id,
        resource.title,
        resource.description,
        resource.category,
        resource.tags,
        resource.price_usd,
        resource.price_points,
        resource.file_size,
        resource.status,
        resource.sales_count,
        resource.download_count,
        resource.view_count,
        resource.rating_average,
        resource.rating_count,
        resource.status === 'active' ? new Date() : null,
      ]
    );
  }

  console.log('✅ Test resources created');
  return resources;
};

// 创建测试交易
const createTestTransactions = async (
  users: { [key: string]: string },
  resources: { [key: string]: string }
): Promise<void> => {
  console.log('💳 Creating test transactions...');

  const transactions = [
    {
      id: uuidv4(),
      buyer_id: users.individual2,
      seller_id: users.individual1,
      resource_id: resources.model1,
      amount_usd: 29.99,
      platform_fee: 1.5,
      creator_earnings: 28.49,
      payment_method: 'paypal',
      paypal_transaction_id: 'PAY-TEST-001',
      status: 'completed',
    },
    {
      id: uuidv4(),
      buyer_id: users.admin,
      seller_id: users.enterprise1,
      resource_id: resources.lora1,
      amount_usd: 9.99,
      platform_fee: 0.8,
      creator_earnings: 9.19,
      payment_method: 'points',
      points_used: 999,
      status: 'completed',
    },
  ];

  for (const transaction of transactions) {
    await query(
      `
      INSERT INTO transactions (
        id, buyer_id, seller_id, resource_id, amount_usd,
        platform_fee, creator_earnings, payment_method,
        paypal_transaction_id, points_used, status, completed_at
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12)
      ON CONFLICT (id) DO NOTHING
    `,
      [
        transaction.id,
        transaction.buyer_id,
        transaction.seller_id,
        transaction.resource_id,
        transaction.amount_usd,
        transaction.platform_fee,
        transaction.creator_earnings,
        transaction.payment_method,
        transaction.paypal_transaction_id || null,
        transaction.points_used || null,
        transaction.status,
        new Date(),
      ]
    );
  }

  console.log('✅ Test transactions created');
};

// 主种子数据函数
const seedDatabase = async (): Promise<void> => {
  try {
    console.log('🌱 Starting database seeding...');

    await transaction(async _client => {
      // 使用事务确保数据一致性
      const users = await createTestUsers();
      const resources = await createTestResources(users);
      await createTestTransactions(users, resources);
    });

    console.log('✅ Database seeding completed successfully!');
    console.log('\n📋 Test Accounts Created:');
    console.log('  Admin: <EMAIL>');
    console.log('  Individual Creator 1: <EMAIL>');
    console.log('  Individual Creator 2: <EMAIL>');
    console.log('  Enterprise 1: <EMAIL>');
    console.log('  Enterprise 2: <EMAIL>');
    console.log('  Password for all accounts: password123');
  } catch (error) {
    console.error('❌ Database seeding failed:', error);
    throw error;
  }
};

// 清理测试数据
const cleanDatabase = async (): Promise<void> => {
  try {
    console.log('🧹 Cleaning test data...');

    const tables = [
      'resource_reviews',
      'refund_requests',
      'points_transactions',
      'commission_records',
      'withdrawal_requests',
      'transactions',
      'resources',
      'users',
    ];

    for (const table of tables) {
      await query(`DELETE FROM ${table} WHERE created_at >= CURRENT_DATE`);
    }

    console.log('✅ Test data cleaned');
  } catch (error) {
    console.error('❌ Database cleaning failed:', error);
    throw error;
  }
};

// 命令行接口
const main = async (): Promise<void> => {
  const command = process.argv[2];

  try {
    switch (command) {
      case 'seed':
      case undefined:
        await seedDatabase();
        break;
      case 'clean':
        await cleanDatabase();
        break;
      default:
        console.log('Usage: npm run seed [seed|clean]');
        console.log('  seed (default) - Create test data');
        console.log('  clean          - Remove test data');
        process.exit(1);
    }
  } catch (error) {
    console.error('❌ Seed command failed:', error);
    process.exit(1);
  } finally {
    await pool.end();
  }
};

// 如果直接运行此文件
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}

export { cleanDatabase, seedDatabase };
