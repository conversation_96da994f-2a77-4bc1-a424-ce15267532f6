import jwt from 'jsonwebtoken';
import { JWTPayload } from '@/types';

const JWT_SECRET = process.env.JWT_SECRET || 'your-super-secret-jwt-key';
const JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || '7d';

// 生成JWT令牌
export function generateToken(payload: JWTPayload): string {
  return jwt.sign(payload, JWT_SECRET, {
    expiresIn: JWT_EXPIRES_IN,
    issuer: 'aigc-service-hub',
    audience: 'aigc-service-hub-users'
  });
}

// 验证JWT令牌
export function verifyToken(token: string): JWTPayload | null {
  try {
    const decoded = jwt.verify(token, JWT_SECRET, {
      issuer: 'aigc-service-hub',
      audience: 'aigc-service-hub-users'
    }) as JWTPayload;
    
    return decoded;
  } catch (error) {
    console.error('JWT verification failed:', error);
    return null;
  }
}

// 解码JWT令牌（不验证签名，用于获取过期令牌的信息）
export function decodeToken(token: string): JWTPayload | null {
  try {
    const decoded = jwt.decode(token) as JWTPayload;
    return decoded;
  } catch (error) {
    console.error('JWT decode failed:', error);
    return null;
  }
}

// 生成刷新令牌
export function generateRefreshToken(payload: JWTPayload): string {
  return jwt.sign(payload, JWT_SECRET, {
    expiresIn: '30d', // 刷新令牌有效期30天
    issuer: 'aigc-service-hub',
    audience: 'aigc-service-hub-refresh'
  });
}

// 验证刷新令牌
export function verifyRefreshToken(token: string): JWTPayload | null {
  try {
    const decoded = jwt.verify(token, JWT_SECRET, {
      issuer: 'aigc-service-hub',
      audience: 'aigc-service-hub-refresh'
    }) as JWTPayload;
    
    return decoded;
  } catch (error) {
    console.error('Refresh token verification failed:', error);
    return null;
  }
}

// 检查令牌是否即将过期（在30分钟内过期）
export function isTokenExpiringSoon(token: string): boolean {
  try {
    const decoded = jwt.decode(token) as any;
    if (!decoded || !decoded.exp) {
      return true;
    }
    
    const expirationTime = decoded.exp * 1000; // 转换为毫秒
    const currentTime = Date.now();
    const thirtyMinutes = 30 * 60 * 1000; // 30分钟的毫秒数
    
    return (expirationTime - currentTime) < thirtyMinutes;
  } catch (error) {
    return true;
  }
}
