import axios from 'axios';
import type { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import type { ApiResponse } from '@/types/index';

// API基础配置
const API_BASE_URL =
  import.meta.env.VITE_API_BASE_URL || 'http://localhost:3000/api';

// 创建axios实例
const apiClient: AxiosInstance = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000, // 30秒超时
  headers: {
    'Content-Type': 'application/json',
  },
});

// 请求拦截器 - 添加认证token
apiClient.interceptors.request.use(
  config => {
    const token = localStorage.getItem('authToken');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  error => {
    return Promise.reject(error);
  }
);

// 响应拦截器 - 统一处理响应和错误
apiClient.interceptors.response.use(
  (response: AxiosResponse<ApiResponse>) => {
    return response;
  },
  error => {
    // 处理401未授权错误
    if (error.response?.status === 401) {
      localStorage.removeItem('authToken');
      localStorage.removeItem('user');
      window.location.href = '/login';
    }

    // 处理网络错误
    if (!error.response) {
      console.error('Network Error:', error.message);
      return Promise.reject({
        success: false,
        error: {
          code: 'NETWORK_ERROR',
          message: '网络连接失败，请检查网络设置',
        },
      });
    }

    return Promise.reject(error.response.data);
  }
);

// API请求方法封装
export const api = {
  // GET请求
  get: async <T = any>(
    url: string,
    config?: AxiosRequestConfig
  ): Promise<ApiResponse<T>> => {
    const response = await apiClient.get<ApiResponse<T>>(url, config);
    return response.data;
  },

  // POST请求
  post: async <T = any>(
    url: string,
    data?: any,
    config?: AxiosRequestConfig
  ): Promise<ApiResponse<T>> => {
    const response = await apiClient.post<ApiResponse<T>>(url, data, config);
    return response.data;
  },

  // PUT请求
  put: async <T = any>(
    url: string,
    data?: any,
    config?: AxiosRequestConfig
  ): Promise<ApiResponse<T>> => {
    const response = await apiClient.put<ApiResponse<T>>(url, data, config);
    return response.data;
  },

  // DELETE请求
  delete: async <T = any>(
    url: string,
    config?: AxiosRequestConfig
  ): Promise<ApiResponse<T>> => {
    const response = await apiClient.delete<ApiResponse<T>>(url, config);
    return response.data;
  },

  // 文件上传请求
  upload: async <T = any>(
    url: string,
    formData: FormData,
    onProgress?: (progress: number) => void
  ): Promise<ApiResponse<T>> => {
    const response = await apiClient.post<ApiResponse<T>>(url, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      onUploadProgress: progressEvent => {
        if (onProgress && progressEvent.total) {
          const progress = Math.round(
            (progressEvent.loaded * 100) / progressEvent.total
          );
          onProgress(progress);
        }
      },
    });
    return response.data;
  },

  // 下载文件
  download: async (url: string, filename?: string): Promise<void> => {
    const response = await apiClient.get(url, {
      responseType: 'blob',
    });

    const blob = new Blob([response.data]);
    const downloadUrl = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = downloadUrl;
    link.download = filename || 'download';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(downloadUrl);
  },
};

// 认证相关API
export const authApi = {
  // 用户注册
  register: (data: any) => api.post('/auth/register', data),

  // 用户登录
  login: (data: any) => api.post('/auth/login', data),

  // 用户登出
  logout: () => api.post('/auth/logout'),

  // 获取用户信息
  getProfile: () => api.get('/auth/profile'),

  // 更新用户信息
  updateProfile: (data: any) => api.put('/auth/profile', data),

  // 邮箱验证
  verifyEmail: (token: string) => api.post('/auth/verify-email', { token }),

  // 重发验证邮件
  resendVerification: () => api.post('/auth/resend-verification'),
};

// 资源相关API
export const resourceApi = {
  // 获取资源列表
  getResources: (params?: any) => api.get('/resources', { params }),

  // 获取资源详情
  getResource: (id: string) => api.get(`/resources/${id}`),

  // 创建资源
  createResource: (data: any) => api.post('/resources', data),

  // 更新资源
  updateResource: (id: string, data: any) => api.put(`/resources/${id}`, data),

  // 删除资源
  deleteResource: (id: string) => api.delete(`/resources/${id}`),

  // 上传资源文件
  uploadFile: (
    id: string,
    formData: FormData,
    onProgress?: (progress: number) => void
  ) => api.upload(`/resources/${id}/upload`, formData, onProgress),

  // 搜索资源
  searchResources: (query: string, filters?: any) =>
    api.get('/resources/search', { params: { q: query, ...filters } }),
};

// 交易相关API
export const transactionApi = {
  // 创建交易
  createTransaction: (data: any) => api.post('/transactions', data),

  // 获取交易详情
  getTransaction: (id: string) => api.get(`/transactions/${id}`),

  // 确认支付
  confirmPayment: (id: string, data: any) =>
    api.post(`/transactions/${id}/confirm`, data),

  // 获取下载链接
  getDownloadLink: (id: string) => api.get(`/transactions/${id}/download`),

  // 获取用户交易历史
  getUserTransactions: (params?: any) => api.get('/transactions', { params }),
};

// 财务相关API
export const financeApi = {
  // 获取账户余额
  getBalance: () => api.get('/finance/balance'),

  // 申请提现
  requestWithdrawal: (data: any) => api.post('/finance/withdraw', data),

  // 获取财务记录
  getTransactions: (params?: any) =>
    api.get('/finance/transactions', { params }),

  // 获取提现记录
  getWithdrawals: (params?: any) => api.get('/finance/withdrawals', { params }),
};

// 积分相关API
export const pointsApi = {
  // 获取积分余额
  getBalance: () => api.get('/points/balance'),

  // 每日签到
  dailyCheckin: () => api.post('/points/checkin'),

  // 获取积分历史
  getHistory: (params?: any) => api.get('/points/history', { params }),

  // 获取积分规则
  getRules: () => api.get('/points/rules'),
};

// 排名相关API
export const rankingApi = {
  // 获取创作者排名
  getCreatorRanking: (period: 'week' | 'month' | 'all_time' = 'week') =>
    api.get('/ranking/creators', { params: { period } }),

  // 获取热门资源
  getPopularResources: (params?: any) =>
    api.get('/ranking/resources', { params }),
};

export default api;
