import app from './app';
import { config } from '@config/index';
import { testConnection, closeConnection } from '@database/connection';

// 启动服务器
const startServer = async (): Promise<void> => {
  try {
    // 测试数据库连接
    console.log('Testing database connection...');
    const dbConnected = await testConnection();

    if (!dbConnected) {
      console.warn(
        '⚠️  Database connection failed. Starting server without database...'
      );
      console.warn(
        '⚠️  Some features will not work properly without database connection.'
      );
    } else {
      console.log('✅ Database connection successful');
    }

    // 启动HTTP服务器
    const server = app.listen(config.port, () => {
      console.log(`
🚀 AIGC Service Hub API Server Started!
📍 Environment: ${config.nodeEnv}
🌐 Server running on: http://localhost:${config.port}
📊 API endpoints: http://localhost:${config.port}/api
🏥 Health check: http://localhost:${config.port}/api/health
📝 Logs: ${config.nodeEnv === 'development' ? 'Console' : 'File'}
⏰ Started at: ${new Date().toISOString()}
      `);
    });

    // 优雅关闭处理
    const gracefulShutdown = async (signal: string): Promise<void> => {
      console.log(`\n${signal} received. Starting graceful shutdown...`);

      // 停止接受新连接
      server.close(async () => {
        console.log('HTTP server closed');

        // 关闭数据库连接
        await closeConnection();

        console.log('Graceful shutdown completed');
        process.exit(0);
      });

      // 强制退出超时
      setTimeout(() => {
        console.error('Forced shutdown due to timeout');
        process.exit(1);
      }, 10000);
    };

    // 监听退出信号
    process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
    process.on('SIGINT', () => gracefulShutdown('SIGINT'));

    // 监听未捕获的异常
    process.on('uncaughtException', (error: Error) => {
      console.error('Uncaught Exception:', error);
      gracefulShutdown('UNCAUGHT_EXCEPTION');
    });

    process.on('unhandledRejection', (reason: any, promise: Promise<any>) => {
      console.error('Unhandled Rejection at:', promise, 'reason:', reason);
      gracefulShutdown('UNHANDLED_REJECTION');
    });
  } catch (error) {
    console.error('Failed to start server:', error);
    process.exit(1);
  }
};

// 启动应用
startServer();
