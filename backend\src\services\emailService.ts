import { verifyEmailConfig, sendVerificationEmail, sendPasswordResetEmail, sendWelcomeEmail } from '@/utils/email';

export class EmailService {
  private static initialized = false;

  // 初始化邮件服务
  static async initialize(): Promise<boolean> {
    if (this.initialized) {
      return true;
    }

    try {
      const isValid = await verifyEmailConfig();
      if (isValid) {
        this.initialized = true;
        console.log('✅ Email service initialized successfully');
        return true;
      } else {
        console.error('❌ Email service initialization failed');
        return false;
      }
    } catch (error) {
      console.error('❌ Email service initialization error:', error);
      return false;
    }
  }

  // 检查邮件服务是否可用
  static isAvailable(): boolean {
    return this.initialized;
  }

  // 发送邮箱验证邮件
  static async sendVerification(email: string, token: string): Promise<boolean> {
    if (!this.initialized) {
      console.warn('Email service not initialized, skipping verification email');
      return false;
    }

    try {
      await sendVerificationEmail(email, token);
      console.log(`✅ Verification email sent to ${email}`);
      return true;
    } catch (error) {
      console.error(`❌ Failed to send verification email to ${email}:`, error);
      return false;
    }
  }

  // 发送密码重置邮件
  static async sendPasswordReset(email: string, token: string): Promise<boolean> {
    if (!this.initialized) {
      console.warn('Email service not initialized, skipping password reset email');
      return false;
    }

    try {
      await sendPasswordResetEmail(email, token);
      console.log(`✅ Password reset email sent to ${email}`);
      return true;
    } catch (error) {
      console.error(`❌ Failed to send password reset email to ${email}:`, error);
      return false;
    }
  }

  // 发送欢迎邮件
  static async sendWelcome(email: string, username: string): Promise<boolean> {
    if (!this.initialized) {
      console.warn('Email service not initialized, skipping welcome email');
      return false;
    }

    try {
      await sendWelcomeEmail(email, username);
      console.log(`✅ Welcome email sent to ${email}`);
      return true;
    } catch (error) {
      console.error(`❌ Failed to send welcome email to ${email}:`, error);
      return false;
    }
  }

  // 批量发送邮件（用于营销邮件等）
  static async sendBulkEmail(
    emails: string[], 
    subject: string, 
    htmlContent: string, 
    textContent?: string
  ): Promise<{ success: string[], failed: string[] }> {
    if (!this.initialized) {
      console.warn('Email service not initialized, skipping bulk email');
      return { success: [], failed: emails };
    }

    const success: string[] = [];
    const failed: string[] = [];

    for (const email of emails) {
      try {
        // 这里需要实现批量邮件发送逻辑
        // 为了避免被标记为垃圾邮件，应该添加延迟
        await new Promise(resolve => setTimeout(resolve, 100));
        
        // TODO: 实现批量邮件发送
        console.log(`Bulk email would be sent to ${email}`);
        success.push(email);
      } catch (error) {
        console.error(`Failed to send bulk email to ${email}:`, error);
        failed.push(email);
      }
    }

    return { success, failed };
  }

  // 获取邮件发送统计
  static getStats(): {
    initialized: boolean;
    totalSent: number;
    totalFailed: number;
  } {
    // TODO: 实现邮件发送统计
    return {
      initialized: this.initialized,
      totalSent: 0,
      totalFailed: 0
    };
  }
}
