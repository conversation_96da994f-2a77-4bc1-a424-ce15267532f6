# EditorConfig is awesome: https://EditorConfig.org

# top-most EditorConfig file
root = true

# All files
[*]
charset = utf-8
end_of_line = lf
insert_final_newline = true
trim_trailing_whitespace = true
indent_style = space
indent_size = 2

# JavaScript, TypeScript, JSON files
[*.{js,jsx,ts,tsx,json,jsonc}]
indent_size = 2

# Python files
[*.py]
indent_size = 4

# Markdown files
[*.md]
trim_trailing_whitespace = false

# YAML files
[*.{yml,yaml}]
indent_size = 2

# Shell scripts
[*.sh]
indent_size = 2

# SQL files
[*.sql]
indent_size = 2

# Docker files
[Dockerfile*]
indent_size = 2

# Configuration files
[*.{toml,ini,cfg}]
indent_size = 2
