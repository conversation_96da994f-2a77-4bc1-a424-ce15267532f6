import React from 'react';
import {
  Box,
  Container,
  Typography,
  Paper,
  Grid,
  Card,
  CardContent,
  Button,
  Avatar,
  Chip,
  Divider,
} from '@mui/material';
import {
  AccountCircle,
  CloudUpload,
  AttachMoney,
  Download,
  Favorite,
  TrendingUp,
  ExitToApp,
} from '@mui/icons-material';
import { useAuth } from '../hooks/useAuth';
import { useNavigate } from 'react-router-dom';

const DashboardPage: React.FC = () => {
  const { user, logout } = useAuth();
  const navigate = useNavigate();

  const handleLogout = () => {
    logout();
    navigate('/');
  };

  if (!user) {
    return null;
  }

  // 获取用户显示名称
  const getDisplayName = () => {
    if (user.userType === 'enterprise') {
      return user.companyName || user.username;
    }
    return user.displayName || `${user.firstName} ${user.lastName}` || user.username;
  };

  // 获取用户状态颜色
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'success';
      case 'pending':
        return 'warning';
      case 'suspended':
        return 'error';
      case 'banned':
        return 'error';
      default:
        return 'default';
    }
  };

  // 获取用户状态文本
  const getStatusText = (status: string) => {
    switch (status) {
      case 'active':
        return '活跃';
      case 'pending':
        return '待验证';
      case 'suspended':
        return '已暂停';
      case 'banned':
        return '已封禁';
      default:
        return status;
    }
  };

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      {/* 页面标题和用户信息 */}
      <Box sx={{ mb: 4 }}>
        <Box sx={{ display: 'flex', justifyContent: 'between', alignItems: 'center', mb: 2 }}>
          <Typography variant="h4" component="h1" fontWeight="bold">
            个人中心
          </Typography>
          <Button
            variant="outlined"
            startIcon={<ExitToApp />}
            onClick={handleLogout}
            color="inherit"
          >
            退出登录
          </Button>
        </Box>
        
        <Paper sx={{ p: 3 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 3 }}>
            <Avatar
              sx={{
                width: 80,
                height: 80,
                bgcolor: 'primary.main',
                fontSize: '2rem',
              }}
            >
              {user.avatar ? (
                <img src={user.avatar} alt="Avatar" style={{ width: '100%', height: '100%' }} />
              ) : (
                <AccountCircle sx={{ fontSize: '3rem' }} />
              )}
            </Avatar>
            
            <Box sx={{ flex: 1 }}>
              <Typography variant="h5" fontWeight="bold" gutterBottom>
                {getDisplayName()}
              </Typography>
              
              <Box sx={{ display: 'flex', gap: 1, mb: 1 }}>
                <Chip
                  label={user.userType === 'individual' ? '个人创作者' : '企业创作者'}
                  color="primary"
                  size="small"
                />
                <Chip
                  label={getStatusText(user.status)}
                  color={getStatusColor(user.status) as any}
                  size="small"
                />
                {user.emailVerified && (
                  <Chip
                    label="邮箱已验证"
                    color="success"
                    size="small"
                  />
                )}
              </Box>
              
              <Typography variant="body2" color="text.secondary">
                {user.email}
              </Typography>
              
              {user.bio && (
                <Typography variant="body2" sx={{ mt: 1 }}>
                  {user.bio}
                </Typography>
              )}
            </Box>
          </Box>
        </Paper>
      </Box>

      {/* 统计数据 */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <AttachMoney color="primary" />
                <Box>
                  <Typography variant="h6" fontWeight="bold">
                    ${user.totalEarnings?.toFixed(2) || '0.00'}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    总收入
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <TrendingUp color="success" />
                <Box>
                  <Typography variant="h6" fontWeight="bold">
                    {user.totalSales || 0}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    总销量
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <Download color="info" />
                <Box>
                  <Typography variant="h6" fontWeight="bold">
                    {user.totalDownloads || 0}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    总下载量
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <Favorite color="error" />
                <Box>
                  <Typography variant="h6" fontWeight="bold">
                    {user.totalLikes || 0}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    获得点赞
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* 快速操作 */}
      <Paper sx={{ p: 3 }}>
        <Typography variant="h6" fontWeight="bold" gutterBottom>
          快速操作
        </Typography>
        <Divider sx={{ mb: 3 }} />
        
        <Grid container spacing={2}>
          <Grid item xs={12} sm={6} md={4}>
            <Button
              fullWidth
              variant="contained"
              startIcon={<CloudUpload />}
              size="large"
              sx={{ py: 2 }}
              onClick={() => navigate('/upload')}
            >
              上传资源
            </Button>
          </Grid>
          
          <Grid item xs={12} sm={6} md={4}>
            <Button
              fullWidth
              variant="outlined"
              startIcon={<AccountCircle />}
              size="large"
              sx={{ py: 2 }}
              onClick={() => navigate('/profile')}
            >
              编辑资料
            </Button>
          </Grid>
          
          <Grid item xs={12} sm={6} md={4}>
            <Button
              fullWidth
              variant="outlined"
              startIcon={<AttachMoney />}
              size="large"
              sx={{ py: 2 }}
              onClick={() => navigate('/finances')}
            >
              财务管理
            </Button>
          </Grid>
        </Grid>
      </Paper>
    </Container>
  );
};

export default DashboardPage;
