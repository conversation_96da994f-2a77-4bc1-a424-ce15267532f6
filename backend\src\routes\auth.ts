import { AuthController } from '@/controllers/authController';
import { asyncHandler } from '@middleware/errorHandler';
import { authRateLimiter } from '@middleware/rateLimiter';
import { Request, Response, Router } from 'express';

const router = Router();

// 应用认证限流
router.use(authRateLimiter);

// 用户注册
router.post('/register', asyncHandler(AuthController.register));

// 用户登录
router.post('/login', asyncHandler(AuthController.login));

// 用户登出
router.post(
  '/logout',
  asyncHandler(async (_req: Request, res: Response) => {
    // TODO: 实现用户登出逻辑
    res.json({
      success: true,
      message: 'User logout endpoint - Coming soon',
      data: {
        endpoint: 'POST /api/auth/logout',
        status: 'under_development',
      },
      timestamp: new Date().toISOString(),
    });
  })
);

// 获取用户信息
router.get(
  '/profile',
  asyncHandler(async (_req: Request, res: Response) => {
    // TODO: 实现获取用户信息逻辑
    res.json({
      success: true,
      message: 'Get user profile endpoint - Coming soon',
      data: {
        endpoint: 'GET /api/auth/profile',
        status: 'under_development',
      },
      timestamp: new Date().toISOString(),
    });
  })
);

// 更新用户信息
router.put(
  '/profile',
  asyncHandler(async (_req: Request, res: Response) => {
    // TODO: 实现更新用户信息逻辑
    res.json({
      success: true,
      message: 'Update user profile endpoint - Coming soon',
      data: {
        endpoint: 'PUT /api/auth/profile',
        status: 'under_development',
      },
      timestamp: new Date().toISOString(),
    });
  })
);

// 邮箱验证
router.get('/verify-email/:token', asyncHandler(AuthController.verifyEmail));

// 重发验证邮件
router.post(
  '/resend-verification',
  asyncHandler(AuthController.resendVerificationEmail)
);

export { router as authRoutes };
