import { Login } from '@mui/icons-material';
import {
  AppB<PERSON>,
  Box,
  <PERSON><PERSON>,
  Card,
  CardContent,
  Container,
  Grid,
  Toolbar,
  Typography,
} from '@mui/material';
import React from 'react';

const SimpleHomePage: React.FC = () => {
  const handleLoginClick = () => {
    window.location.href = '/login';
  };

  return (
    <Box sx={{ flexGrow: 1 }}>
      {/* 顶部导航栏 */}
      <AppBar position='static' elevation={1}>
        <Toolbar>
          <Typography
            variant='h6'
            component='div'
            sx={{ flexGrow: 1, fontWeight: 600 }}
          >
            AIGC Service Hub
          </Typography>
          <Button
            color='inherit'
            startIcon={<Login />}
            onClick={handleLoginClick}
          >
            登录
          </Button>
        </Toolbar>
      </AppBar>

      {/* 主要内容区域 */}
      <Container maxWidth='lg' sx={{ mt: 4, mb: 4 }}>
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            textAlign: 'center',
            py: 8,
          }}
        >
          <Typography
            variant='h2'
            component='h1'
            gutterBottom
            sx={{
              background: 'linear-gradient(45deg, #1976d2, #42a5f5)',
              backgroundClip: 'text',
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent',
              fontWeight: 'bold',
            }}
          >
            欢迎来到 AIGC Service Hub
          </Typography>

          <Typography
            variant='h5'
            component='h2'
            gutterBottom
            color='text.secondary'
            sx={{ mb: 4 }}
          >
            全球领先的AI创作者服务平台
          </Typography>

          <Typography
            variant='body1'
            color='text.secondary'
            sx={{ maxWidth: 600, mb: 4 }}
          >
            为AI创作者提供一个自由、高效的资源交易与共享空间，支持微调模型、LoRA、工作流、提示词等AI资源的上传与交易。
          </Typography>

          {/* 功能特性展示 */}
          <Grid container spacing={3} sx={{ mt: 4, mb: 4 }}>
            {[
              { icon: '🤖', title: 'AI模型交易', desc: '上传和交易各种AI模型' },
              { icon: '💰', title: '智能分佣系统', desc: '公平的收益分配机制' },
              { icon: '🔒', title: '安全支付保障', desc: 'PayPal安全支付保护' },
              { icon: '📊', title: '数据统计分析', desc: '详细的销售数据分析' },
              { icon: '🎮', title: '游戏化积分', desc: '签到获得积分奖励' },
              { icon: '🏆', title: '创作者排名', desc: '展示优秀创作者' },
            ].map(feature => (
              <Grid item xs={12} sm={6} md={4} key={feature.title}>
                <Card sx={{ height: '100%', textAlign: 'center' }}>
                  <CardContent>
                    <Typography variant='h3' sx={{ mb: 2 }}>
                      {feature.icon}
                    </Typography>
                    <Typography variant='h6' gutterBottom>
                      {feature.title}
                    </Typography>
                    <Typography variant='body2' color='text.secondary'>
                      {feature.desc}
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>

          {/* 行动按钮 */}
          <Box sx={{ display: 'flex', gap: 2, mt: 4 }}>
            <Button
              variant='contained'
              size='large'
              onClick={handleLoginClick}
              sx={{
                px: 4,
                py: 1.5,
                background: 'linear-gradient(45deg, #1976d2, #42a5f5)',
                '&:hover': {
                  background: 'linear-gradient(45deg, #1565c0, #1976d2)',
                },
              }}
            >
              开始使用
            </Button>

            <Button variant='outlined' size='large' sx={{ px: 4, py: 1.5 }}>
              了解更多
            </Button>
          </Box>

          {/* 开发状态提示 */}
          <Box
            sx={{
              mt: 6,
              p: 3,
              bgcolor: 'info.main',
              color: 'info.contrastText',
              borderRadius: 2,
              maxWidth: 500,
            }}
          >
            <Typography variant='h6' gutterBottom>
              🚧 开发中
            </Typography>
            <Typography variant='body2'>
              前端开发环境已配置完成！
              <br />
              React + Vite + Material-UI + TypeScript 技术栈就绪
              <br />
              用户认证系统已实现
            </Typography>
          </Box>
        </Box>
      </Container>
    </Box>
  );
};

export default SimpleHomePage;
