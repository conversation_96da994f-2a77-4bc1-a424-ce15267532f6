import dotenv from 'dotenv';
import { afterEach, beforeEach, vi } from 'vitest';

// 加载测试环境变量
dotenv.config({ path: '.env' });

// Mock console methods for cleaner test output
const originalConsole = { ...console };

beforeEach(() => {
  // 在测试中静默console输出
  vi.spyOn(console, 'log').mockImplementation(() => {});
  vi.spyOn(console, 'warn').mockImplementation(() => {});
  vi.spyOn(console, 'error').mockImplementation(() => {});
});

afterEach(() => {
  // 恢复console方法
  vi.restoreAllMocks();
});

// 全局测试工具
global.console = originalConsole;

// Mock数据库连接
vi.mock('@/database/connection', () => ({
  pool: {
    query: vi.fn(),
    connect: vi.fn(),
    end: vi.fn(),
  },
  testConnection: vi.fn().mockResolvedValue(false),
  query: vi.fn(),
  transaction: vi.fn(),
  closeConnection: vi.fn(),
  healthCheck: vi.fn().mockResolvedValue({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    latency: 10,
  }),
}));

// 设置测试环境变量
process.env.NODE_ENV = 'test';
process.env.PORT = '3001';
process.env.JWT_SECRET = 'test_jwt_secret_for_testing_only';
process.env.DATABASE_URL = 'postgresql://test:test@localhost:5432/test_db';
process.env.CORS_ORIGIN = 'http://localhost:5173';

export {};
