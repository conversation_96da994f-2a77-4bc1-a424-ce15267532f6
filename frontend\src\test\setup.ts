import '@testing-library/jest-dom';
import { vi } from 'vitest';

// Mock localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
};

Object.defineProperty(window, 'localStorage', {
  value: localStorageMock,
});

// Mock sessionStorage
const sessionStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
};

Object.defineProperty(window, 'sessionStorage', {
  value: sessionStorageMock,
});

// Mock window.matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: vi.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: vi.fn(), // deprecated
    removeListener: vi.fn(), // deprecated
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    dispatchEvent: vi.fn(),
  })),
});

// Mock IntersectionObserver
global.IntersectionObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}));

// Mock ResizeObserver
global.ResizeObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}));

// Mock URL.createObjectURL
Object.defineProperty(URL, 'createObjectURL', {
  writable: true,
  value: vi.fn(() => 'mocked-url'),
});

Object.defineProperty(URL, 'revokeObjectURL', {
  writable: true,
  value: vi.fn(),
});

// Mock File and FileReader
global.File = vi.fn().mockImplementation((chunks, filename, options) => ({
  name: filename,
  size: chunks.reduce((acc: number, chunk: any) => acc + chunk.length, 0),
  type: options?.type || '',
  lastModified: Date.now(),
}));

global.FileReader = class MockFileReader {
  static readonly EMPTY = 0;
  static readonly LOADING = 1;
  static readonly DONE = 2;

  readAsDataURL = vi.fn();
  readAsText = vi.fn();
  readAsArrayBuffer = vi.fn();
  result = null;
  error = null;
  onload = null;
  onerror = null;
  onprogress = null;
} as any;

// Mock environment variables
vi.mock('import.meta', () => ({
  env: {
    VITE_API_BASE_URL: 'http://localhost:3000/api',
    VITE_APP_NAME: 'AIGC Service Hub',
    VITE_APP_VERSION: '1.0.0-test',
    VITE_PAYPAL_CLIENT_ID: 'test_paypal_client_id',
    VITE_PAYPAL_MODE: 'sandbox',
  },
}));

// Global test utilities
export const mockUser = {
  id: 'test-user-id',
  email: '<EMAIL>',
  userType: 'individual' as const,
  isVerified: true,
  totalEarnings: 0,
  availableBalance: 0,
  frozenBalance: 0,
  pointsBalance: 100,
  createdAt: '2025-01-01T00:00:00Z',
  updatedAt: '2025-01-01T00:00:00Z',
};

export const mockResource = {
  id: 'test-resource-id',
  creatorId: 'test-user-id',
  title: 'Test Resource',
  description: 'Test Description',
  category: 'fine_tuned_models' as const,
  tags: ['test', 'ai'],
  priceUsd: 10.0,
  pricePoints: 1000,
  fileUrl: 'https://example.com/file.zip',
  fileSize: 1024000,
  downloadCount: 0,
  salesCount: 0,
  status: 'active' as const,
  createdAt: '2025-01-01T00:00:00Z',
  updatedAt: '2025-01-01T00:00:00Z',
};

export const mockApiResponse = <T>(data: T) => ({
  success: true,
  data,
  timestamp: new Date().toISOString(),
});

export const mockApiError = (message: string, code = 'TEST_ERROR') => ({
  success: false,
  error: {
    code,
    message,
  },
  timestamp: new Date().toISOString(),
});
