# AIGC Service Hub 数据库设计文档

## 📋 数据库概览

本文档详细描述了AIGC Service Hub平台的数据库设计，包括表结构、关系、索引和约束。

### 🛠 技术栈

- **数据库**: PostgreSQL 14+
- **扩展**: uuid-ossp (UUID生成)
- **特性**: 触发器、视图、函数、全文搜索

## 📊 数据库表结构

### 1. 用户表 (users)

存储平台用户信息，支持个人和企业创作者。

```sql
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    user_type VARCHAR(20) NOT NULL CHECK (user_type IN ('individual', 'enterprise')),
    company_name VARCHAR(255),
    paypal_email VARCHAR(255),
    is_verified BOOLEAN DEFAULT FALSE,

    -- 财务信息
    total_earnings DECIMAL(12,2) DEFAULT 0.00,
    available_balance DECIMAL(12,2) DEFAULT 0.00,
    frozen_balance DECIMAL(12,2) DEFAULT 0.00,

    -- 积分系统
    points_balance INTEGER DEFAULT 0,
    last_checkin_date DATE,
    checkin_streak INTEGER DEFAULT 0,

    -- 统计信息
    total_uploads INTEGER DEFAULT 0,
    total_sales INTEGER DEFAULT 0,
    total_downloads INTEGER DEFAULT 0,

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

**关键特性**:

- 支持个人和企业创作者类型
- 集成财务管理（收入、余额、冻结金额）
- 内置积分系统和签到功能
- 自动更新时间戳

### 2. 资源表 (resources)

存储AI资源信息，支持5种资源类型。

```sql
CREATE TABLE resources (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    creator_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    category VARCHAR(50) NOT NULL CHECK (category IN (
        'fine_tuned_models', 'lora', 'workflows', 'prompts', 'tools'
    )),
    tags TEXT[] DEFAULT '{}',
    price_usd DECIMAL(10,2) NOT NULL CHECK (price_usd >= 0),
    price_points INTEGER NOT NULL CHECK (price_points >= 0),
    file_url VARCHAR(500),
    file_size BIGINT CHECK (file_size > 0),
    status VARCHAR(20) DEFAULT 'pending_review',

    -- 统计信息
    download_count INTEGER DEFAULT 0,
    sales_count INTEGER DEFAULT 0,
    view_count INTEGER DEFAULT 0,
    rating_average DECIMAL(3,2) DEFAULT 0.00,
    rating_count INTEGER DEFAULT 0,

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    published_at TIMESTAMP
);
```

**关键特性**:

- 支持双重定价（美元+积分）
- 全文搜索支持（标题、描述、标签）
- 自动统计和评分系统
- 状态管理和发布时间跟踪

### 3. 交易表 (transactions)

记录所有资源购买交易。

```sql
CREATE TABLE transactions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    buyer_id UUID NOT NULL REFERENCES users(id),
    seller_id UUID NOT NULL REFERENCES users(id),
    resource_id UUID NOT NULL REFERENCES resources(id),
    amount_usd DECIMAL(10,2) NOT NULL,
    platform_fee DECIMAL(10,2) NOT NULL,
    creator_earnings DECIMAL(10,2) NOT NULL,
    payment_method VARCHAR(20) NOT NULL CHECK (payment_method IN ('paypal', 'points')),
    status VARCHAR(20) DEFAULT 'pending',

    -- 下载信息
    download_url VARCHAR(500),
    download_expires_at TIMESTAMP,
    download_count INTEGER DEFAULT 0,

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP
);
```

**关键特性**:

- 支持PayPal和积分双重支付
- 自动计算平台费用和创作者收益
- 下载链接管理和过期控制
- 防止自购约束

### 4. 分佣记录表 (commission_records)

记录阶梯分佣系统的详细计算。

```sql
CREATE TABLE commission_records (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    transaction_id UUID NOT NULL REFERENCES transactions(id),
    creator_id UUID NOT NULL REFERENCES users(id),
    resource_id UUID NOT NULL REFERENCES resources(id),
    sales_sequence INTEGER NOT NULL,
    commission_rate DECIMAL(5,4) NOT NULL,
    creator_type VARCHAR(20) NOT NULL,
    initial_rate DECIMAL(5,4) NOT NULL,
    final_rate DECIMAL(5,4) NOT NULL,
    decay_factor DECIMAL(5,4) NOT NULL,

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

**关键特性**:

- 记录每次销售的分佣计算详情
- 支持个人和企业不同的分佣策略
- 阶梯衰减算法实现

### 5. 提现申请表 (withdrawal_requests)

管理用户提现申请和处理。

```sql
CREATE TABLE withdrawal_requests (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id),
    amount DECIMAL(10,2) NOT NULL,
    paypal_fee DECIMAL(10,2) DEFAULT 0.00,
    tax_fee DECIMAL(10,2) DEFAULT 0.00,
    net_amount DECIMAL(10,2) NOT NULL,
    paypal_email VARCHAR(255) NOT NULL,
    status VARCHAR(20) DEFAULT 'pending',

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    processed_at TIMESTAMP,
    completed_at TIMESTAMP
);
```

**关键特性**:

- 支持PayPal自动化提现
- 手续费和税费计算
- 状态跟踪和时间记录

### 6. 积分交易表 (points_transactions)

记录所有积分变动操作。

```sql
CREATE TABLE points_transactions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id),
    points INTEGER NOT NULL,
    type VARCHAR(20) NOT NULL CHECK (type IN ('earned', 'spent', 'expired', 'refunded')),
    source VARCHAR(50) NOT NULL,
    balance_before INTEGER NOT NULL,
    balance_after INTEGER NOT NULL,
    expires_at TIMESTAMP,

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

**关键特性**:

- 完整的积分流水记录
- 余额快照功能
- 积分过期管理
- 多种积分来源支持

### 7. 退款申请表 (refund_requests)

管理7天内退款申请。

```sql
CREATE TABLE refund_requests (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    transaction_id UUID NOT NULL REFERENCES transactions(id),
    requester_id UUID NOT NULL REFERENCES users(id),
    reason TEXT NOT NULL,
    refund_amount DECIMAL(10,2) NOT NULL,
    status VARCHAR(20) DEFAULT 'pending',

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    reviewed_at TIMESTAMP,
    processed_at TIMESTAMP
);
```

**关键特性**:

- 7天退款窗口期约束
- 管理员审核流程
- 自动时间戳管理

### 8. 资源评价表 (resource_reviews)

用户对购买资源的评价系统。

```sql
CREATE TABLE resource_reviews (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    resource_id UUID NOT NULL REFERENCES resources(id),
    reviewer_id UUID NOT NULL REFERENCES users(id),
    transaction_id UUID NOT NULL REFERENCES transactions(id),
    rating INTEGER NOT NULL CHECK (rating >= 1 AND rating <= 5),
    title VARCHAR(255),
    content TEXT,
    status VARCHAR(20) DEFAULT 'active',

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

**关键特性**:

- 验证购买后才能评价
- 自动更新资源评分统计
- 评价内容审核支持

### 9. 系统配置表 (system_configs)

存储平台配置参数。

```sql
CREATE TABLE system_configs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    config_key VARCHAR(100) UNIQUE NOT NULL,
    config_value TEXT NOT NULL,
    config_type VARCHAR(20) NOT NULL,
    category VARCHAR(50) NOT NULL,
    is_public BOOLEAN DEFAULT FALSE,
    is_editable BOOLEAN DEFAULT TRUE,

    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

**关键特性**:

- 动态配置管理
- 类型安全的配置值
- 公开/私有配置区分

## 🔗 表关系图

```
users (1) ──── (N) resources
  │                 │
  │                 │
  └── (N) transactions (N) ──┘
          │
          ├── (1) commission_records
          ├── (1) refund_requests
          └── (1) resource_reviews

users (1) ──── (N) withdrawal_requests
users (1) ──── (N) points_transactions
```

## 📈 索引策略

### 主要索引

- **用户表**: email, user_type, is_verified, created_at
- **资源表**: creator_id, category, status, price_usd, sales_count
- **交易表**: buyer_id, seller_id, resource_id, status, created_at
- **全文搜索**: resources(title, description, tags)

### 复合索引

- `(resource_id, status)` - 资源状态查询
- `(user_id, created_at)` - 用户时间序列查询
- `(category, status, created_at)` - 资源分类浏览

## 🛡️ 数据完整性

### 约束检查

- 邮箱格式验证
- 余额非负数约束
- 评分范围约束 (1-5)
- 退款时间窗口约束
- 防止自购约束

### 触发器

- 自动更新时间戳
- 自动设置发布时间
- 评分统计自动更新
- 余额变动记录

## 🔧 数据库函数

### 配置管理函数

```sql
-- 获取配置值
SELECT get_config('points_to_usd_rate');

-- 设置配置值
SELECT set_config('daily_checkin_points', '15', user_id);
```

### 统计视图

- `user_points_balance` - 用户积分余额统计
- `resource_review_stats` - 资源评价统计
- `refund_statistics` - 退款统计

## 📊 性能优化

### 查询优化

- 使用部分索引减少存储空间
- 合理使用复合索引
- 避免全表扫描的查询设计

### 数据分区

- 考虑按时间分区大表（如transactions）
- 历史数据归档策略

### 缓存策略

- 热点配置数据缓存
- 用户余额信息缓存
- 资源统计信息缓存

## 🔄 迁移和维护

### 迁移管理

- 版本化迁移脚本
- 回滚支持
- 数据一致性检查

### 备份策略

- 定期全量备份
- 增量备份
- 跨区域备份

### 监控指标

- 连接池使用率
- 查询性能监控
- 存储空间监控
- 锁等待监控

---

**设计版本**: 1.0  
**最后更新**: 2025年7月17日  
**维护团队**: AIGC Service Hub 开发团队
