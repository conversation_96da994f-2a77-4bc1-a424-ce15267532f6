{"name": "aigc-service-hub-backend", "version": "1.0.0", "description": "AIGC Service Hub - AI创作者服务平台后端API", "main": "dist/index.js", "type": "module", "scripts": {"dev": "tsx watch src/index.ts", "start:dev": "tsx watch src/index.ts", "build": "tsc", "start": "node dist/index.js", "start:prod": "node dist/index.js", "test": "vitest", "test:watch": "vitest --watch", "test:coverage": "vitest --coverage", "test:e2e": "vitest run --config vitest.e2e.config.ts", "lint": "eslint src --ext .ts", "lint:fix": "eslint src --ext .ts --fix", "format": "prettier --write \"src/**/*.{ts,js,json,md}\"", "format:check": "prettier --check \"src/**/*.{ts,js,json,md}\"", "type-check": "tsc --noEmit", "migrate": "tsx src/database/migrate.ts", "seed": "tsx src/database/seed.ts", "db:reset": "npm run migrate && npm run seed", "clean": "rm -rf dist node_modules/.cache"}, "keywords": ["aigc", "ai", "service", "hub", "api", "backend"], "author": "AIGC Service Hub Team", "license": "MIT", "dependencies": {"@types/pg": "^8.15.4", "bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^17.2.0", "express": "^5.1.0", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "pg": "^8.16.3", "uuid": "^11.1.0"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/jsonwebtoken": "^9.0.10", "@types/morgan": "^1.9.10", "@types/node": "^24.0.14", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^8.37.0", "@typescript-eslint/parser": "^8.37.0", "@vitest/coverage-v8": "^3.2.4", "eslint": "^9.31.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import": "^2.29.1", "eslint-plugin-security": "^2.1.1", "eslint-plugin-unused-imports": "^3.0.0", "prettier": "^3.1.1", "supertest": "^6.3.4", "@types/supertest": "^6.0.2", "tsx": "^4.20.3", "typescript": "^5.8.3", "vitest": "^3.2.4"}}