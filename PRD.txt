AIGC Service Hub 产品需求文档 (PRD)
文档版本

V1.0 (MVP 阶段)

创建日期

2025年7月15日

项目名称

AIGC Service Hub

文档作者

Gemini (根据项目方资料撰写)

状态

初稿

1. 项目概述 (Overview)
1.1. 项目愿景 (Vision)
AIGC Service Hub 立志成为全球领先的 AI 创作者服务平台，为全球创作者提供一个自由、高效的 AI 资源交易与共享空间。我们致力于通过推动微调模型、工作流等 AI 资源的流通与交流，促进全球 AI 生态的繁荣发展，支持个人与企业在 AI 领域实现技术创新及价值创造。

1.2. 项目使命 (Mission)
我们的核心使命是赋能每一位在 AI 时代浪潮中面临挑战与机遇的职业人士，特别是设计师、程序员、法律顾问、文案创作者、算法工程师等群体。通过提供一个稳定、高效的 AI 资源交易平台，我们帮助他们将专业知识和技能转化为可盈利的数字资产，开辟新的收入来源与职业发展机会，在 AI 时代重塑职业生涯成为超级个体，实现“创收、分享与交流”的梦想。

1.3. 目标用户 (Target Audience)
资源提供方（创作者/卖家）:

个人创作者: 自由职业者、AI爱好者、设计师、程序员、算法工程师等，希望通过分享自己的 AI 作品（如 LoRA 模型、提示词）获得收入。

企业/团体创作者: 专注于 AI 技术研发与应用的公司或团队，希望通过平台销售其商业级模型、工具或工作流，扩大市场影响力并实现商业变现。

资源使用方（消费者/买家）:

需要使用特定 AI 资源来提升工作效率或创作质量的个人和企业。

寻求特定解决方案来集成到自身产品或服务中的开发者和公司。

1.4. 成功指标 (Success Metrics for MVP)
用户增长: 上线后3个月内，注册创作者数量达到 500+。

平台活跃度: 上线后3个月内，发布的资源总数超过 1,000 个。

交易量: 实现首次交易，并持续产生稳定的日均交易订单。

用户满意度: 7天退款率低于10%，确保资源质量和交易体验。

2. MVP 范围 (MVP Scope)
为实现最轻量级MVP（Minimum Viable Product）阶段，我们将集中资源实现核心功能，快速推向市场进行验证。

2.1. 核心功能 (In Scope)
用户注册与登录（个人/企业创作者身份区分）。

创作者资源上传、编辑、管理与定价功能。

资源浏览、搜索与详情页展示。

无购物车，即时购买与支付流程（集成 PayPal）。

交易完成后，即时提供资源下载链接。

自动化的阶梯分佣与分账系统。

用户个人中心（包含资产管理、提现申请）。

基础的游戏化系统（积分系统、创作者排名）。

后台管理系统（用于管理员审核退款请求、管理用户与资源）。

2.2. 暂不实现 (Out of Scope)
购物车功能。

移动端（App / H5 响应式）。

复杂的社交功能（如私信、评论区、论坛）。

订阅制收费模式。

站内AI模型在线运行/测试功能。

多语言支持（MVP阶段仅支持一种主要语言，如中文或英文）。

除 PayPal 外的其他支付方式。

3. 功能需求 (Functional Requirements)
3.1. 用户模块 (User Module)
功能点

详细描述

优先级

用户注册

1. 用户可选择“个人创作者”或“企业创作者”身份进行注册。<br>2. 需要提供邮箱、设置密码。<br>3. 邮箱需要验证。<br>4. 企业创作者需要额外提供企业名称（初期可简化，无需强制认证）。

高

用户登录

使用邮箱和密码进行登录。

高

个人中心

1. 我的主页: 展示个人信息、发布的资源列表、总收入、总销量。<br>2. 资源管理: 查看、编辑、发布、下架自己上传的资源。<br>3. 财务管理: 显示总余额、可提现余额、冻结金额（7天内）、收支明细、提现记录。<br>4. 提现申请: 当可提现余额大于$0时，可发起提现至绑定的 PayPal 账户。<br>5. 订单管理: 查看我的购买记录和下载链接。

高

3.2. 资源管理模块 (Resource Management Module)
功能点

详细描述

优先级

资源上传

1. 通过首页发布+按钮弹出一个独立的上传页面。<br>2. 必填信息：二级分类名称（领域）（例如视频、图片、文字、3D、建模）、封面图、详细描述、用途标签。<br>3. 文件上传: 支持最大单个 30GB 的文件上传。需采用分片上传、断点续传技术以保证大文件上传成功率。<br>4. 定价: 创作者可选择使用 美元($) 或 积分 定价。

高

资源编辑/管理

创作者可以随时在个人中心编辑已发布资源的名称、描述、价格等信息，或进行下架操作。

高

资源分类

平台预设固定一级分类，创作者点击首页发布+按钮上传时进行选择：<br> - 微调模型 (Fine-tuned Models)<br> - LoRA<br> - 工作流 (Workflows)<br> - 提示词 (Prompts)<br> - 工具 (Tools)

高

3.3. 交易与支付模块 (Transaction & Payment Module)
功能点

详细描述

优先级

资源购买

1. 资源详情页有明确的“立即购买”按钮，显示价格（美元或积分）。<br>2. 无购物车，点击后直接跳转到支付确认页面。

高

PayPal 支付

1. 集成 PayPal 作为唯一的支付网关。<br>2. 用户点击购买后，调用 PayPal API 完成支付流程。

高

交易完成

1. 支付成功后，系统立即为用户生成一个永久有效的下载链接。<br>2. 该链接显示在交易成功页面，并同时保存在用户的“订单管理”中。<br>3. 资源可被无限次购买。

高

积分支付

如果用户选择积分支付且积分足够，直接扣除相应积分，完成交易。

中

3.4. 运营与财务模块 (Operation & Finance Module)
功能点

详细描述

优先级

阶梯分佣

1. 系统需自动记录每项资源的销售次数。<br>2. 个人创作者: 首单创作者得95%，平台5%。此后每售出一单，平台佣金+5%，直至平台佣金达50%后固定。<br>3. 企业创作者: 首单创作者得92%，平台8%。此后每售出一单，平台佣金+8%，直至平台佣金达56%后固定。<br>4. 交易完成后，系统需 自动分账，将相应金额记入创作者和平台的账户。

高

提现规则

1. 7天冻结期: 新产生的交易金额，将被冻结7天，7天后自动转为“可提现余额”。<br>2. 随时提现: 创作者可随时对“可提现余额”发起提现申请。<br>3. 自动化提现: 提现申请应尽可能自动化处理，直接支付到创作者绑定的PayPal账户。

高

提现扣费

1. 提现时，系统需自动计算并展示扣费明细。<br>2. 必要扣费: PayPal交易手续费（按PayPal标准）。<br>3. 税务扣费: 根据运营主体“洛拉（天津）人工智能有限公司”所在地税法，计算并代扣相关税费（如个人所得税等）。[注：此项规则复杂，MVP阶段可先实现手续费扣除，并明确告知用户税费由其自行申报，或与财务顾问确认简化方案]。

高

7天退款

1. 用户购买资源后7天内，如有版权争议或严重质量问题，可向平台申请退款。<br>2. 申请需提交给管理员审核，管理员核实后执行退款操作。<br>3. 退款后，资金原路退回，并从创作者收入中扣除相应款项。

中

3.5. 游戏化模块 (Gamification Module)
功能点

详细描述

优先级

积分系统

1. 用户可通过每日签到、完成首次上传、达成销售额等行为获得积分。<br>2. 积分可用于购买标明为“积分出售”的资源。<br>3. 美元与积分的兑换比例由平台设定。

中

创作者排名

1. 平台设立排行榜，如“周榜”、“月榜”、“总榜”。<br>2. 排名依据可以是：销售额、销售量、资源下载量等。<br>3. 在首页或专门的社区页面展示，激励创作者。

中

4. 非功能性需求 (Non-Functional Requirements)
类别

详细描述

性能

1. 页面加载时间：核心页面（首页、列表页、详情页）在3秒内完成加载。<br>2. 文件上传/下载：针对 30GB 大文件，需保证传输稳定性和合理的速度。使用CDN加速全球访问。

可用性

平台整体可用性需达到 99.9%。

安全性

1. 用户密码需加密存储。<br>2. 防止SQL注入、XSS、CSRF等常见Web攻击。<br>3. 支付流程必须符合 PayPal 的安全标准。<br>4. 资源文件存储安全，防止未经授权的盗链和下载。

技术栈/环境

1. 云服务: 亚马逊云科技 (AWS)，区域：美国俄勒冈州 (us-west-2)。<br>2. 核心服务: 使用 S3 进行文件存储，CloudFront 作为 CDN，EC2/ECS/Fargate 部署应用。<br>3. UI/UX: 前端使用 Material-UI 组件库进行构建。<br>4. 容器化: 所有开发、测试、集成、部署环境均需基于 Docker 容器，确保环境一致性。<br>5. 支付网关: PayPal。

设备兼容性

仅需支持主流 PC 端浏览器（Chrome, Firefox, Safari, Edge 最新版本）。

5. 开发与项目管理 (Development & Project Management)
整体架构 (Architecture):

由 Sequential thinking MCP 协助完成整体架构设计及任务清单，确保逻辑清晰、可扩展。

UI/UX 设计:

由 Material-UI 协助完成原型及交互设计，保证界面风格统一、用户体验良好。

代码实践 (Code Practice):

由 Context 7 MCP 协助完成最佳代码实践，保证代码质量、可维护性。

测试 (Testing):

所有项目测试（特别是端到端测试）由 Playwright MCP 协助完成，实现自动化测试流程。

文档与资源 (Documentation & Assets):

创建并维护 README.md 文件，记录项目从零到一的开发全过程，包括环境配置、启动命令、架构决策等。

创建项目品牌素材库文件夹，统一管理 LOGO、色值、字体、图片及各类设计素材，按类别存放。

6. 未来路线图 (Future Roadmap - Post MVP)
功能增强:

实现购物车功能，支持一次性购买多个资源。

引入订阅模式，用户付费后可在一定期限内无限下载某些资源。

开发站内信/即时通讯功能，方便买卖双方沟通。

社区生态:

建立资源评论和评分系统。

开设创作者社区或论坛，鼓励交流与分享。

平台扩展:

支持多语言界面。

集成更多支付方式（如Stripe、支付宝、微信支付）。

开发移动端应用（iOS/Android）。

技术升级:

提供模型在线预览或API调用服务。

引入AI推荐算法，为用户精准推荐所需资源。