# AIGC Service Hub Backend Environment Variables

# 应用配置
NODE_ENV=development
PORT=3000
APP_NAME=AIGC Service Hub API
APP_VERSION=1.0.0
FRONTEND_URL=http://localhost:5173
BACKEND_URL=http://localhost:3000

# 数据库配置
DATABASE_URL=postgresql://username:password@localhost:5432/aigc_service_hub
DB_HOST=localhost
DB_PORT=5432
DB_NAME=aigc_service_hub
DB_USER=your_username
DB_PASSWORD=your_password
DB_SSL=false

# JWT配置
JWT_SECRET=your_super_secret_jwt_key_here_minimum_32_characters
JWT_EXPIRES_IN=7d
JWT_REFRESH_SECRET=your_refresh_token_secret_here
JWT_REFRESH_EXPIRES_IN=30d

# PayPal配置
PAYPAL_CLIENT_ID=your_paypal_client_id_here
PAYPAL_CLIENT_SECRET=your_paypal_client_secret_here
PAYPAL_MODE=sandbox
PAYPAL_WEBHOOK_ID=your_paypal_webhook_id_here

# AWS配置
AWS_ACCESS_KEY_ID=your_aws_access_key_id
AWS_SECRET_ACCESS_KEY=your_aws_secret_access_key
AWS_REGION=us-west-2
AWS_S3_BUCKET=aigc-service-hub-files
AWS_CLOUDFRONT_DOMAIN=your_cloudfront_domain.cloudfront.net

# Redis配置 (可选)
REDIS_URL=redis://localhost:6379
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

# 邮件服务配置
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASS=your_app_password
FROM_EMAIL=<EMAIL>
FROM_NAME=AIGC Service Hub

# 文件上传配置
MAX_FILE_SIZE=32212254720
CHUNK_SIZE=5242880
ALLOWED_FILE_TYPES=.zip,.rar,.7z,.tar,.gz,.json,.txt,.md,.py,.js,.ts
UPLOAD_TIMEOUT=300000

# 安全配置
BCRYPT_ROUNDS=12
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
CORS_ORIGIN=http://localhost:5173

# 积分系统配置
POINTS_TO_USD_RATE=0.01
DAILY_CHECKIN_POINTS=10
FIRST_UPLOAD_POINTS=100
SALES_MILESTONE_POINTS=50

# 分佣配置
INDIVIDUAL_CREATOR_INITIAL_RATE=0.95
INDIVIDUAL_CREATOR_FINAL_RATE=0.50
ENTERPRISE_CREATOR_INITIAL_RATE=0.92
ENTERPRISE_CREATOR_FINAL_RATE=0.56
COMMISSION_DECAY_FACTOR=0.05

# 提现配置
WITHDRAWAL_MIN_AMOUNT=10.00
WITHDRAWAL_FEE_RATE=0.02
WITHDRAWAL_FREEZE_DAYS=7

# 日志配置
LOG_LEVEL=info
LOG_FILE=logs/app.log
LOG_MAX_SIZE=10m
LOG_MAX_FILES=5

# 监控配置
ENABLE_METRICS=true
METRICS_PORT=9090
HEALTH_CHECK_INTERVAL=30000

# 第三方服务配置
SENTRY_DSN=your_sentry_dsn_here
GOOGLE_ANALYTICS_ID=your_ga_id_here

# 功能开关
ENABLE_REGISTRATION=true
ENABLE_EMAIL_VERIFICATION=true
ENABLE_RATE_LIMITING=true
ENABLE_REQUEST_LOGGING=true
ENABLE_CORS=true
