import { Request, Response, NextFunction } from 'express';
import { verifyToken } from '@/utils/jwt';
import { UserModel } from '@/models/User';
import { JWTPayload } from '@/types';

// 扩展Request接口以包含用户信息
declare global {
  namespace Express {
    interface Request {
      user?: {
        id: string;
        email: string;
        userType: 'individual' | 'enterprise';
        status: string;
        emailVerified: boolean;
      };
    }
  }
}

// 认证中间件
export async function authenticateToken(
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> {
  try {
    const authHeader = req.headers.authorization;
    const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

    if (!token) {
      res.status(401).json({
        success: false,
        error: {
          code: 'MISSING_TOKEN',
          message: '访问令牌缺失'
        }
      });
      return;
    }

    // 验证JWT令牌
    const decoded = verifyToken(token);
    if (!decoded) {
      res.status(403).json({
        success: false,
        error: {
          code: 'INVALID_TOKEN',
          message: '访问令牌无效或已过期'
        }
      });
      return;
    }

    // 从数据库获取最新用户信息
    const user = await UserModel.findById(decoded.userId);
    if (!user) {
      res.status(403).json({
        success: false,
        error: {
          code: 'USER_NOT_FOUND',
          message: '用户不存在'
        }
      });
      return;
    }

    // 检查用户状态
    if (user.status === 'banned') {
      res.status(403).json({
        success: false,
        error: {
          code: 'ACCOUNT_BANNED',
          message: '账户已被封禁'
        }
      });
      return;
    }

    if (user.status === 'suspended') {
      res.status(403).json({
        success: false,
        error: {
          code: 'ACCOUNT_SUSPENDED',
          message: '账户已被暂停'
        }
      });
      return;
    }

    // 将用户信息添加到请求对象
    req.user = {
      id: user.id,
      email: user.email,
      userType: user.userType,
      status: user.status,
      emailVerified: user.emailVerified
    };

    next();
  } catch (error) {
    console.error('Authentication error:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: '认证失败'
      }
    });
  }
}

// 可选认证中间件（用户可能已登录也可能未登录）
export async function optionalAuth(
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> {
  try {
    const authHeader = req.headers.authorization;
    const token = authHeader && authHeader.split(' ')[1];

    if (!token) {
      // 没有令牌，继续处理但不设置用户信息
      next();
      return;
    }

    const decoded = verifyToken(token);
    if (!decoded) {
      // 令牌无效，继续处理但不设置用户信息
      next();
      return;
    }

    const user = await UserModel.findById(decoded.userId);
    if (user && user.status === 'active') {
      req.user = {
        id: user.id,
        email: user.email,
        userType: user.userType,
        status: user.status,
        emailVerified: user.emailVerified
      };
    }

    next();
  } catch (error) {
    // 认证失败，继续处理但不设置用户信息
    console.error('Optional auth error:', error);
    next();
  }
}

// 邮箱验证检查中间件
export function requireEmailVerification(
  req: Request,
  res: Response,
  next: NextFunction
): void {
  if (!req.user) {
    res.status(401).json({
      success: false,
      error: {
        code: 'AUTHENTICATION_REQUIRED',
        message: '需要登录'
      }
    });
    return;
  }

  if (!req.user.emailVerified) {
    res.status(403).json({
      success: false,
      error: {
        code: 'EMAIL_NOT_VERIFIED',
        message: '请先验证邮箱'
      }
    });
    return;
  }

  next();
}

// 用户类型检查中间件
export function requireUserType(userTypes: ('individual' | 'enterprise')[]) {
  return (req: Request, res: Response, next: NextFunction): void => {
    if (!req.user) {
      res.status(401).json({
        success: false,
        error: {
          code: 'AUTHENTICATION_REQUIRED',
          message: '需要登录'
        }
      });
      return;
    }

    if (!userTypes.includes(req.user.userType)) {
      res.status(403).json({
        success: false,
        error: {
          code: 'INSUFFICIENT_PERMISSIONS',
          message: '权限不足'
        }
      });
      return;
    }

    next();
  };
}

// 管理员权限检查中间件
export async function requireAdmin(
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> {
  try {
    if (!req.user) {
      res.status(401).json({
        success: false,
        error: {
          code: 'AUTHENTICATION_REQUIRED',
          message: '需要登录'
        }
      });
      return;
    }

    // 获取完整用户信息检查是否为管理员
    const user = await UserModel.findById(req.user.id);
    if (!user) {
      res.status(403).json({
        success: false,
        error: {
          code: 'USER_NOT_FOUND',
          message: '用户不存在'
        }
      });
      return;
    }

    // TODO: 添加管理员角色检查逻辑
    // 这里需要在用户表中添加role字段或创建单独的管理员表
    // if (user.role !== 'admin') {
    //   res.status(403).json({
    //     success: false,
    //     error: {
    //       code: 'ADMIN_REQUIRED',
    //       message: '需要管理员权限'
    //     }
    //   });
    //   return;
    // }

    next();
  } catch (error) {
    console.error('Admin auth error:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: '权限验证失败'
      }
    });
  }
}

// 资源所有者检查中间件
export function requireResourceOwner(resourceIdParam: string = 'id') {
  return async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      if (!req.user) {
        res.status(401).json({
          success: false,
          error: {
            code: 'AUTHENTICATION_REQUIRED',
            message: '需要登录'
          }
        });
        return;
      }

      const resourceId = req.params[resourceIdParam];
      if (!resourceId) {
        res.status(400).json({
          success: false,
          error: {
            code: 'MISSING_RESOURCE_ID',
            message: '资源ID缺失'
          }
        });
        return;
      }

      // TODO: 实现资源所有者检查逻辑
      // 这里需要查询资源表检查创建者是否为当前用户
      // const resource = await ResourceModel.findById(resourceId);
      // if (!resource || resource.creatorId !== req.user.id) {
      //   res.status(403).json({
      //     success: false,
      //     error: {
      //       code: 'NOT_RESOURCE_OWNER',
      //       message: '只能操作自己的资源'
      //     }
      //   });
      //   return;
      // }

      next();
    } catch (error) {
      console.error('Resource owner check error:', error);
      res.status(500).json({
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: '权限验证失败'
        }
      });
    }
  };
}
