# AIGC Service Hub 色彩规范

## 🎨 主色调系统

### 主品牌色 - 蓝色系

```css
/* 主品牌色 */
--primary-50: #eff6ff; /* 最浅 */
--primary-100: #dbeafe; /* 很浅 */
--primary-200: #bfdbfe; /* 浅 */
--primary-300: #93c5fd; /* 中浅 */
--primary-400: #60a5fa; /* 中 */
--primary-500: #3b82f6; /* 标准 */
--primary-600: #2563eb; /* 主色调 ⭐ */
--primary-700: #1d4ed8; /* 深 */
--primary-800: #1e40af; /* 很深 */
--primary-900: #1e3a8a; /* 最深 */
```

### 辅助色 - 紫色系

```css
/* 辅助色 */
--secondary-50: #faf5ff; /* 最浅 */
--secondary-100: #f3e8ff; /* 很浅 */
--secondary-200: #e9d5ff; /* 浅 */
--secondary-300: #d8b4fe; /* 中浅 */
--secondary-400: #c084fc; /* 中 */
--secondary-500: #a855f7; /* 标准 */
--secondary-600: #9333ea; /* 主辅助色 ⭐ */
--secondary-700: #7c3aed; /* 深 */
--secondary-800: #6b21a8; /* 很深 */
--secondary-900: #581c87; /* 最深 */
```

## 🌈 功能色彩系统

### 成功色 - 绿色系

```css
/* 成功/确认 */
--success-50: #ecfdf5;
--success-100: #d1fae5;
--success-200: #a7f3d0;
--success-300: #6ee7b7;
--success-400: #34d399;
--success-500: #10b981; /* 主成功色 ⭐ */
--success-600: #059669;
--success-700: #047857;
--success-800: #065f46;
--success-900: #064e3b;
```

### 警告色 - 橙色系

```css
/* 警告/注意 */
--warning-50: #fffbeb;
--warning-100: #fef3c7;
--warning-200: #fde68a;
--warning-300: #fcd34d;
--warning-400: #fbbf24;
--warning-500: #f59e0b; /* 主警告色 ⭐ */
--warning-600: #d97706;
--warning-700: #b45309;
--warning-800: #92400e;
--warning-900: #78350f;
```

### 错误色 - 红色系

```css
/* 错误/危险 */
--error-50: #fef2f2;
--error-100: #fee2e2;
--error-200: #fecaca;
--error-300: #fca5a5;
--error-400: #f87171;
--error-500: #ef4444; /* 主错误色 ⭐ */
--error-600: #dc2626;
--error-700: #b91c1c;
--error-800: #991b1b;
--error-900: #7f1d1d;
```

### 信息色 - 青色系

```css
/* 信息/提示 */
--info-50: #ecfeff;
--info-100: #cffafe;
--info-200: #a5f3fc;
--info-300: #67e8f9;
--info-400: #22d3ee;
--info-500: #06b6d4; /* 主信息色 ⭐ */
--info-600: #0891b2;
--info-700: #0e7490;
--info-800: #155e75;
--info-900: #164e63;
```

## ⚫ 中性色系统

### 灰色系

```css
/* 中性色 */
--gray-50: #f9fafb; /* 背景色 */
--gray-100: #f3f4f6; /* 浅背景 */
--gray-200: #e5e7eb; /* 边框色 */
--gray-300: #d1d5db; /* 分割线 */
--gray-400: #9ca3af; /* 占位符 */
--gray-500: #6b7280; /* 辅助文字 ⭐ */
--gray-600: #4b5563; /* 次要文字 */
--gray-700: #374151; /* 主要文字 */
--gray-800: #1f2937; /* 标题文字 ⭐ */
--gray-900: #111827; /* 最深文字 */
```

### 特殊色彩

```css
/* 纯色 */
--white: #ffffff; /* 纯白 */
--black: #000000; /* 纯黑 */

/* 透明度 */
--overlay-light: rgba(255, 255, 255, 0.9);
--overlay-dark: rgba(0, 0, 0, 0.5);
--backdrop: rgba(0, 0, 0, 0.25);
```

## 🎯 使用场景指南

### 主要界面元素

```css
/* 导航栏 */
.navbar {
  background-color: var(--white);
  border-bottom: 1px solid var(--gray-200);
  color: var(--gray-800);
}

/* 主要按钮 */
.btn-primary {
  background-color: var(--primary-600);
  color: var(--white);
  border: none;
}

.btn-primary:hover {
  background-color: var(--primary-700);
}

/* 次要按钮 */
.btn-secondary {
  background-color: var(--gray-100);
  color: var(--gray-700);
  border: 1px solid var(--gray-300);
}
```

### 文字颜色

```css
/* 标题文字 */
.text-title {
  color: var(--gray-900);
}

/* 正文文字 */
.text-body {
  color: var(--gray-700);
}

/* 辅助文字 */
.text-muted {
  color: var(--gray-500);
}

/* 链接文字 */
.text-link {
  color: var(--primary-600);
}

.text-link:hover {
  color: var(--primary-700);
}
```

### 状态指示

```css
/* 成功状态 */
.status-success {
  color: var(--success-600);
  background-color: var(--success-50);
  border: 1px solid var(--success-200);
}

/* 警告状态 */
.status-warning {
  color: var(--warning-600);
  background-color: var(--warning-50);
  border: 1px solid var(--warning-200);
}

/* 错误状态 */
.status-error {
  color: var(--error-600);
  background-color: var(--error-50);
  border: 1px solid var(--error-200);
}
```

## 🌓 深色模式适配

### 深色模式色彩

```css
/* 深色模式主色调 */
@media (prefers-color-scheme: dark) {
  :root {
    --bg-primary: var(--gray-900);
    --bg-secondary: var(--gray-800);
    --bg-tertiary: var(--gray-700);

    --text-primary: var(--gray-100);
    --text-secondary: var(--gray-300);
    --text-muted: var(--gray-400);

    --border-primary: var(--gray-700);
    --border-secondary: var(--gray-600);
  }
}
```

## 📊 对比度标准

### WCAG 2.1 AA级标准

- **正常文字**: 对比度 ≥ 4.5:1
- **大文字**: 对比度 ≥ 3:1
- **图形元素**: 对比度 ≥ 3:1

### 推荐组合

```css
/* 高对比度组合 */
.high-contrast {
  color: var(--gray-900);
  background-color: var(--white);
  /* 对比度: 21:1 */
}

/* 标准对比度组合 */
.standard-contrast {
  color: var(--gray-700);
  background-color: var(--white);
  /* 对比度: 9.5:1 */
}

/* 最低可接受对比度 */
.minimum-contrast {
  color: var(--gray-600);
  background-color: var(--white);
  /* 对比度: 4.6:1 */
}
```

## 🎨 渐变色规范

### 主要渐变

```css
/* 主品牌渐变 */
.gradient-primary {
  background: linear-gradient(135deg, var(--primary-600), var(--primary-700));
}

/* 辅助渐变 */
.gradient-secondary {
  background: linear-gradient(135deg, var(--secondary-600), var(--secondary-700));
}

/* 彩虹渐变 */
.gradient-rainbow {
  background: linear-gradient(135deg, var(--primary-500), var(--secondary-500), var(--info-500));
}
```

## 🔧 实现工具

### CSS变量文件

```css
/* colors.css */
:root {
  /* 在此处定义所有颜色变量 */
}
```

### Sass/SCSS变量

```scss
// _colors.scss
$primary-600: #2563eb;
$secondary-600: #9333ea;
// ... 其他颜色变量
```

### JavaScript颜色对象

```javascript
// colors.js
export const colors = {
  primary: {
    50: '#EFF6FF',
    600: '#2563EB',
    // ... 其他色阶
  },
  // ... 其他颜色系列
};
```

## ✅ 使用检查清单

### 设计阶段

- [ ] 是否使用了定义的颜色变量
- [ ] 颜色组合是否满足对比度要求
- [ ] 是否考虑了深色模式适配
- [ ] 功能色彩是否使用正确

### 开发阶段

- [ ] 是否使用CSS变量或预定义常量
- [ ] 是否实现了深色模式切换
- [ ] 颜色是否在不同设备上显示一致
- [ ] 是否通过了无障碍测试

---

**颜色规范版本**: 1.0  
**最后更新**: 2025年7月17日  
**维护团队**: AIGC Service Hub 设计团队
