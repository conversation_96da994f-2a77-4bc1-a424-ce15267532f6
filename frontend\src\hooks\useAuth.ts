import { useAuthContext } from '../contexts/AuthContext';
import { User } from '../types';

// 认证Hook接口
interface UseAuthReturn {
  // 状态
  user: User | null;
  token: string | null;
  loading: boolean;
  isAuthenticated: boolean;
  initialized: boolean;
  
  // 方法
  login: (email: string, password: string) => Promise<void>;
  logout: () => void;
  updateUser: (user: User) => void;
}

// 使用认证的Hook
export const useAuth = (): UseAuthReturn => {
  const { state, login, logout, updateUser, loading } = useAuthContext();

  return {
    // 状态
    user: state.user,
    token: state.token,
    loading,
    isAuthenticated: !!state.user && !!state.token,
    initialized: state.initialized,
    
    // 方法
    login,
    logout,
    updateUser,
  };
};
