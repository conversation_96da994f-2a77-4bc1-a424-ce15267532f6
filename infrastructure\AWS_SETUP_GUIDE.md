# AIGC Service Hub AWS基础设施设置指南

## 📋 AWS基础设施概览

本指南详细介绍了AIGC Service Hub项目的AWS云基础设施配置，包括完整的生产级部署方案。

### 🏗️ 架构组件
- **计算**: ECS Fargate + Application Load Balancer
- **存储**: S3 (文件存储) + RDS PostgreSQL (数据库)
- **缓存**: ElastiCache Redis
- **CDN**: CloudFront
- **网络**: VPC + 公有/私有子网
- **监控**: CloudWatch + CloudTrail
- **安全**: IAM + Security Groups + WAF

## 🛠 前置要求

### 1. AWS账户设置

**创建AWS账户**
- 访问 [AWS官网](https://aws.amazon.com/) 创建账户
- 完成账户验证和支付方式设置
- 启用必要的AWS服务

**IAM用户创建**
```bash
# 创建具有管理员权限的IAM用户
# 1. 登录AWS控制台
# 2. 进入IAM服务
# 3. 创建新用户，选择"编程访问"
# 4. 附加"AdministratorAccess"策略
# 5. 下载访问密钥
```

### 2. 本地工具安装

**AWS CLI安装**
```bash
# Windows
curl "https://awscli.amazonaws.com/AWSCLIV2.msi" -o "AWSCLIV2.msi"
msiexec /i AWSCLIV2.msi

# macOS
curl "https://awscli.amazonaws.com/AWSCLIV2.pkg" -o "AWSCLIV2.pkg"
sudo installer -pkg AWSCLIV2.pkg -target /

# Linux
curl "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" -o "awscliv2.zip"
unzip awscliv2.zip
sudo ./aws/install
```

**Terraform安装**
```bash
# Windows (使用Chocolatey)
choco install terraform

# macOS (使用Homebrew)
brew install terraform

# Linux
wget https://releases.hashicorp.com/terraform/1.6.0/terraform_1.6.0_linux_amd64.zip
unzip terraform_1.6.0_linux_amd64.zip
sudo mv terraform /usr/local/bin/
```

### 3. AWS凭证配置

**配置AWS凭证**
```bash
aws configure
# AWS Access Key ID: [输入访问密钥ID]
# AWS Secret Access Key: [输入秘密访问密钥]
# Default region name: us-west-2
# Default output format: json
```

**验证配置**
```bash
aws sts get-caller-identity
```

## 🚀 基础设施部署

### 1. 准备配置文件

**复制变量文件**
```bash
cd infrastructure/terraform
cp terraform.tfvars.example terraform.tfvars
```

**编辑配置文件**
```hcl
# terraform.tfvars
project_name = "aigc-service-hub"
environment  = "prod"
aws_region   = "us-west-2"

# 数据库配置
db_name     = "aigc_service_hub"
db_username = "aigc_admin"

# 域名配置（可选）
domain_name     = "your-domain.com"
certificate_arn = "arn:aws:acm:us-west-2:123456789012:certificate/12345678-1234-1234-1234-123456789012"

# 环境特定配置
environment_config = {
  db_instance_class    = "db.t3.small"
  redis_node_type     = "cache.t3.small"
  ecs_desired_count   = 3
  enable_auto_scaling = true
}
```

### 2. 执行部署

**使用部署脚本（推荐）**
```bash
# 给脚本执行权限
chmod +x infrastructure/scripts/deploy-aws.sh

# 初始化基础设施
./infrastructure/scripts/deploy-aws.sh init

# 规划部署
./infrastructure/scripts/deploy-aws.sh plan

# 应用配置
./infrastructure/scripts/deploy-aws.sh apply
```

**手动部署**
```bash
cd infrastructure/terraform

# 初始化Terraform
terraform init

# 验证配置
terraform validate

# 规划部署
terraform plan -var-file="terraform.tfvars"

# 应用配置
terraform apply -var-file="terraform.tfvars"
```

### 3. 验证部署

**检查基础设施状态**
```bash
./infrastructure/scripts/deploy-aws.sh status
```

**获取输出信息**
```bash
./infrastructure/scripts/deploy-aws.sh output
```

## 📊 架构详解

### VPC网络架构

```
┌─────────────────────────────────────────────────────────────┐
│                    VPC (10.0.0.0/16)                       │
├─────────────────────────────────────────────────────────────┤
│  Public Subnets (10.0.0.0/24, ********/24, ********/24)  │
│  ├─ Internet Gateway                                        │
│  ├─ Application Load Balancer                              │
│  └─ NAT Gateways                                           │
├─────────────────────────────────────────────────────────────┤
│  Private Subnets (10.0.10.0/24, 10.0.11.0/24, 10.0.12.0/24)│
│  ├─ ECS Fargate Tasks                                      │
│  └─ ElastiCache Redis                                      │
├─────────────────────────────────────────────────────────────┤
│  Database Subnets (10.0.20.0/24, 10.0.21.0/24, 10.0.22.0/24)│
│  └─ RDS PostgreSQL                                         │
└─────────────────────────────────────────────────────────────┘
```

### 服务组件

**计算层**
- **ECS Fargate**: 无服务器容器运行
- **Application Load Balancer**: 负载均衡和SSL终止
- **Auto Scaling**: 基于CPU/内存自动扩缩容

**存储层**
- **S3存储桶**:
  - `files`: AI模型和资源文件
  - `backups`: 数据库备份
  - `logs`: 应用程序日志
- **RDS PostgreSQL**: 主数据库
- **ElastiCache Redis**: 缓存和会话存储

**网络层**
- **CloudFront CDN**: 全球内容分发
- **Route53**: DNS管理（可选）
- **VPC**: 网络隔离和安全

## 🔧 配置选项

### 环境配置

**开发环境**
```hcl
environment = "dev"
environment_config = {
  db_instance_class     = "db.t3.micro"
  redis_node_type      = "cache.t3.micro"
  ecs_desired_count    = 1
  enable_auto_scaling  = false
}
enable_spot_instances = true
log_retention_days = 7
```

**生产环境**
```hcl
environment = "prod"
environment_config = {
  db_instance_class     = "db.t3.large"
  redis_node_type      = "cache.t3.medium"
  ecs_desired_count    = 5
  enable_auto_scaling  = true
}
backup_retention_period = 30
log_retention_days = 90
```

### 成本优化

**开发环境成本优化**
- 使用Spot实例
- 较小的实例类型
- 较短的日志保留期
- 单AZ部署

**生产环境成本优化**
- 预留实例
- S3生命周期策略
- CloudFront缓存优化
- 自动扩缩容

## 🛡️ 安全配置

### 网络安全

**Security Groups**
- ALB: 仅允许80/443端口
- ECS: 仅允许来自ALB的流量
- RDS: 仅允许来自ECS的5432端口
- Redis: 仅允许来自ECS的6379端口

**网络ACLs**
- 默认拒绝所有流量
- 仅允许必要的端口和协议

### 数据安全

**加密**
- S3: 服务器端加密(AES-256)
- RDS: 静态数据加密
- EBS: 卷加密
- CloudFront: HTTPS强制

**访问控制**
- IAM角色最小权限原则
- S3存储桶策略
- VPC端点安全

## 📈 监控和日志

### CloudWatch监控

**指标监控**
- ECS: CPU、内存、网络使用率
- RDS: 连接数、CPU、存储
- ALB: 请求数、延迟、错误率
- CloudFront: 缓存命中率、错误率

**告警配置**
- 高CPU使用率告警
- 数据库连接数告警
- 应用程序错误率告警
- 磁盘空间不足告警

### 日志管理

**日志收集**
- 应用程序日志 → CloudWatch Logs
- 访问日志 → S3
- VPC流日志 → CloudWatch Logs
- CloudTrail → S3

## 🔄 运维操作

### 常用命令

```bash
# 检查基础设施状态
./infrastructure/scripts/deploy-aws.sh status

# 更新基础设施
./infrastructure/scripts/deploy-aws.sh plan
./infrastructure/scripts/deploy-aws.sh apply

# 查看输出信息
./infrastructure/scripts/deploy-aws.sh output

# 销毁基础设施（谨慎使用）
./infrastructure/scripts/deploy-aws.sh destroy
```

### 备份和恢复

**数据库备份**
- 自动备份: 每日备份，保留7-30天
- 手动快照: 重要变更前创建
- 跨区域复制: 灾难恢复

**应用程序备份**
- S3版本控制
- 跨区域复制
- 生命周期策略

## 🚨 故障排除

### 常见问题

**1. Terraform初始化失败**
```bash
# 检查AWS凭证
aws sts get-caller-identity

# 检查权限
aws iam get-user
```

**2. ECS任务启动失败**
```bash
# 查看ECS服务事件
aws ecs describe-services --cluster <cluster-name> --services <service-name>

# 查看任务日志
aws logs get-log-events --log-group-name <log-group> --log-stream-name <stream>
```

**3. 数据库连接失败**
```bash
# 检查安全组规则
aws ec2 describe-security-groups --group-ids <sg-id>

# 检查数据库状态
aws rds describe-db-instances --db-instance-identifier <db-id>
```

### 性能优化

**数据库优化**
- 调整实例类型
- 优化连接池配置
- 启用性能洞察

**应用程序优化**
- 调整ECS任务资源
- 配置自动扩缩容
- 优化容器镜像

## 💰 成本管理

### 成本监控

**AWS Cost Explorer**
- 按服务查看成本
- 设置预算告警
- 分析成本趋势

**标签策略**
- 按环境标记资源
- 按项目分组成本
- 定期审查未使用资源

### 成本优化建议

1. **使用预留实例** - 生产环境长期运行的资源
2. **启用S3智能分层** - 自动优化存储成本
3. **配置自动扩缩容** - 根据负载调整资源
4. **定期清理** - 删除未使用的资源
5. **监控异常** - 设置成本异常告警

---

**文档版本**: 1.0  
**最后更新**: 2025年7月17日  
**维护团队**: AIGC Service Hub 开发团队
