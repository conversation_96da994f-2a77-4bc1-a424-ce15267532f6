# AIGC Service Hub 项目初始化脚本 (PowerShell)

param(
    [switch]$SkipInstall,
    [switch]$SkipGit,
    [switch]$Verbose
)

# 颜色函数
function WriteColorOutput($ForegroundColor, $message) {
    $fc = $host.UI.RawUI.ForegroundColor
    $host.UI.RawUI.ForegroundColor = $ForegroundColor
    Write-Output $message
    $host.UI.RawUI.ForegroundColor = $fc
}

function LogInfo($message) {
    WriteColorOutput Blue "[INFO] $message"
}

function LogSuccess($message) {
    WriteColorOutput Green "[SUCCESS] $message"
}

function LogWarning($message) {
    WriteColorOutput Yellow "[WARNING] $message"
}

function LogError($message) {
    WriteColorOutput Red "[ERROR] $message"
}

function LogStep($step, $total, $message) {
    WriteColorOutput Cyan "[$step/$total] $message"
}

# 检查先决条件
function CheckPrerequisites {
    LogInfo "检查先决条件..."
    
    $errors = @()
    
    # 检查Node.js
    try {
        $nodeVersion = node --version
        $nodeVersion = $nodeVersion.TrimStart('v')
        $requiredVersion = [Version]"18.0.0"
        $currentVersion = [Version]$nodeVersion
        
        if ($currentVersion -ge $requiredVersion) {
            LogSuccess "Node.js版本: $nodeVersion ✓"
        } else {
            $errors += "Node.js版本过低: $nodeVersion (需要 >= 18.0.0)"
        }
    } catch {
        $errors += "Node.js未安装"
    }
    
    # 检查npm
    try {
        $npmVersion = npm --version
        $requiredVersion = [Version]"8.0.0"
        $currentVersion = [Version]$npmVersion
        
        if ($currentVersion -ge $requiredVersion) {
            LogSuccess "npm版本: $npmVersion ✓"
        } else {
            $errors += "npm版本过低: $npmVersion (需要 >= 8.0.0)"
        }
    } catch {
        $errors += "npm未安装"
    }
    
    # 检查Git
    if (-not $SkipGit) {
        try {
            $gitVersion = git --version
            LogSuccess "Git已安装: $gitVersion ✓"
        } catch {
            $errors += "Git未安装"
        }
    }
    
    if ($errors.Count -gt 0) {
        LogError "先决条件检查失败:"
        foreach ($error in $errors) {
            LogError "  - $error"
        }
        return $false
    }
    
    LogSuccess "所有先决条件满足 ✓"
    return $true
}

# 初始化Git仓库
function InitializeGit {
    if ($SkipGit) {
        LogInfo "跳过Git初始化"
        return $true
    }
    
    LogInfo "初始化Git仓库..."
    
    try {
        if (-not (Test-Path ".git")) {
            git init
            LogSuccess "Git仓库已初始化 ✓"
        } else {
            LogInfo "Git仓库已存在"
        }
        
        # 设置Git配置模板
        if (-not (git config --get commit.template)) {
            git config commit.template .gitmessage
            LogSuccess "Git提交模板已设置 ✓"
        }
        
        return $true
    } catch {
        LogError "Git初始化失败: $_"
        return $false
    }
}

# 安装依赖
function InstallDependencies {
    if ($SkipInstall) {
        LogInfo "跳过依赖安装"
        return $true
    }
    
    LogInfo "安装项目依赖..."
    
    try {
        # 安装根目录依赖
        LogInfo "安装根目录依赖..."
        npm install
        LogSuccess "根目录依赖安装完成 ✓"
        
        # 安装前端依赖
        LogInfo "安装前端依赖..."
        Set-Location frontend
        npm install
        Set-Location ..
        LogSuccess "前端依赖安装完成 ✓"
        
        # 安装后端依赖
        LogInfo "安装后端依赖..."
        Set-Location backend
        npm install
        Set-Location ..
        LogSuccess "后端依赖安装完成 ✓"
        
        return $true
    } catch {
        LogError "依赖安装失败: $_"
        return $false
    }
}

# 设置Git hooks
function SetupGitHooks {
    if ($SkipGit) {
        LogInfo "跳过Git hooks设置"
        return $true
    }
    
    LogInfo "设置Git hooks..."
    
    try {
        # 初始化husky
        npx husky install
        LogSuccess "Husky已初始化 ✓"
        
        return $true
    } catch {
        LogError "Git hooks设置失败: $_"
        return $false
    }
}

# 创建环境配置文件
function CreateEnvironmentFiles {
    LogInfo "创建环境配置文件..."
    
    # 前端环境文件
    if (-not (Test-Path "frontend/.env")) {
        $frontendEnv = @"
# 前端环境配置
VITE_API_BASE_URL=http://localhost:3000/api
VITE_APP_NAME=AIGC Service Hub
VITE_APP_VERSION=1.0.0
VITE_ENABLE_MOCK=false
"@
        $frontendEnv | Out-File -FilePath "frontend/.env" -Encoding UTF8
        LogSuccess "前端环境文件已创建 ✓"
    } else {
        LogInfo "前端环境文件已存在"
    }
    
    # 后端环境文件
    if (-not (Test-Path "backend/.env")) {
        $backendEnv = @"
# 后端环境配置
NODE_ENV=development
PORT=3000

# 数据库配置
DB_HOST=localhost
DB_PORT=5432
DB_NAME=aigc_service_hub
DB_USER=postgres
DB_PASSWORD=password

# JWT配置
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRES_IN=7d

# Redis配置 (可选)
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

# AWS配置 (生产环境)
AWS_REGION=us-east-1
AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
S3_BUCKET_NAME=

# 邮件配置 (可选)
SMTP_HOST=
SMTP_PORT=587
SMTP_USER=
SMTP_PASS=

# 日志配置
LOG_LEVEL=debug
"@
        $backendEnv | Out-File -FilePath "backend/.env" -Encoding UTF8
        LogSuccess "后端环境文件已创建 ✓"
    } else {
        LogInfo "后端环境文件已存在"
    }
}

# 运行初始检查
function RunInitialChecks {
    LogInfo "运行初始代码质量检查..."
    
    try {
        # 格式化代码
        LogInfo "格式化代码..."
        npm run format
        LogSuccess "代码格式化完成 ✓"
        
        # 运行类型检查
        LogInfo "运行TypeScript类型检查..."
        npm run type-check
        LogSuccess "类型检查通过 ✓"
        
        return $true
    } catch {
        LogWarning "初始检查中发现问题，请稍后手动修复"
        return $false
    }
}

# 显示下一步指引
function ShowNextSteps {
    LogSuccess "项目初始化完成！"
    Write-Output ""
    WriteColorOutput Cyan "🚀 下一步操作:"
    Write-Output ""
    Write-Output "1. 配置数据库:"
    Write-Output "   - 安装PostgreSQL"
    Write-Output "   - 创建数据库: aigc_service_hub"
    Write-Output "   - 更新 backend/.env 中的数据库配置"
    Write-Output ""
    Write-Output "2. 启动开发服务器:"
    Write-Output "   npm run dev                    # 同时启动前后端"
    Write-Output "   # 或者分别启动:"
    Write-Output "   cd frontend && npm run dev     # 前端: http://localhost:5173"
    Write-Output "   cd backend && npm run dev      # 后端: http://localhost:3000"
    Write-Output ""
    Write-Output "3. 运行代码质量检查:"
    Write-Output "   ./scripts/check-code-quality.ps1"
    Write-Output ""
    Write-Output "4. 查看开发指南:"
    Write-Output "   DEVELOPMENT_GUIDE.md"
    Write-Output "   CODE_STANDARDS.md"
    Write-Output ""
    WriteColorOutput Green "✨ 开始愉快的开发吧！"
}

# 主函数
function Main {
    $totalSteps = 7
    $currentStep = 0
    
    Write-Output "🚀 AIGC Service Hub 项目初始化"
    Write-Output "================================"
    Write-Output ""
    
    # 步骤1: 检查先决条件
    $currentStep++
    LogStep $currentStep $totalSteps "检查先决条件"
    if (-not (CheckPrerequisites)) {
        LogError "项目初始化失败"
        return 1
    }
    Write-Output ""
    
    # 步骤2: 初始化Git
    $currentStep++
    LogStep $currentStep $totalSteps "初始化Git仓库"
    if (-not (InitializeGit)) {
        LogError "项目初始化失败"
        return 1
    }
    Write-Output ""
    
    # 步骤3: 安装依赖
    $currentStep++
    LogStep $currentStep $totalSteps "安装项目依赖"
    if (-not (InstallDependencies)) {
        LogError "项目初始化失败"
        return 1
    }
    Write-Output ""
    
    # 步骤4: 设置Git hooks
    $currentStep++
    LogStep $currentStep $totalSteps "设置Git hooks"
    if (-not (SetupGitHooks)) {
        LogError "项目初始化失败"
        return 1
    }
    Write-Output ""
    
    # 步骤5: 创建环境文件
    $currentStep++
    LogStep $currentStep $totalSteps "创建环境配置文件"
    CreateEnvironmentFiles
    Write-Output ""
    
    # 步骤6: 运行初始检查
    $currentStep++
    LogStep $currentStep $totalSteps "运行初始代码检查"
    RunInitialChecks
    Write-Output ""
    
    # 步骤7: 显示下一步
    $currentStep++
    LogStep $currentStep $totalSteps "完成初始化"
    ShowNextSteps
    
    return 0
}

# 运行主函数
$exitCode = Main
exit $exitCode
