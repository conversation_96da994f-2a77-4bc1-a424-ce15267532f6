-- 创建资源评价表
-- 支持用户对购买的资源进行评价和评分

CREATE TABLE resource_reviews (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    resource_id UUID NOT NULL REFERENCES resources(id) ON DELETE CASCADE,
    reviewer_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    transaction_id UUID NOT NULL REFERENCES transactions(id) ON DELETE RESTRICT,
    
    -- 评价内容
    rating INTEGER NOT NULL CHECK (rating >= 1 AND rating <= 5),
    title VARCHAR(255),
    content TEXT,
    
    -- 评价标签
    tags TEXT[] DEFAULT '{}',
    
    -- 状态管理
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN (
        'active',
        'hidden',
        'reported',
        'deleted'
    )),
    
    -- 审核信息
    is_verified_purchase BOOLEAN DEFAULT TRUE,
    moderation_notes TEXT,
    
    -- 互动统计
    helpful_count INTEGER DEFAULT 0 CHECK (helpful_count >= 0),
    unhelpful_count INTEGER DEFAULT 0 CHECK (unhelpful_count >= 0),
    
    -- 时间戳
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- 约束：确保只有购买者才能评价
    CONSTRAINT valid_reviewer CHECK (
        reviewer_id = (
            SELECT buyer_id 
            FROM transactions 
            WHERE transactions.id = resource_reviews.transaction_id
        )
    ),
    CONSTRAINT valid_resource CHECK (
        resource_id = (
            SELECT resource_id 
            FROM transactions 
            WHERE transactions.id = resource_reviews.transaction_id
        )
    )
);

-- 创建索引
CREATE INDEX idx_resource_reviews_resource_id ON resource_reviews(resource_id);
CREATE INDEX idx_resource_reviews_reviewer_id ON resource_reviews(reviewer_id);
CREATE INDEX idx_resource_reviews_transaction_id ON resource_reviews(transaction_id);
CREATE INDEX idx_resource_reviews_rating ON resource_reviews(rating);
CREATE INDEX idx_resource_reviews_status ON resource_reviews(status);
CREATE INDEX idx_resource_reviews_created_at ON resource_reviews(created_at);

-- 创建唯一约束，确保每个交易只能评价一次
CREATE UNIQUE INDEX idx_resource_reviews_unique_transaction 
    ON resource_reviews(transaction_id);

-- 全文搜索索引
CREATE INDEX idx_resource_reviews_content_search ON resource_reviews 
    USING gin(to_tsvector('english', COALESCE(title, '') || ' ' || COALESCE(content, '')));

-- 创建更新时间戳触发器
CREATE TRIGGER update_resource_reviews_updated_at 
    BEFORE UPDATE ON resource_reviews 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- 创建评价统计更新触发器
CREATE OR REPLACE FUNCTION update_resource_rating_stats()
RETURNS TRIGGER AS $$
BEGIN
    -- 更新资源的评分统计
    UPDATE resources SET 
        rating_average = (
            SELECT ROUND(AVG(rating)::numeric, 2)
            FROM resource_reviews 
            WHERE resource_id = COALESCE(NEW.resource_id, OLD.resource_id) 
            AND status = 'active'
        ),
        rating_count = (
            SELECT COUNT(*)
            FROM resource_reviews 
            WHERE resource_id = COALESCE(NEW.resource_id, OLD.resource_id) 
            AND status = 'active'
        )
    WHERE id = COALESCE(NEW.resource_id, OLD.resource_id);
    
    RETURN COALESCE(NEW, OLD);
END;
$$ language 'plpgsql';

CREATE TRIGGER update_resource_rating_stats_trigger
    AFTER INSERT OR UPDATE OR DELETE ON resource_reviews
    FOR EACH ROW
    EXECUTE FUNCTION update_resource_rating_stats();

-- 创建评价统计视图
CREATE VIEW resource_review_stats AS
SELECT 
    r.id as resource_id,
    r.title as resource_title,
    COUNT(rv.id) as total_reviews,
    AVG(rv.rating) as average_rating,
    COUNT(CASE WHEN rv.rating = 5 THEN 1 END) as five_star_count,
    COUNT(CASE WHEN rv.rating = 4 THEN 1 END) as four_star_count,
    COUNT(CASE WHEN rv.rating = 3 THEN 1 END) as three_star_count,
    COUNT(CASE WHEN rv.rating = 2 THEN 1 END) as two_star_count,
    COUNT(CASE WHEN rv.rating = 1 THEN 1 END) as one_star_count,
    MAX(rv.created_at) as latest_review_at
FROM resources r
LEFT JOIN resource_reviews rv ON r.id = rv.resource_id AND rv.status = 'active'
GROUP BY r.id, r.title;

-- 添加注释
COMMENT ON TABLE resource_reviews IS '资源评价表，记录用户对购买资源的评价';
COMMENT ON COLUMN resource_reviews.rating IS '评分（1-5星）';
COMMENT ON COLUMN resource_reviews.is_verified_purchase IS '是否为验证购买';
COMMENT ON COLUMN resource_reviews.helpful_count IS '有用投票数';
COMMENT ON COLUMN resource_reviews.unhelpful_count IS '无用投票数';
COMMENT ON COLUMN resource_reviews.status IS '评价状态：active, hidden, reported, deleted';

COMMENT ON VIEW resource_review_stats IS '资源评价统计视图';
