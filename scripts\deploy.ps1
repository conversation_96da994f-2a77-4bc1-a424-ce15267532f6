# AIGC Service Hub Docker Deployment Script for Windows PowerShell

param(
    [Parameter(Mandatory=$true)]
    [string]$Command,
    
    [Parameter(Mandatory=$false)]
    [string]$Environment = "dev",
    
    [Parameter(Mandatory=$false)]
    [string]$Service = ""
)

# 颜色定义
function WriteColorOutput($ForegroundColor) {
    $fc = $host.UI.RawUI.ForegroundColor
    $host.UI.RawUI.ForegroundColor = $ForegroundColor
    if ($args) {
        Write-Output $args
    }
    $host.UI.RawUI.ForegroundColor = $fc
}

function LogInfo($message) {
    WriteColorOutput Blue "[INFO] $message"
}

function LogSuccess($message) {
    WriteColorOutput Green "[SUCCESS] $message"
}

function LogWarning($message) {
    WriteColorOutput Yellow "[WARNING] $message"
}

function LogError($message) {
    WriteColorOutput Red "[ERROR] $message"
}

# 检查Docker和Docker Compose
function CheckDependencies {
    LogInfo "检查依赖..."
    
    try {
        docker --version | Out-Null
    }
    catch {
        Log-Error "Docker 未安装，请先安装 Docker Desktop"
        exit 1
    }
    
    try {
        docker-compose --version | Out-Null
    }
    catch {
        Log-Error "Docker Compose 未安装，请先安装 Docker Compose"
        exit 1
    }
    
    Log-Success "依赖检查通过"
}

# 检查环境变量文件
function Check-EnvFile {
    if (-not (Test-Path ".env")) {
        Log-Warning ".env 文件不存在，从模板创建..."
        Copy-Item ".env.docker" ".env"
        Log-Warning "请编辑 .env 文件并填入正确的配置值"
        exit 1
    }
    Log-Success "环境变量文件检查通过"
}

# 构建镜像
function Build-Images($env) {
    Log-Info "构建Docker镜像..."
    
    switch ($env) {
        "dev" {
            docker-compose -f docker-compose.dev.yml build
        }
        "prod" {
            docker-compose -f docker-compose.prod.yml build
        }
        default {
            Log-Error "无效的环境: $env"
            exit 1
        }
    }
    
    if ($LASTEXITCODE -eq 0) {
        Log-Success "镜像构建完成"
    } else {
        Log-Error "镜像构建失败"
        exit 1
    }
}

# 启动服务
function Start-Services($env) {
    Log-Info "启动服务..."
    
    switch ($env) {
        "dev" {
            docker-compose -f docker-compose.dev.yml up -d
        }
        "prod" {
            docker-compose -f docker-compose.prod.yml up -d
        }
        default {
            Log-Error "无效的环境: $env"
            exit 1
        }
    }
    
    if ($LASTEXITCODE -eq 0) {
        Log-Success "服务启动完成"
    } else {
        Log-Error "服务启动失败"
        exit 1
    }
}

# 停止服务
function Stop-Services($env) {
    Log-Info "停止服务..."
    
    switch ($env) {
        "dev" {
            docker-compose -f docker-compose.dev.yml down
        }
        "prod" {
            docker-compose -f docker-compose.prod.yml down
        }
        default {
            Log-Error "无效的环境: $env"
            exit 1
        }
    }
    
    if ($LASTEXITCODE -eq 0) {
        Log-Success "服务停止完成"
    } else {
        Log-Error "服务停止失败"
        exit 1
    }
}

# 查看服务状态
function Show-Status($env) {
    Log-Info "服务状态:"
    
    switch ($env) {
        "dev" {
            docker-compose -f docker-compose.dev.yml ps
        }
        "prod" {
            docker-compose -f docker-compose.prod.yml ps
        }
        default {
            Log-Error "无效的环境: $env"
            exit 1
        }
    }
}

# 查看日志
function Show-Logs($env, $service) {
    switch ($env) {
        "dev" {
            if ($service) {
                docker-compose -f docker-compose.dev.yml logs -f $service
            } else {
                docker-compose -f docker-compose.dev.yml logs -f
            }
        }
        "prod" {
            if ($service) {
                docker-compose -f docker-compose.prod.yml logs -f $service
            } else {
                docker-compose -f docker-compose.prod.yml logs -f
            }
        }
        default {
            Log-Error "无效的环境: $env"
            exit 1
        }
    }
}

# 数据库迁移
function Run-Migrations($env) {
    Log-Info "运行数据库迁移..."
    
    switch ($env) {
        "dev" {
            docker-compose -f docker-compose.dev.yml exec backend npm run migrate
        }
        "prod" {
            docker-compose -f docker-compose.prod.yml exec backend npm run migrate
        }
        default {
            Log-Error "无效的环境: $env"
            exit 1
        }
    }
    
    if ($LASTEXITCODE -eq 0) {
        Log-Success "数据库迁移完成"
    } else {
        Log-Error "数据库迁移失败"
        exit 1
    }
}

# 创建种子数据
function Seed-Database($env) {
    Log-Info "创建种子数据..."
    
    switch ($env) {
        "dev" {
            docker-compose -f docker-compose.dev.yml exec backend npm run seed
        }
        "prod" {
            Log-Warning "生产环境不建议运行种子数据"
            $confirm = Read-Host "确定要在生产环境创建种子数据吗? (y/N)"
            if ($confirm -eq "y" -or $confirm -eq "Y") {
                docker-compose -f docker-compose.prod.yml exec backend npm run seed
            }
        }
        default {
            Log-Error "无效的环境: $env"
            exit 1
        }
    }
    
    if ($LASTEXITCODE -eq 0) {
        Log-Success "种子数据创建完成"
    } else {
        Log-Error "种子数据创建失败"
        exit 1
    }
}

# 健康检查
function HealthCheck($env) {
    Log-Info "执行健康检查..."
    
    # 检查前端
    try {
        $response = Invoke-WebRequest -Uri "http://localhost/health" -UseBasicParsing -TimeoutSec 5
        if ($response.StatusCode -eq 200) {
            Log-Success "前端服务健康"
        } else {
            Log-Error "前端服务异常"
        }
    }
    catch {
        Log-Error "前端服务异常"
    }
    
    # 检查后端API
    try {
        $response = Invoke-WebRequest -Uri "http://localhost:3000/api/health" -UseBasicParsing -TimeoutSec 5
        if ($response.StatusCode -eq 200) {
            Log-Success "后端API服务健康"
        } else {
            Log-Error "后端API服务异常"
        }
    }
    catch {
        Log-Error "后端API服务异常"
    }
}

# 清理资源
function Cleanup {
    Log-Info "清理Docker资源..."
    
    # 停止所有容器
    try { docker-compose -f docker-compose.dev.yml down 2>$null } catch {}
    try { docker-compose -f docker-compose.prod.yml down 2>$null } catch {}
    
    # 清理未使用的镜像
    docker image prune -f
    
    # 清理未使用的卷
    docker volume prune -f
    
    Log-Success "清理完成"
}

# 显示帮助信息
function Show-Help {
    Write-Host "AIGC Service Hub Docker 部署脚本 (Windows PowerShell)"
    Write-Host ""
    Write-Host "用法: .\deploy.ps1 -Command <命令> -Environment <环境> [-Service <服务>]"
    Write-Host ""
    Write-Host "环境:"
    Write-Host "  dev     开发环境"
    Write-Host "  prod    生产环境"
    Write-Host ""
    Write-Host "命令:"
    Write-Host "  build           构建Docker镜像"
    Write-Host "  start           启动服务"
    Write-Host "  stop            停止服务"
    Write-Host "  restart         重启服务"
    Write-Host "  status          查看服务状态"
    Write-Host "  logs            查看日志"
    Write-Host "  migrate         运行数据库迁移"
    Write-Host "  seed            创建种子数据"
    Write-Host "  health          健康检查"
    Write-Host "  cleanup         清理Docker资源"
    Write-Host "  help            显示帮助信息"
    Write-Host ""
    Write-Host "示例:"
    Write-Host "  .\deploy.ps1 -Command start -Environment dev"
    Write-Host "  .\deploy.ps1 -Command logs -Environment prod -Service backend"
    Write-Host "  .\deploy.ps1 -Command migrate -Environment dev"
}

# 主逻辑
Check-Dependencies

switch ($Command.ToLower()) {
    "build" {
        Check-EnvFile
        Build-Images $Environment
    }
    "start" {
        Check-EnvFile
        Start-Services $Environment
    }
    "stop" {
        Stop-Services $Environment
    }
    "restart" {
        Check-EnvFile
        Stop-Services $Environment
        Start-Services $Environment
    }
    "status" {
        Show-Status $Environment
    }
    "logs" {
        Show-Logs $Environment $Service
    }
    "migrate" {
        Run-Migrations $Environment
    }
    "seed" {
        Seed-Database $Environment
    }
    "health" {
        Health-Check $Environment
    }
    "cleanup" {
        Cleanup
    }
    "help" {
        Show-Help
    }
    default {
        Log-Error "无效的命令: $Command"
        Show-Help
        exit 1
    }
}
