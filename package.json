{"name": "aigc-service-hub", "version": "1.0.0", "description": "AIGC Service Hub - AI创作者服务平台", "private": true, "workspaces": ["frontend", "backend"], "scripts": {"install:all": "npm install && npm run install:frontend && npm run install:backend", "install:frontend": "cd frontend && npm install", "install:backend": "cd backend && npm install", "dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:frontend": "cd frontend && npm run dev", "dev:backend": "cd backend && npm run start:dev", "build": "npm run build:frontend && npm run build:backend", "build:frontend": "cd frontend && npm run build", "build:backend": "cd backend && npm run build", "lint": "npm run lint:frontend && npm run lint:backend", "lint:frontend": "cd frontend && npm run lint", "lint:backend": "cd backend && npm run lint", "lint:fix": "npm run lint:fix:frontend && npm run lint:fix:backend", "lint:fix:frontend": "cd frontend && npm run lint:fix", "lint:fix:backend": "cd backend && npm run lint:fix", "format": "prettier --write \"**/*.{js,jsx,ts,tsx,json,md,yml,yaml}\"", "format:check": "prettier --check \"**/*.{js,jsx,ts,tsx,json,md,yml,yaml}\"", "type-check": "npm run type-check:frontend && npm run type-check:backend", "type-check:frontend": "cd frontend && npm run type-check", "type-check:backend": "cd backend && npm run type-check", "test": "npm run test:frontend && npm run test:backend", "test:frontend": "cd frontend && npm run test", "test:backend": "cd backend && npm run test", "test:watch": "concurrently \"npm run test:watch:frontend\" \"npm run test:watch:backend\"", "test:watch:frontend": "cd frontend && npm run test:watch", "test:watch:backend": "cd backend && npm run test:watch", "clean": "npm run clean:frontend && npm run clean:backend", "clean:frontend": "cd frontend && rm -rf dist node_modules", "clean:backend": "cd backend && rm -rf dist node_modules", "docker:build": "docker-compose build", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down", "docker:dev": "docker-compose -f docker-compose.dev.yml up", "prepare": "husky install"}, "devDependencies": {"@commitlint/cli": "^18.4.3", "@commitlint/config-conventional": "^18.4.3", "concurrently": "^8.2.2", "husky": "^8.0.3", "lint-staged": "^15.2.0", "prettier": "^3.1.1"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "keywords": ["aigc", "ai", "service-hub", "marketplace", "creators", "typescript", "react", "<PERSON><PERSON><PERSON>"], "author": "AIGC Service Hub Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/your-org/aigc-service-hub.git"}, "bugs": {"url": "https://github.com/your-org/aigc-service-hub/issues"}, "homepage": "https://github.com/your-org/aigc-service-hub#readme"}