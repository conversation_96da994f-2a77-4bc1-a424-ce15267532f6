# AIGC Service Hub 开发指南

## 📋 开发环境设置

### 必需工具

**Node.js 环境**

```bash
# 推荐使用 Node.js 18.x 或更高版本
node --version  # 应该 >= 18.0.0
npm --version   # 应该 >= 8.0.0
```

**包管理器**

```bash
# 推荐使用 npm (项目默认)
npm install -g npm@latest

# 或者使用 yarn
npm install -g yarn

# 或者使用 pnpm
npm install -g pnpm
```

**开发工具**

- **VSCode**: 推荐的代码编辑器
- **Git**: 版本控制
- **Docker**: 容器化开发 (可选)
- **PostgreSQL**: 数据库 (本地开发)
- **Redis**: 缓存服务 (本地开发)

### VSCode 扩展

项目已配置推荐扩展列表，首次打开项目时 VSCode 会提示安装：

**核心扩展**

- ESLint - 代码质量检查
- Prettier - 代码格式化
- TypeScript - TypeScript 支持
- GitLens - Git 增强功能

**前端扩展**

- ES7+ React/Redux/React-Native snippets
- Tailwind CSS IntelliSense
- Auto Rename Tag

**后端扩展**

- REST Client - API 测试
- PostgreSQL Client
- Docker

## 🚀 快速开始

### 1. 克隆项目

```bash
git clone https://github.com/your-org/aigc-service-hub.git
cd aigc-service-hub
```

### 2. 安装依赖

```bash
# 安装根目录依赖 (开发工具)
npm install

# 安装前端依赖
cd frontend && npm install && cd ..

# 安装后端依赖
cd backend && npm install && cd ..
```

### 3. 环境配置

```bash
# 复制环境变量模板
cp backend/.env.example backend/.env
cp frontend/.env.example frontend/.env

# 编辑环境变量文件
# 配置数据库连接、API密钥等
```

### 4. 启动开发服务器

```bash
# 方式1: 使用 VSCode 任务 (推荐)
# Ctrl+Shift+P -> Tasks: Run Task -> Start Development Servers

# 方式2: 手动启动
# 终端1: 启动后端
cd backend && npm run start:dev

# 终端2: 启动前端
cd frontend && npm run dev
```

### 5. 访问应用

- **前端**: http://localhost:5173
- **后端API**: http://localhost:3000
- **API文档**: http://localhost:3000/api/docs

## 📝 代码规范

### 代码风格

**自动格式化**

- 使用 Prettier 进行代码格式化
- 保存时自动格式化 (VSCode 已配置)
- 提交前自动格式化 (Git hooks)

**代码检查**

- 使用 ESLint 进行代码质量检查
- TypeScript 严格模式
- 导入排序和未使用导入清理

### 命名规范

**文件命名**

```
# 组件文件 (PascalCase)
UserProfile.tsx
UserProfile.test.tsx
UserProfile.stories.tsx

# 工具函数 (camelCase)
apiClient.ts
dateUtils.ts
validationHelpers.ts

# 常量文件 (UPPER_SNAKE_CASE)
API_ENDPOINTS.ts
ERROR_MESSAGES.ts

# 页面文件 (kebab-case)
user-profile.page.tsx
settings.page.tsx
```

**变量命名**

```typescript
// 变量和函数 (camelCase)
const userName = 'john';
const getUserData = () => {};

// 常量 (UPPER_SNAKE_CASE)
const API_BASE_URL = 'https://api.example.com';
const MAX_RETRY_COUNT = 3;

// 类和接口 (PascalCase)
class UserService {}
interface UserData {}

// 类型别名 (PascalCase)
type ApiResponse<T> = {
  data: T;
  status: number;
};
```

### TypeScript 规范

**类型定义**

```typescript
// 优先使用 interface 定义对象类型
interface User {
  id: string;
  name: string;
  email: string;
  createdAt: Date;
}

// 使用 type 定义联合类型、函数类型等
type Status = 'pending' | 'approved' | 'rejected';
type EventHandler = (event: Event) => void;

// 使用泛型提高代码复用性
interface ApiResponse<T> {
  data: T;
  message: string;
  success: boolean;
}
```

**导入规范**

```typescript
// 类型导入使用 type 关键字
import type { User, ApiResponse } from './types';
import { getUserData, updateUser } from './api';

// 导入顺序 (ESLint 自动排序)
// 1. Node.js 内置模块
// 2. 第三方库
// 3. 内部模块
// 4. 相对路径导入
```

## 🔧 开发工具配置

### Git 工作流

**分支命名规范**

```bash
# 功能分支
feature/user-authentication
feature/payment-integration

# 修复分支
fix/login-validation-error
fix/memory-leak-issue

# 热修复分支
hotfix/critical-security-patch

# 发布分支
release/v1.2.0
```

**提交信息规范**

```bash
# 格式: <type>(<scope>): <subject>

# 示例
feat(auth): add OAuth2 login support
fix(api): resolve user data validation error
docs: update API documentation for v2.0
style(frontend): format code with prettier
refactor(backend): extract user service logic
test(api): add integration tests for auth endpoints
```

**提交类型**

- `feat`: 新功能
- `fix`: 修复bug
- `docs`: 文档更新
- `style`: 代码格式化
- `refactor`: 代码重构
- `perf`: 性能优化
- `test`: 测试相关
- `build`: 构建相关
- `ci`: CI/CD相关
- `chore`: 其他杂项

### 代码质量检查

**预提交检查**

```bash
# 自动运行 (通过 husky + lint-staged)
# 1. ESLint 检查和修复
# 2. Prettier 格式化
# 3. TypeScript 类型检查
# 4. 测试运行 (如果有相关文件变更)
```

**手动检查命令**

```bash
# 代码检查
npm run lint              # 检查所有代码
npm run lint:fix          # 检查并自动修复

# 代码格式化
npm run format            # 格式化所有代码
npm run format:check      # 检查格式化状态

# 类型检查
npm run type-check        # TypeScript 类型检查

# 测试
npm run test              # 运行所有测试
npm run test:watch        # 监听模式运行测试
npm run test:coverage     # 生成测试覆盖率报告
```

## 🧪 测试规范

### 测试文件组织

```
src/
├── components/
│   ├── UserProfile/
│   │   ├── UserProfile.tsx
│   │   ├── UserProfile.test.tsx
│   │   └── UserProfile.stories.tsx
│   └── ...
├── utils/
│   ├── dateUtils.ts
│   ├── dateUtils.test.ts
│   └── ...
└── __tests__/
    ├── integration/
    ├── e2e/
    └── fixtures/
```

### 测试命名规范

```typescript
// 描述性测试名称
describe('UserProfile Component', () => {
  it('should render user name correctly', () => {
    // 测试实现
  });

  it('should handle loading state', () => {
    // 测试实现
  });

  it('should call onEdit when edit button is clicked', () => {
    // 测试实现
  });
});
```

### 测试覆盖率要求

- **单元测试**: >= 80%
- **集成测试**: 核心业务流程
- **E2E测试**: 关键用户路径

## 📦 构建和部署

### 本地构建

```bash
# 构建前端
cd frontend && npm run build

# 构建后端
cd backend && npm run build

# 构建所有项目
npm run build:all
```

### Docker 开发

```bash
# 启动开发环境
docker-compose -f docker-compose.dev.yml up

# 构建生产镜像
docker-compose -f docker-compose.prod.yml build

# 启动生产环境
docker-compose -f docker-compose.prod.yml up
```

## 🔍 调试指南

### VSCode 调试配置

项目已配置 VSCode 调试环境：

**前端调试**

- 在 Chrome 中调试 React 应用
- 断点调试 TypeScript 代码
- 热重载支持

**后端调试**

- Node.js 调试器
- 断点调试 NestJS 应用
- 数据库查询调试

### 常用调试命令

```bash
# 前端调试模式启动
cd frontend && npm run dev:debug

# 后端调试模式启动
cd backend && npm run start:debug

# 数据库连接测试
npm run db:test

# API 健康检查
curl http://localhost:3000/api/health
```

## 📚 学习资源

### 技术文档

- [React 官方文档](https://react.dev/)
- [TypeScript 官方文档](https://www.typescriptlang.org/)
- [NestJS 官方文档](https://nestjs.com/)
- [Tailwind CSS 文档](https://tailwindcss.com/)

### 项目相关

- [API 文档](./docs/api.md)
- [数据库设计](./docs/database.md)
- [部署指南](./docs/deployment.md)
- [故障排除](./docs/troubleshooting.md)

## 🤝 贡献指南

### 开发流程

1. 从 `main` 分支创建功能分支
2. 开发功能并编写测试
3. 提交代码并推送到远程分支
4. 创建 Pull Request
5. 代码审查和测试
6. 合并到 `main` 分支

### 代码审查清单

- [ ] 代码符合项目规范
- [ ] 包含适当的测试
- [ ] 文档已更新
- [ ] 无安全漏洞
- [ ] 性能影响可接受
- [ ] 向后兼容性

---

**开发指南版本**: 1.0  
**最后更新**: 2025年7月17日  
**维护团队**: AIGC Service Hub 开发团队
