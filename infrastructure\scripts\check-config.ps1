# AIGC Service Hub AWS配置检查脚本 (PowerShell)

# 颜色函数
function Write-ColorOutput($ForegroundColor, $message) {
    $fc = $host.UI.RawUI.ForegroundColor
    $host.UI.RawUI.ForegroundColor = $ForegroundColor
    Write-Output $message
    $host.UI.RawUI.ForegroundColor = $fc
}

function LogInfo($message) {
    Write-ColorOutput Blue "[INFO] $message"
}

function LogSuccess($message) {
    Write-ColorOutput Green "[SUCCESS] $message"
}

function LogWarning($message) {
    Write-ColorOutput Yellow "[WARNING] $message"
}

function LogError($message) {
    Write-ColorOutput Red "[ERROR] $message"
}

# 检查配置文件
function CheckConfigFiles {
    LogInfo "检查配置文件..."
    
    $terraformDir = "infrastructure/terraform"
    $requiredFiles = @(
        "$terraformDir/main.tf",
        "$terraformDir/variables.tf",
        "$terraformDir/outputs.tf"
    )
    
    $missingFiles = @()
    
    foreach ($file in $requiredFiles) {
        if (-not (Test-Path $file)) {
            $missingFiles += $file
        }
    }
    
    if ($missingFiles.Count -gt 0) {
        LogError "缺少必要的配置文件:"
        foreach ($file in $missingFiles) {
            Write-Output "  - $file"
        }
        return $false
    }
    
    LogSuccess "所有必要的配置文件都存在"
    return $true
}

# 检查模块文件
function CheckModules {
    LogInfo "检查Terraform模块..."
    
    $modulesDir = "infrastructure/terraform/modules"
    $requiredModules = @("vpc", "s3", "cloudfront")
    
    $missingModules = @()
    
    foreach ($module in $requiredModules) {
        $modulePath = "$modulesDir/$module"
        if (-not (Test-Path $modulePath)) {
            $missingModules += $module
        } elseif (-not (Test-Path "$modulePath/main.tf")) {
            $missingModules += "$module (main.tf missing)"
        }
    }
    
    if ($missingModules.Count -gt 0) {
        LogError "缺少必要的模块:"
        foreach ($module in $missingModules) {
            Write-Output "  - $module"
        }
        return $false
    }
    
    LogSuccess "所有必要的模块都存在"
    return $true
}

# 检查变量文件
function CheckVariables {
    LogInfo "检查变量配置..."
    
    $tfvarsExample = "infrastructure/terraform/terraform.tfvars.example"
    $tfvarsFile = "infrastructure/terraform/terraform.tfvars"
    
    if (-not (Test-Path $tfvarsExample)) {
        LogError "变量示例文件不存在: $tfvarsExample"
        return $false
    }
    
    if (-not (Test-Path $tfvarsFile)) {
        LogWarning "变量文件不存在: $tfvarsFile"
        LogInfo "请从示例文件复制: Copy-Item $tfvarsExample $tfvarsFile"
        return $false
    }
    
    LogSuccess "变量文件配置正确"
    return $true
}

# 检查脚本文件
function CheckScripts {
    LogInfo "检查部署脚本..."
    
    $scripts = @(
        "infrastructure/scripts/deploy-aws.sh",
        "infrastructure/scripts/validate-terraform.sh"
    )
    
    foreach ($script in $scripts) {
        if (-not (Test-Path $script)) {
            LogError "脚本文件不存在: $script"
            return $false
        }
    }
    
    LogSuccess "部署脚本检查完成"
    return $true
}

# 检查文档
function CheckDocumentation {
    LogInfo "检查文档..."
    
    $docs = @(
        "infrastructure/AWS_SETUP_GUIDE.md",
        "infrastructure/AWS_COST_ESTIMATION.md"
    )
    
    foreach ($doc in $docs) {
        if (-not (Test-Path $doc)) {
            LogWarning "文档文件不存在: $doc"
        }
    }
    
    LogSuccess "文档检查完成"
    return $true
}

# 生成验证报告
function GenerateReport($checksPassed, $totalChecks) {
    LogInfo "生成验证报告..."
    
    $reportFile = "infrastructure/validation-report.txt"
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    
    $reportContent = @"
AIGC Service Hub AWS基础设施验证报告
生成时间: $timestamp

配置文件检查: ✓
模块文件检查: ✓
变量配置: ✓
脚本文件: ✓
文档文件: ✓

通过检查: $checksPassed/$totalChecks

建议的下一步操作:
1. 安装AWS CLI: https://aws.amazon.com/cli/
2. 安装Terraform: https://www.terraform.io/downloads
3. 配置AWS凭证: aws configure
4. 复制并编辑变量文件: Copy-Item terraform.tfvars.example terraform.tfvars
5. 初始化基础设施: ./scripts/deploy-aws.sh init
6. 规划部署: ./scripts/deploy-aws.sh plan
7. 应用配置: ./scripts/deploy-aws.sh apply

注意事项:
- 确保AWS账户有足够的权限
- 检查成本估算文档
- 在生产环境部署前进行充分测试

Windows用户注意:
- 建议使用WSL2或Git Bash运行shell脚本
- 或者使用PowerShell版本的部署脚本
"@
    
    $reportContent | Out-File -FilePath $reportFile -Encoding UTF8
    LogSuccess "验证报告已生成: $reportFile"
}

# 主函数
function Main {
    LogInfo "开始AWS基础设施配置验证..."
    Write-Output ""
    
    $checksPassed = 0
    $totalChecks = 5
    
    # 运行所有检查
    if (CheckConfigFiles) { $checksPassed++ }
    Write-Output ""
    
    if (CheckModules) { $checksPassed++ }
    Write-Output ""
    
    if (CheckVariables) { $checksPassed++ }
    Write-Output ""
    
    if (CheckScripts) { $checksPassed++ }
    Write-Output ""
    
    if (CheckDocumentation) { $checksPassed++ }
    Write-Output ""
    
    # 输出结果
    LogInfo "验证完成！"
    Write-Output "通过检查: $checksPassed/$totalChecks"
    
    if ($checksPassed -eq $totalChecks) {
        LogSuccess "所有检查通过！AWS基础设施配置就绪。"
        GenerateReport $checksPassed $totalChecks
        return 0
    } else {
        LogWarning "部分检查未通过，请修复问题后重新验证。"
        GenerateReport $checksPassed $totalChecks
        return 1
    }
}

# 运行验证
Main
