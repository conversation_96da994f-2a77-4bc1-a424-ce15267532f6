# AIGC Service Hub AWS Infrastructure Configuration
# Terraform configuration for complete AWS infrastructure setup

terraform {
  required_version = ">= 1.0"
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"
    }
    random = {
      source  = "hashicorp/random"
      version = "~> 3.1"
    }
  }

  # Backend configuration for state management
  backend "s3" {
    bucket         = "aigc-service-hub-terraform-state"
    key            = "infrastructure/terraform.tfstate"
    region         = "us-west-2"
    encrypt        = true
    dynamodb_table = "aigc-terraform-locks"
  }
}

# Configure the AWS Provider
provider "aws" {
  region = var.aws_region

  default_tags {
    tags = {
      Project     = "AIGC Service Hub"
      Environment = var.environment
      ManagedBy   = "Terraform"
      Owner       = "AIGC Team"
    }
  }
}

# Data sources
data "aws_caller_identity" "current" {}
data "aws_region" "current" {}

# Random password for database
resource "random_password" "db_password" {
  length  = 32
  special = true
}

# Random string for resource naming
resource "random_string" "suffix" {
  length  = 8
  special = false
  upper   = false
}

# Local values
locals {
  name_prefix = "${var.project_name}-${var.environment}"
  common_tags = {
    Project     = var.project_name
    Environment = var.environment
    ManagedBy   = "Terraform"
  }
}

# VPC Configuration
module "vpc" {
  source = "./modules/vpc"

  name_prefix         = local.name_prefix
  vpc_cidr           = var.vpc_cidr
  availability_zones = var.availability_zones
  
  tags = local.common_tags
}

# Security Groups
module "security_groups" {
  source = "./modules/security"

  name_prefix = local.name_prefix
  vpc_id      = module.vpc.vpc_id
  
  tags = local.common_tags
}

# S3 Storage
module "s3" {
  source = "./modules/s3"

  name_prefix = local.name_prefix
  environment = var.environment
  
  tags = local.common_tags
}

# CloudFront CDN
module "cloudfront" {
  source = "./modules/cloudfront"

  name_prefix           = local.name_prefix
  s3_bucket_domain_name = module.s3.files_bucket_domain_name
  s3_bucket_id          = module.s3.files_bucket_id
  
  tags = local.common_tags
}

# RDS Database
module "rds" {
  source = "./modules/rds"

  name_prefix           = local.name_prefix
  vpc_id               = module.vpc.vpc_id
  private_subnet_ids   = module.vpc.private_subnet_ids
  security_group_ids   = [module.security_groups.rds_security_group_id]
  
  db_name     = var.db_name
  db_username = var.db_username
  db_password = random_password.db_password.result
  
  tags = local.common_tags
}

# ElastiCache Redis
module "redis" {
  source = "./modules/redis"

  name_prefix           = local.name_prefix
  vpc_id               = module.vpc.vpc_id
  private_subnet_ids   = module.vpc.private_subnet_ids
  security_group_ids   = [module.security_groups.redis_security_group_id]
  
  tags = local.common_tags
}

# Application Load Balancer
module "alb" {
  source = "./modules/alb"

  name_prefix           = local.name_prefix
  vpc_id               = module.vpc.vpc_id
  public_subnet_ids    = module.vpc.public_subnet_ids
  security_group_ids   = [module.security_groups.alb_security_group_id]
  
  tags = local.common_tags
}

# ECS Cluster
module "ecs" {
  source = "./modules/ecs"

  name_prefix           = local.name_prefix
  vpc_id               = module.vpc.vpc_id
  private_subnet_ids   = module.vpc.private_subnet_ids
  security_group_ids   = [module.security_groups.ecs_security_group_id]
  target_group_arn     = module.alb.target_group_arn
  
  # Database connection
  db_host     = module.rds.db_endpoint
  db_name     = var.db_name
  db_username = var.db_username
  db_password = random_password.db_password.result
  
  # Redis connection
  redis_endpoint = module.redis.redis_endpoint
  
  # S3 configuration
  s3_bucket_name = module.s3.files_bucket_name
  
  # CloudFront
  cloudfront_domain = module.cloudfront.cloudfront_domain
  
  tags = local.common_tags
}

# Route53 DNS (optional)
module "route53" {
  source = "./modules/route53"
  count  = var.domain_name != "" ? 1 : 0

  domain_name       = var.domain_name
  alb_dns_name      = module.alb.alb_dns_name
  alb_zone_id       = module.alb.alb_zone_id
  cloudfront_domain = module.cloudfront.cloudfront_domain
  cloudfront_zone_id = module.cloudfront.cloudfront_zone_id
  
  tags = local.common_tags
}

# IAM Roles and Policies
module "iam" {
  source = "./modules/iam"

  name_prefix = local.name_prefix
  s3_bucket_arn = module.s3.files_bucket_arn
  
  tags = local.common_tags
}

# Monitoring and Logging
module "monitoring" {
  source = "./modules/monitoring"

  name_prefix = local.name_prefix
  
  tags = local.common_tags
}
