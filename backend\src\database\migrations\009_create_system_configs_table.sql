-- 创建系统配置表
-- 存储平台的各种配置参数

CREATE TABLE system_configs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    config_key VARCHAR(100) UNIQUE NOT NULL,
    config_value TEXT NOT NULL,
    config_type VARCHAR(20) NOT NULL CHECK (config_type IN (
        'string', 
        'number', 
        'boolean', 
        'json',
        'array'
    )),
    category VARCHAR(50) NOT NULL,
    description TEXT,
    is_public BOOLEAN DEFAULT FALSE,
    is_editable BOOLEAN DEFAULT TRUE,
    
    -- 时间戳
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_by UUID REFERENCES users(id)
);

-- 创建索引
CREATE INDEX idx_system_configs_config_key ON system_configs(config_key);
CREATE INDEX idx_system_configs_category ON system_configs(category);
CREATE INDEX idx_system_configs_is_public ON system_configs(is_public);

-- 创建更新时间戳触发器
CREATE TRIGGER update_system_configs_updated_at 
    BEFORE UPDATE ON system_configs 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- 插入默认配置
INSERT INTO system_configs (config_key, config_value, config_type, category, description, is_public) VALUES
-- 积分系统配置
('points_to_usd_rate', '0.01', 'number', 'points', '积分兑换美元汇率', true),
('daily_checkin_points', '10', 'number', 'points', '每日签到获得积分', true),
('first_upload_points', '100', 'number', 'points', '首次上传奖励积分', true),
('sales_milestone_points', '50', 'number', 'points', '销售里程碑奖励积分', true),
('points_expiry_days', '365', 'number', 'points', '积分过期天数', true),

-- 分佣系统配置
('individual_creator_initial_rate', '0.95', 'number', 'commission', '个人创作者初始分佣比例', false),
('individual_creator_final_rate', '0.50', 'number', 'commission', '个人创作者最终分佣比例', false),
('enterprise_creator_initial_rate', '0.92', 'number', 'commission', '企业创作者初始分佣比例', false),
('enterprise_creator_final_rate', '0.56', 'number', 'commission', '企业创作者最终分佣比例', false),
('commission_decay_factor', '0.05', 'number', 'commission', '分佣衰减因子', false),

-- 提现系统配置
('withdrawal_min_amount', '10.00', 'number', 'withdrawal', '最小提现金额（美元）', true),
('withdrawal_fee_rate', '0.02', 'number', 'withdrawal', '提现手续费率', true),
('withdrawal_freeze_days', '7', 'number', 'withdrawal', '提现冻结天数', true),
('withdrawal_auto_process', 'true', 'boolean', 'withdrawal', '是否自动处理提现', false),

-- 文件上传配置
('max_file_size', '32212254720', 'number', 'upload', '最大文件大小（字节）', true),
('allowed_file_types', '["zip","rar","7z","tar","gz","json","txt","md","py","js","ts"]', 'json', 'upload', '允许的文件类型', true),
('chunk_size', '5242880', 'number', 'upload', '分片上传大小（字节）', false),

-- 安全配置
('rate_limit_window_ms', '900000', 'number', 'security', '限流时间窗口（毫秒）', false),
('rate_limit_max_requests', '100', 'number', 'security', '限流最大请求数', false),
('jwt_expires_in', '7d', 'string', 'security', 'JWT过期时间', false),
('bcrypt_rounds', '12', 'number', 'security', 'bcrypt加密轮数', false),

-- 业务规则配置
('refund_window_days', '7', 'number', 'business', '退款申请窗口期（天）', true),
('review_window_days', '30', 'number', 'business', '评价窗口期（天）', true),
('download_link_expires_hours', '24', 'number', 'business', '下载链接过期时间（小时）', true),
('max_downloads_per_purchase', '5', 'number', 'business', '每次购买最大下载次数', true),

-- 平台信息配置
('platform_name', 'AIGC Service Hub', 'string', 'platform', '平台名称', true),
('platform_version', '1.0.0', 'string', 'platform', '平台版本', true),
('support_email', '<EMAIL>', 'string', 'platform', '客服邮箱', true),
('terms_version', '1.0', 'string', 'platform', '服务条款版本', true),
('privacy_version', '1.0', 'string', 'platform', '隐私政策版本', true),

-- 功能开关
('enable_registration', 'true', 'boolean', 'features', '是否开放注册', false),
('enable_email_verification', 'true', 'boolean', 'features', '是否启用邮箱验证', false),
('enable_points_system', 'true', 'boolean', 'features', '是否启用积分系统', false),
('enable_reviews', 'true', 'boolean', 'features', '是否启用评价系统', false),
('maintenance_mode', 'false', 'boolean', 'features', '是否为维护模式', false);

-- 创建配置获取函数
CREATE OR REPLACE FUNCTION get_config(key_name VARCHAR)
RETURNS TEXT AS $$
DECLARE
    config_val TEXT;
BEGIN
    SELECT config_value INTO config_val 
    FROM system_configs 
    WHERE config_key = key_name;
    
    RETURN config_val;
END;
$$ LANGUAGE plpgsql;

-- 创建配置设置函数
CREATE OR REPLACE FUNCTION set_config(key_name VARCHAR, new_value TEXT, user_id UUID DEFAULT NULL)
RETURNS BOOLEAN AS $$
BEGIN
    UPDATE system_configs 
    SET config_value = new_value, 
        updated_at = CURRENT_TIMESTAMP,
        updated_by = user_id
    WHERE config_key = key_name AND is_editable = TRUE;
    
    RETURN FOUND;
END;
$$ LANGUAGE plpgsql;

-- 添加注释
COMMENT ON TABLE system_configs IS '系统配置表，存储平台各种配置参数';
COMMENT ON COLUMN system_configs.config_type IS '配置值类型：string, number, boolean, json, array';
COMMENT ON COLUMN system_configs.is_public IS '是否为公开配置（前端可见）';
COMMENT ON COLUMN system_configs.is_editable IS '是否可编辑';

COMMENT ON FUNCTION get_config(VARCHAR) IS '获取配置值函数';
COMMENT ON FUNCTION set_config(VARCHAR, TEXT, UUID) IS '设置配置值函数';
