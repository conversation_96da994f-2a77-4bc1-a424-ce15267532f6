-- 创建用户表
-- 支持个人和企业创作者，包含财务信息和积分系统

CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    user_type VARCHAR(20) NOT NULL CHECK (user_type IN ('individual', 'enterprise')),
    company_name VARCHAR(255),
    paypal_email VARCHAR(255),
    is_verified BOOLEAN DEFAULT FALSE,
    email_verification_token VARCHAR(255),
    email_verification_expires_at TIMESTAMP,
    
    -- 财务信息
    total_earnings DECIMAL(12,2) DEFAULT 0.00,
    available_balance DECIMAL(12,2) DEFAULT 0.00,
    frozen_balance DECIMAL(12,2) DEFAULT 0.00,
    
    -- 积分系统
    points_balance INTEGER DEFAULT 0,
    last_checkin_date DATE,
    checkin_streak INTEGER DEFAULT 0,
    
    -- 统计信息
    total_uploads INTEGER DEFAULT 0,
    total_sales INTEGER DEFAULT 0,
    total_downloads INTEGER DEFAULT 0,
    
    -- 时间戳
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- 约束
    CONSTRAINT valid_email CHECK (email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$'),
    CONSTRAINT valid_company_name CHECK (
        (user_type = 'enterprise' AND company_name IS NOT NULL AND LENGTH(company_name) > 0) OR
        (user_type = 'individual')
    ),
    CONSTRAINT non_negative_balances CHECK (
        total_earnings >= 0 AND 
        available_balance >= 0 AND 
        frozen_balance >= 0 AND
        points_balance >= 0
    )
);

-- 创建索引
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_user_type ON users(user_type);
CREATE INDEX idx_users_is_verified ON users(is_verified);
CREATE INDEX idx_users_created_at ON users(created_at);
CREATE INDEX idx_users_paypal_email ON users(paypal_email) WHERE paypal_email IS NOT NULL;

-- 创建更新时间戳触发器
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_users_updated_at 
    BEFORE UPDATE ON users 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- 添加注释
COMMENT ON TABLE users IS '用户表，支持个人和企业创作者';
COMMENT ON COLUMN users.user_type IS '用户类型：individual(个人) 或 enterprise(企业)';
COMMENT ON COLUMN users.total_earnings IS '总收入（美元）';
COMMENT ON COLUMN users.available_balance IS '可用余额（美元）';
COMMENT ON COLUMN users.frozen_balance IS '冻结余额（美元）';
COMMENT ON COLUMN users.points_balance IS '积分余额';
COMMENT ON COLUMN users.last_checkin_date IS '最后签到日期';
COMMENT ON COLUMN users.checkin_streak IS '连续签到天数';
