-- 创建提现申请表
-- 支持7天冻结期和自动化提现处理

CREATE TABLE withdrawal_requests (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE RESTRICT,
    
    -- 提现金额
    amount DECIMAL(10,2) NOT NULL CHECK (amount > 0),
    paypal_fee DECIMAL(10,2) DEFAULT 0.00 CHECK (paypal_fee >= 0),
    tax_fee DECIMAL(10,2) DEFAULT 0.00 CHECK (tax_fee >= 0),
    net_amount DECIMAL(10,2) NOT NULL CHECK (net_amount > 0),
    
    -- PayPal信息
    paypal_email VARCHAR(255) NOT NULL,
    paypal_payout_id VARCHAR(255),
    paypal_payout_batch_id VARCHAR(255),
    
    -- 状态管理
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN (
        'pending',
        'processing',
        'completed',
        'failed',
        'cancelled'
    )),
    
    -- 处理信息
    processed_by UUID REFERENCES users(id),
    processing_notes TEXT,
    failure_reason TEXT,
    
    -- 时间戳
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    processed_at TIMESTAMP,
    completed_at TIMESTAMP,
    
    -- 约束
    CONSTRAINT valid_net_amount CHECK (net_amount = amount - paypal_fee - tax_fee),
    CONSTRAINT valid_paypal_email CHECK (
        paypal_email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$'
    )
);

-- 创建索引
CREATE INDEX idx_withdrawal_requests_user_id ON withdrawal_requests(user_id);
CREATE INDEX idx_withdrawal_requests_status ON withdrawal_requests(status);
CREATE INDEX idx_withdrawal_requests_created_at ON withdrawal_requests(created_at);
CREATE INDEX idx_withdrawal_requests_processed_at ON withdrawal_requests(processed_at);
CREATE INDEX idx_withdrawal_requests_paypal_payout_id ON withdrawal_requests(paypal_payout_id) 
    WHERE paypal_payout_id IS NOT NULL;

-- 创建更新时间戳触发器
CREATE TRIGGER update_withdrawal_requests_updated_at 
    BEFORE UPDATE ON withdrawal_requests 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- 创建处理时间自动设置触发器
CREATE OR REPLACE FUNCTION set_withdrawal_processed_at()
RETURNS TRIGGER AS $$
BEGIN
    -- 当状态从pending变为processing时，设置处理时间
    IF OLD.status = 'pending' AND NEW.status = 'processing' AND NEW.processed_at IS NULL THEN
        NEW.processed_at = CURRENT_TIMESTAMP;
    END IF;
    
    -- 当状态变为completed时，设置完成时间
    IF OLD.status != 'completed' AND NEW.status = 'completed' AND NEW.completed_at IS NULL THEN
        NEW.completed_at = CURRENT_TIMESTAMP;
    END IF;
    
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER set_withdrawal_requests_processed_at 
    BEFORE UPDATE ON withdrawal_requests 
    FOR EACH ROW 
    EXECUTE FUNCTION set_withdrawal_processed_at();

-- 添加注释
COMMENT ON TABLE withdrawal_requests IS '提现申请表，支持PayPal自动化提现';
COMMENT ON COLUMN withdrawal_requests.amount IS '申请提现金额（美元）';
COMMENT ON COLUMN withdrawal_requests.paypal_fee IS 'PayPal手续费（美元）';
COMMENT ON COLUMN withdrawal_requests.tax_fee IS '税费（美元）';
COMMENT ON COLUMN withdrawal_requests.net_amount IS '实际到账金额（美元）';
COMMENT ON COLUMN withdrawal_requests.status IS '提现状态：pending, processing, completed, failed, cancelled';
