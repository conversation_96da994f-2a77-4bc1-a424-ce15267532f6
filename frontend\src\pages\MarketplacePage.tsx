import {
  AutoAwesome,
  Build,
  CloudUpload,
  Download,
  Favorite,
  FavoriteBorder,
  FilterList,
  PlayArrow,
  Search,
  Star,
  TrendingUp,
} from '@mui/icons-material';
import {
  Avatar,
  Box,
  Button,
  Card,
  CardContent,
  CardMedia,
  Chip,
  Container,
  Divider,
  FormControl,
  Grid,
  InputLabel,
  MenuItem,
  Pagination,
  Rating,
  Select,
  Stack,
  TextField,
  Typography,
} from '@mui/material';
import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import Layout from '../components/Layout';

// 资源分类配置
const categories = [
  { id: 'all', name: '全部', icon: <CloudUpload /> },
  { id: 'fine_tuned_models', name: '微调模型', icon: <AutoAwesome /> },
  { id: 'lora', name: 'LoRA', icon: <TrendingUp /> },
  { id: 'workflows', name: '工作流', icon: <PlayArrow /> },
  { id: 'prompts', name: '提示词', icon: <Star /> },
  { id: 'tools', name: '工具', icon: <Build /> },
];

// 排序选项
const sortOptions = [
  { value: 'latest', label: '最新发布' },
  { value: 'popular', label: '最受欢迎' },
  { value: 'price_low', label: '价格从低到高' },
  { value: 'price_high', label: '价格从高到低' },
  { value: 'rating', label: '评分最高' },
];

// 模拟资源数据
const mockResources = [
  {
    id: '1',
    title: 'AI人像生成LoRA模型',
    description:
      '专业的AI人像生成模型，支持多种风格和表情控制，适用于Stable Diffusion 1.5和SDXL。包含详细使用说明和示例图片。',
    price: 29.99,
    category: 'lora',
    creator: {
      name: '专业创作者',
      avatar: '',
      verified: true,
    },
    downloads: 1560,
    rating: 4.8,
    reviewCount: 234,
    tags: ['人像', 'LoRA', 'Stable Diffusion', 'SDXL'],
    image: '/api/placeholder/300/200',
    featured: true,
    createdAt: '2024-01-15',
  },
  {
    id: '2',
    title: 'ComfyUI完整工作流',
    description:
      '一键生成高质量图像的完整工作流，包含图像生成、风格转换、细节增强等多个节点。',
    price: 19.99,
    category: 'workflows',
    creator: {
      name: 'AI工作室',
      avatar: '',
      verified: true,
    },
    downloads: 2340,
    rating: 4.9,
    reviewCount: 456,
    tags: ['ComfyUI', '工作流', '图像生成'],
    image: '/api/placeholder/300/200',
    featured: false,
    createdAt: '2024-01-10',
  },
  {
    id: '3',
    title: '商业摄影提示词集',
    description:
      '精心整理的商业摄影提示词合集，包含产品摄影、人像摄影、建筑摄影等多个类别。',
    price: 9.99,
    category: 'prompts',
    creator: {
      name: '摄影大师',
      avatar: '',
      verified: false,
    },
    downloads: 890,
    rating: 4.7,
    reviewCount: 123,
    tags: ['提示词', '商业摄影', '产品摄影'],
    image: '/api/placeholder/300/200',
    featured: false,
    createdAt: '2024-01-08',
  },
  {
    id: '4',
    title: 'AI图像增强工具',
    description:
      '基于深度学习的图像增强工具，支持超分辨率、去噪、色彩增强等功能。',
    price: 49.99,
    category: 'tools',
    creator: {
      name: '技术专家',
      avatar: '',
      verified: true,
    },
    downloads: 567,
    rating: 4.6,
    reviewCount: 89,
    tags: ['工具', '图像增强', '超分辨率'],
    image: '/api/placeholder/300/200',
    featured: true,
    createdAt: '2024-01-12',
  },
  {
    id: '5',
    title: 'GPT-4微调模型',
    description:
      '针对特定领域优化的GPT-4微调模型，在代码生成和技术文档编写方面表现优异。',
    price: 99.99,
    category: 'fine_tuned_models',
    creator: {
      name: 'AI研究院',
      avatar: '',
      verified: true,
    },
    downloads: 234,
    rating: 4.9,
    reviewCount: 67,
    tags: ['GPT-4', '微调模型', '代码生成'],
    image: '/api/placeholder/300/200',
    featured: false,
    createdAt: '2024-01-05',
  },
  {
    id: '6',
    title: '动漫风格LoRA模型',
    description:
      '专门用于生成动漫风格图像的LoRA模型，支持多种动漫画风和角色设计。',
    price: 24.99,
    category: 'lora',
    creator: {
      name: '动漫创作者',
      avatar: '',
      verified: false,
    },
    downloads: 1890,
    rating: 4.5,
    reviewCount: 345,
    tags: ['动漫', 'LoRA', '角色设计'],
    image: '/api/placeholder/300/200',
    featured: false,
    createdAt: '2024-01-03',
  },
];

const MarketplacePage: React.FC = () => {
  const navigate = useNavigate();
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [sortBy, setSortBy] = useState('latest');
  const [currentPage, setCurrentPage] = useState(1);
  const [favorites, setFavorites] = useState<string[]>([]);

  const itemsPerPage = 6;

  // 过滤和排序资源
  const filteredResources = mockResources
    .filter(resource => {
      const matchesSearch =
        resource.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        resource.description
          .toLowerCase()
          .includes(searchQuery.toLowerCase()) ||
        resource.tags.some(tag =>
          tag.toLowerCase().includes(searchQuery.toLowerCase())
        );
      const matchesCategory =
        selectedCategory === 'all' || resource.category === selectedCategory;
      return matchesSearch && matchesCategory;
    })
    .sort((a, b) => {
      switch (sortBy) {
        case 'popular':
          return b.downloads - a.downloads;
        case 'price_low':
          return a.price - b.price;
        case 'price_high':
          return b.price - a.price;
        case 'rating':
          return b.rating - a.rating;
        case 'latest':
        default:
          return (
            new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
          );
      }
    });

  const totalPages = Math.ceil(filteredResources.length / itemsPerPage);
  const paginatedResources = filteredResources.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  );

  const handleFavoriteToggle = (resourceId: string) => {
    setFavorites(prev =>
      prev.includes(resourceId)
        ? prev.filter(id => id !== resourceId)
        : [...prev, resourceId]
    );
  };

  const handleResourceClick = (resourceId: string) => {
    navigate(`/resource/${resourceId}`);
  };

  return (
    <Layout>
      <Box sx={{ bgcolor: 'grey.50', minHeight: '100vh', py: 4 }}>
        <Container maxWidth='lg'>
          {/* 页面标题 */}
          <Box sx={{ mb: 4 }}>
            <Typography
              variant='h3'
              component='h1'
              fontWeight='bold'
              gutterBottom
            >
              AI资源市场
            </Typography>
            <Typography variant='h6' color='text.secondary'>
              发现和购买高质量的AI创作资源
            </Typography>
          </Box>

          {/* 搜索和筛选区域 */}
          <Card sx={{ mb: 4, p: 3 }}>
            <Grid container spacing={3} alignItems='center'>
              {/* 搜索框 */}
              <Grid item xs={12} md={4}>
                <TextField
                  fullWidth
                  placeholder='搜索资源、创作者或标签...'
                  value={searchQuery}
                  onChange={e => setSearchQuery(e.target.value)}
                  InputProps={{
                    startAdornment: (
                      <Search sx={{ mr: 1, color: 'text.secondary' }} />
                    ),
                  }}
                />
              </Grid>

              {/* 分类筛选 */}
              <Grid item xs={12} md={3}>
                <FormControl fullWidth>
                  <InputLabel>分类</InputLabel>
                  <Select
                    value={selectedCategory}
                    label='分类'
                    onChange={e => setSelectedCategory(e.target.value)}
                  >
                    {categories.map(category => (
                      <MenuItem key={category.id} value={category.id}>
                        <Box
                          sx={{ display: 'flex', alignItems: 'center', gap: 1 }}
                        >
                          {category.icon}
                          {category.name}
                        </Box>
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>

              {/* 排序 */}
              <Grid item xs={12} md={3}>
                <FormControl fullWidth>
                  <InputLabel>排序</InputLabel>
                  <Select
                    value={sortBy}
                    label='排序'
                    onChange={e => setSortBy(e.target.value)}
                  >
                    {sortOptions.map(option => (
                      <MenuItem key={option.value} value={option.value}>
                        {option.label}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>

              {/* 筛选按钮 */}
              <Grid item xs={12} md={2}>
                <Button
                  fullWidth
                  variant='outlined'
                  startIcon={<FilterList />}
                  sx={{ height: 56 }}
                >
                  高级筛选
                </Button>
              </Grid>
            </Grid>
          </Card>

          {/* 结果统计 */}
          <Box sx={{ mb: 3 }}>
            <Typography variant='body1' color='text.secondary'>
              找到 {filteredResources.length} 个资源
            </Typography>
          </Box>

          {/* 资源网格 */}
          <Grid container spacing={3}>
            {paginatedResources.map(resource => (
              <Grid item xs={12} sm={6} md={4} key={resource.id}>
                <Card
                  sx={{
                    height: '100%',
                    cursor: 'pointer',
                    transition: 'all 0.3s ease',
                    '&:hover': {
                      transform: 'translateY(-4px)',
                      boxShadow: 4,
                    },
                  }}
                  onClick={() => handleResourceClick(resource.id)}
                >
                  {/* 资源图片 */}
                  <CardMedia
                    component='div'
                    sx={{
                      height: 200,
                      bgcolor: 'grey.200',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      position: 'relative',
                    }}
                  >
                    <CloudUpload
                      sx={{ fontSize: 48, color: 'text.secondary' }}
                    />

                    {/* 特色标签 */}
                    {resource.featured && (
                      <Chip
                        label='精选'
                        color='primary'
                        size='small'
                        sx={{
                          position: 'absolute',
                          top: 8,
                          left: 8,
                        }}
                      />
                    )}

                    {/* 收藏按钮 */}
                    <Button
                      size='small'
                      sx={{
                        position: 'absolute',
                        top: 8,
                        right: 8,
                        minWidth: 'auto',
                        p: 0.5,
                        bgcolor: 'rgba(255,255,255,0.9)',
                        '&:hover': {
                          bgcolor: 'rgba(255,255,255,1)',
                        },
                      }}
                      onClick={e => {
                        e.stopPropagation();
                        handleFavoriteToggle(resource.id);
                      }}
                    >
                      {favorites.includes(resource.id) ? (
                        <Favorite sx={{ color: 'error.main' }} />
                      ) : (
                        <FavoriteBorder />
                      )}
                    </Button>
                  </CardMedia>

                  <CardContent sx={{ p: 2 }}>
                    {/* 分类和价格 */}
                    <Box
                      sx={{
                        display: 'flex',
                        justifyContent: 'space-between',
                        alignItems: 'center',
                        mb: 1,
                      }}
                    >
                      <Chip
                        label={
                          categories.find(cat => cat.id === resource.category)
                            ?.name
                        }
                        size='small'
                        color='primary'
                        variant='outlined'
                      />
                      <Typography
                        variant='h6'
                        color='primary'
                        fontWeight='bold'
                      >
                        ${resource.price}
                      </Typography>
                    </Box>

                    {/* 标题 */}
                    <Typography
                      variant='h6'
                      fontWeight='bold'
                      gutterBottom
                      sx={{
                        overflow: 'hidden',
                        textOverflow: 'ellipsis',
                        display: '-webkit-box',
                        WebkitLineClamp: 2,
                        WebkitBoxOrient: 'vertical',
                      }}
                    >
                      {resource.title}
                    </Typography>

                    {/* 描述 */}
                    <Typography
                      variant='body2'
                      color='text.secondary'
                      sx={{
                        mb: 2,
                        overflow: 'hidden',
                        textOverflow: 'ellipsis',
                        display: '-webkit-box',
                        WebkitLineClamp: 2,
                        WebkitBoxOrient: 'vertical',
                      }}
                    >
                      {resource.description}
                    </Typography>

                    {/* 创作者信息 */}
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                      <Avatar
                        sx={{
                          width: 24,
                          height: 24,
                          mr: 1,
                          bgcolor: 'primary.main',
                        }}
                      >
                        {resource.creator.name.charAt(0)}
                      </Avatar>
                      <Typography variant='body2' color='text.secondary'>
                        {resource.creator.name}
                      </Typography>
                      {resource.creator.verified && (
                        <Chip
                          label='已验证'
                          size='small'
                          color='success'
                          sx={{ ml: 1, height: 20 }}
                        />
                      )}
                    </Box>

                    {/* 标签 */}
                    <Box sx={{ mb: 2 }}>
                      <Stack direction='row' spacing={0.5} flexWrap='wrap'>
                        {resource.tags.slice(0, 3).map(tag => (
                          <Chip
                            key={tag}
                            label={tag}
                            size='small'
                            variant='outlined'
                            sx={{ fontSize: '0.7rem', height: 20 }}
                          />
                        ))}
                        {resource.tags.length > 3 && (
                          <Chip
                            label={`+${resource.tags.length - 3}`}
                            size='small'
                            variant='outlined'
                            sx={{ fontSize: '0.7rem', height: 20 }}
                          />
                        )}
                      </Stack>
                    </Box>

                    <Divider sx={{ mb: 2 }} />

                    {/* 统计信息 */}
                    <Box
                      sx={{
                        display: 'flex',
                        justifyContent: 'space-between',
                        alignItems: 'center',
                      }}
                    >
                      <Box
                        sx={{ display: 'flex', alignItems: 'center', gap: 2 }}
                      >
                        <Box
                          sx={{
                            display: 'flex',
                            alignItems: 'center',
                            gap: 0.5,
                          }}
                        >
                          <Download fontSize='small' color='action' />
                          <Typography variant='body2' color='text.secondary'>
                            {resource.downloads.toLocaleString()}
                          </Typography>
                        </Box>

                        <Box
                          sx={{
                            display: 'flex',
                            alignItems: 'center',
                            gap: 0.5,
                          }}
                        >
                          <Rating
                            value={resource.rating}
                            precision={0.1}
                            size='small'
                            readOnly
                          />
                          <Typography variant='body2' color='text.secondary'>
                            ({resource.reviewCount})
                          </Typography>
                        </Box>
                      </Box>
                    </Box>
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>

          {/* 分页 */}
          {totalPages > 1 && (
            <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
              <Pagination
                count={totalPages}
                page={currentPage}
                onChange={(_, page) => setCurrentPage(page)}
                color='primary'
                size='large'
              />
            </Box>
          )}
        </Container>
      </Box>
    </Layout>
  );
};

export default MarketplacePage;
