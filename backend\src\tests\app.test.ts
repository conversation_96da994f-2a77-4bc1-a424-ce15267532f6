import { describe, it, expect } from 'vitest';

describe('Backend Configuration Tests', () => {
  it('should load environment variables', () => {
    // 测试环境变量是否正确加载
    expect(process.env.NODE_ENV).toBeDefined();
    expect(process.env.PORT).toBeDefined();
    expect(process.env.JWT_SECRET).toBeDefined();
  });

  it('should have correct app configuration', () => {
    // 测试应用配置
    const { config } = require('@/config/index');

    expect(config.port).toBe(3000);
    expect(config.nodeEnv).toBe('development');
    expect(config.jwtSecret).toBeDefined();
    expect(config.corsOrigin).toBe('http://localhost:5173');
  });

  it('should export required types', () => {
    // 测试类型定义是否正确导出
    const types = require('@/types/index');

    expect(types.AppError).toBeDefined();
    expect(typeof types.AppError).toBe('function');
  });

  it('should have middleware functions', () => {
    // 测试中间件是否正确导出
    const { errorHandler, asyncHandler } = require('@/middleware/errorHandler');
    const { notFoundHandler } = require('@/middleware/notFoundHandler');
    const { rateLimiter } = require('@/middleware/rateLimiter');

    expect(typeof errorHandler).toBe('function');
    expect(typeof asyncHandler).toBe('function');
    expect(typeof notFoundHandler).toBe('function');
    expect(typeof rateLimiter).toBe('function');
  });

  it('should have route handlers', () => {
    // 测试路由是否正确导出
    const { healthRoutes } = require('@/routes/health');
    const { authRoutes } = require('@/routes/auth');

    expect(healthRoutes).toBeDefined();
    expect(authRoutes).toBeDefined();
  });
});
