import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import morgan from 'morgan';
import { config, featureFlags } from '@config/index';
import { errorHandler } from '@middleware/errorHandler';
import { notFoundHandler } from '@middleware/notFoundHandler';
import { requestLogger } from '@middleware/requestLogger';
import { rateLimiter } from '@middleware/rateLimiter';
import { authRoutes } from '@routes/auth';
import { healthRoutes } from '@routes/health';

// 创建Express应用
const app = express();

// 信任代理 (用于部署在负载均衡器后面)
app.set('trust proxy', 1);

// 安全中间件
app.use(
  helmet({
    contentSecurityPolicy: {
      directives: {
        defaultSrc: ["'self'"],
        styleSrc: ["'self'", "'unsafe-inline'"],
        scriptSrc: ["'self'"],
        imgSrc: ["'self'", 'data:', 'https:'],
        connectSrc: ["'self'"],
        fontSrc: ["'self'"],
        objectSrc: ["'none'"],
        mediaSrc: ["'self'"],
        frameSrc: ["'none'"],
      },
    },
    crossOriginEmbedderPolicy: false,
  })
);

// CORS配置
if (featureFlags.enableCors) {
  app.use(
    cors({
      origin: config.corsOrigin,
      credentials: true,
      methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
      allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
      exposedHeaders: ['X-Total-Count', 'X-Page-Count'],
    })
  );
}

// 请求日志中间件
if (featureFlags.enableRequestLogging) {
  if (config.nodeEnv === 'development') {
    app.use(morgan('dev'));
  } else {
    app.use(morgan('combined'));
  }
  app.use(requestLogger);
}

// 限流中间件
if (featureFlags.enableRateLimiting) {
  app.use('/api', rateLimiter);
}

// 解析JSON和URL编码的请求体
app.use(
  express.json({
    limit: '10mb',
    verify: (req, _res, buf) => {
      // 保存原始请求体用于webhook验证
      (req as any).rawBody = buf;
    },
  })
);
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// 静态文件服务 (如果需要)
app.use('/static', express.static('public'));

// API路由
app.use('/api/health', healthRoutes);
app.use('/api/auth', authRoutes);

// 根路径响应
app.get('/', (_req, res) => {
  res.json({
    success: true,
    message: 'AIGC Service Hub API',
    version: process.env.APP_VERSION || '1.0.0',
    environment: config.nodeEnv,
    timestamp: new Date().toISOString(),
  });
});

// API信息路径
app.get('/api', (_req, res) => {
  res.json({
    success: true,
    message: 'AIGC Service Hub API',
    version: process.env.APP_VERSION || '1.0.0',
    environment: config.nodeEnv,
    endpoints: {
      health: '/api/health',
      auth: '/api/auth',
      resources: '/api/resources',
      transactions: '/api/transactions',
      finance: '/api/finance',
      points: '/api/points',
      ranking: '/api/ranking',
    },
    documentation: '/api/docs',
    timestamp: new Date().toISOString(),
  });
});

// 404处理中间件
app.use(notFoundHandler);

// 错误处理中间件 (必须放在最后)
app.use(errorHandler);

export default app;
