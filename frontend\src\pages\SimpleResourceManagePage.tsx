import {
  Add,
  AttachMoney,
  CloudUpload,
  Download,
  Edit,
  FilterList,
  MoreVert,
  Refresh,
  Search,
  Visibility,
} from '@mui/icons-material';
import {
  <PERSON>ert,
  Box,
  Button,
  Card,
  CardContent,
  CardMedia,
  Chip,
  Container,
  FormControl,
  Grid,
  IconButton,
  InputLabel,
  MenuItem,
  Paper,
  Select,
  Skeleton,
  Tab,
  TablePagination,
  Tabs,
  TextField,
  Typography,
} from '@mui/material';
import React, { useEffect, useState } from 'react';

// 资源分类常量
const RESOURCE_CATEGORIES = [
  { value: 'fine_tuned_models', label: '微调模型' },
  { value: 'lora', label: 'LoRA' },
  { value: 'workflows', label: '工作流' },
  { value: 'prompts', label: '提示词' },
  { value: 'tools', label: '工具' },
];

// 资源状态常量
const RESOURCE_STATUS = [
  { value: 'active', label: '已发布', color: 'success' },
  { value: 'inactive', label: '已下架', color: 'default' },
  { value: 'pending_review', label: '审核中', color: 'warning' },
  { value: 'rejected', label: '已拒绝', color: 'error' },
  { value: 'draft', label: '草稿', color: 'info' },
];

// 模拟资源数据类型
interface Resource {
  id: string;
  title: string;
  description: string;
  category: string;
  tags: string[];
  priceUsd: number;
  pricePoints: number;
  fileSize: number;
  coverImageUrl?: string;
  downloadCount: number;
  salesCount: number;
  revenue: number;
  status: 'active' | 'inactive' | 'pending_review' | 'rejected' | 'draft';
  createdAt: string;
  updatedAt: string;
}

// Tab面板组件
interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;
  return (
    <div
      role='tabpanel'
      hidden={value !== index}
      id={`resource-tabpanel-${index}`}
      aria-labelledby={`resource-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ py: 3 }}>{children}</Box>}
    </div>
  );
}

const SimpleResourceManagePage: React.FC = () => {
  const [tabValue, setTabValue] = useState(0);
  const [loading, setLoading] = useState(true);
  const [resources, setResources] = useState<Resource[]>([]);
  const [filteredResources, setFilteredResources] = useState<Resource[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [categoryFilter, setCategoryFilter] = useState<string>('all');
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(6);

  // 模拟数据加载
  useEffect(() => {
    const loadResources = async () => {
      setLoading(true);
      // 模拟API调用延迟
      await new Promise(resolve => setTimeout(resolve, 1000));

      // 模拟资源数据
      const mockResources: Resource[] = [
        {
          id: '1',
          title: 'AI人像生成LoRA模型',
          description:
            '专业的AI人像生成LoRA模型，支持多种风格和表情控制，适用于Stable Diffusion 1.5和SDXL。',
          category: 'lora',
          tags: ['人像', 'AI生成', '风格化', 'Stable Diffusion'],
          priceUsd: 29.99,
          pricePoints: 2999,
          fileSize: 144000000, // 144MB
          coverImageUrl: '/images/ai-portrait-cover.jpg',
          downloadCount: 156,
          salesCount: 23,
          revenue: 689.77,
          status: 'active',
          createdAt: '2024-01-15T10:30:00Z',
          updatedAt: '2024-01-20T14:22:00Z',
        },
        {
          id: '2',
          title: 'Stable Diffusion工作流',
          description:
            'ComfyUI完整工作流，包含图像生成、风格转换、细节增强等多个节点，一键生成高质量图像。',
          category: 'workflows',
          tags: ['ComfyUI', '工作流', '图像生成', '自动化'],
          priceUsd: 19.99,
          pricePoints: 1999,
          fileSize: 2500000, // 2.5MB
          coverImageUrl: '/images/workflow-cover.jpg',
          downloadCount: 234,
          salesCount: 45,
          revenue: 899.55,
          status: 'active',
          createdAt: '2024-01-10T09:15:00Z',
          updatedAt: '2024-01-18T16:45:00Z',
        },
        {
          id: '3',
          title: '商业摄影提示词集',
          description:
            '精心整理的商业摄影提示词集合，包含产品摄影、人像摄影、建筑摄影等多个类别。',
          category: 'prompts',
          tags: ['提示词', '商业摄影', '产品摄影', '专业'],
          priceUsd: 9.99,
          pricePoints: 999,
          fileSize: 150000, // 150KB
          coverImageUrl: '/images/prompts-cover.jpg',
          downloadCount: 89,
          salesCount: 12,
          revenue: 119.88,
          status: 'pending_review',
          createdAt: '2024-01-08T14:20:00Z',
          updatedAt: '2024-01-08T14:20:00Z',
        },
        {
          id: '4',
          title: 'AI图像增强工具',
          description:
            '基于深度学习的图像增强工具，支持超分辨率、去噪、色彩增强等功能。',
          category: 'tools',
          tags: ['图像增强', '超分辨率', '去噪', 'Python'],
          priceUsd: 49.99,
          pricePoints: 4999,
          fileSize: 25000000, // 25MB
          coverImageUrl: '/images/enhancer-cover.jpg',
          downloadCount: 67,
          salesCount: 8,
          revenue: 399.92,
          status: 'active',
          createdAt: '2024-01-05T11:10:00Z',
          updatedAt: '2024-01-12T13:30:00Z',
        },
        {
          id: '5',
          title: 'GPT-4微调模型',
          description:
            '针对特定领域优化的GPT-4微调模型，在代码生成和技术文档编写方面表现优异。',
          category: 'fine_tuned_models',
          tags: ['GPT-4', '微调', '代码生成', '技术文档'],
          priceUsd: 99.99,
          pricePoints: 9999,
          fileSize: 500000000, // 500MB
          coverImageUrl: '/images/gpt4-cover.jpg',
          downloadCount: 34,
          salesCount: 5,
          revenue: 499.95,
          status: 'draft',
          createdAt: '2024-01-03T16:45:00Z',
          updatedAt: '2024-01-15T10:20:00Z',
        },
      ];

      setResources(mockResources);
      setFilteredResources(mockResources);
      setLoading(false);
    };

    loadResources();
  }, []);

  // 筛选和搜索逻辑
  useEffect(() => {
    let filtered = resources;

    // 搜索筛选
    if (searchQuery) {
      filtered = filtered.filter(
        resource =>
          resource.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
          resource.description
            .toLowerCase()
            .includes(searchQuery.toLowerCase()) ||
          resource.tags.some(tag =>
            tag.toLowerCase().includes(searchQuery.toLowerCase())
          )
      );
    }

    // 状态筛选
    if (statusFilter !== 'all') {
      filtered = filtered.filter(resource => resource.status === statusFilter);
    }

    // 分类筛选
    if (categoryFilter !== 'all') {
      filtered = filtered.filter(
        resource => resource.category === categoryFilter
      );
    }

    setFilteredResources(filtered);
    setPage(0); // 重置页码
  }, [resources, searchQuery, statusFilter, categoryFilter]);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const handleChangePage = (event: unknown, newPage: number) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  // 获取状态标签颜色
  const getStatusColor = (status: string) => {
    const statusObj = RESOURCE_STATUS.find(s => s.value === status);
    return statusObj?.color || 'default';
  };

  // 获取状态标签文本
  const getStatusText = (status: string) => {
    const statusObj = RESOURCE_STATUS.find(s => s.value === status);
    return statusObj?.label || status;
  };

  // 获取分类标签文本
  const getCategoryText = (category: string) => {
    const categoryObj = RESOURCE_CATEGORIES.find(c => c.value === category);
    return categoryObj?.label || category;
  };

  // 格式化文件大小
  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // 格式化日期
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  return (
    <Container maxWidth='xl' sx={{ py: 4 }}>
      {/* 页面标题和操作 */}
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          mb: 4,
        }}
      >
        <Typography variant='h4' component='h1' fontWeight='bold'>
          资源管理
        </Typography>
        <Button
          variant='contained'
          startIcon={<Add />}
          size='large'
          onClick={() => (window.location.href = '/upload')}
        >
          上传新资源
        </Button>
      </Box>

      {/* 统计卡片 */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <CloudUpload color='primary' />
                <Box>
                  <Typography variant='h6' fontWeight='bold'>
                    {resources.length}
                  </Typography>
                  <Typography variant='body2' color='text.secondary'>
                    总资源数
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <Visibility color='success' />
                <Box>
                  <Typography variant='h6' fontWeight='bold'>
                    {resources.filter(r => r.status === 'active').length}
                  </Typography>
                  <Typography variant='body2' color='text.secondary'>
                    已发布
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <Download color='info' />
                <Box>
                  <Typography variant='h6' fontWeight='bold'>
                    {resources.reduce((sum, r) => sum + r.downloadCount, 0)}
                  </Typography>
                  <Typography variant='body2' color='text.secondary'>
                    总下载量
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <AttachMoney color='warning' />
                <Box>
                  <Typography variant='h6' fontWeight='bold'>
                    $
                    {resources
                      .reduce((sum, r) => sum + r.revenue, 0)
                      .toFixed(2)}
                  </Typography>
                  <Typography variant='body2' color='text.secondary'>
                    总收入
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Tab导航和筛选 */}
      <Paper sx={{ mb: 4 }}>
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs
            value={tabValue}
            onChange={handleTabChange}
            aria-label='资源管理导航'
          >
            <Tab label='卡片视图' />
            <Tab label='列表视图' />
            <Tab label='数据分析' />
          </Tabs>
        </Box>

        {/* 筛选和搜索栏 */}
        <Box sx={{ p: 3, borderBottom: 1, borderColor: 'divider' }}>
          <Grid container spacing={2} alignItems='center'>
            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                placeholder='搜索资源标题、描述或标签...'
                value={searchQuery}
                onChange={e => setSearchQuery(e.target.value)}
                InputProps={{
                  startAdornment: (
                    <Search sx={{ mr: 1, color: 'text.secondary' }} />
                  ),
                }}
              />
            </Grid>

            <Grid item xs={12} sm={6} md={2}>
              <FormControl fullWidth>
                <InputLabel>状态筛选</InputLabel>
                <Select
                  value={statusFilter}
                  label='状态筛选'
                  onChange={e => setStatusFilter(e.target.value)}
                >
                  <MenuItem value='all'>全部状态</MenuItem>
                  {RESOURCE_STATUS.map(status => (
                    <MenuItem key={status.value} value={status.value}>
                      {status.label}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12} sm={6} md={2}>
              <FormControl fullWidth>
                <InputLabel>分类筛选</InputLabel>
                <Select
                  value={categoryFilter}
                  label='分类筛选'
                  onChange={e => setCategoryFilter(e.target.value)}
                >
                  <MenuItem value='all'>全部分类</MenuItem>
                  {RESOURCE_CATEGORIES.map(category => (
                    <MenuItem key={category.value} value={category.value}>
                      {category.label}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12} md={4}>
              <Box sx={{ display: 'flex', gap: 1, justifyContent: 'flex-end' }}>
                <Button
                  variant='outlined'
                  startIcon={<FilterList />}
                  onClick={() => {
                    setSearchQuery('');
                    setStatusFilter('all');
                    setCategoryFilter('all');
                  }}
                >
                  清除筛选
                </Button>
                <Button
                  variant='outlined'
                  startIcon={<Refresh />}
                  onClick={() => window.location.reload()}
                >
                  刷新
                </Button>
              </Box>
            </Grid>
          </Grid>
        </Box>

        {/* 卡片视图 */}
        <TabPanel value={tabValue} index={0}>
          {loading ? (
            <Grid container spacing={3} sx={{ p: 3 }}>
              {[1, 2, 3, 4, 5, 6].map(item => (
                <Grid item xs={12} sm={6} md={4} key={item}>
                  <Card>
                    <Skeleton variant='rectangular' height={200} />
                    <CardContent>
                      <Skeleton variant='text' width='80%' height={24} />
                      <Skeleton variant='text' width='60%' height={20} />
                      <Skeleton variant='text' width='40%' height={20} />
                    </CardContent>
                  </Card>
                </Grid>
              ))}
            </Grid>
          ) : filteredResources.length === 0 ? (
            <Box sx={{ p: 6, textAlign: 'center' }}>
              <CloudUpload
                sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }}
              />
              <Typography variant='h6' color='text.secondary' gutterBottom>
                {searchQuery ||
                statusFilter !== 'all' ||
                categoryFilter !== 'all'
                  ? '没有找到匹配的资源'
                  : '还没有上传任何资源'}
              </Typography>
              <Typography variant='body2' color='text.secondary' sx={{ mb: 3 }}>
                {searchQuery ||
                statusFilter !== 'all' ||
                categoryFilter !== 'all'
                  ? '请尝试调整搜索条件或筛选器'
                  : '点击上传按钮开始分享您的AI资源'}
              </Typography>
              <Button
                variant='contained'
                startIcon={<Add />}
                onClick={() => (window.location.href = '/upload')}
              >
                上传新资源
              </Button>
            </Box>
          ) : (
            <Grid container spacing={3} sx={{ p: 3 }}>
              {filteredResources
                .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
                .map(resource => (
                  <Grid item xs={12} sm={6} md={4} key={resource.id}>
                    <Card
                      sx={{
                        height: '100%',
                        display: 'flex',
                        flexDirection: 'column',
                      }}
                    >
                      <CardMedia
                        component='div'
                        sx={{
                          height: 200,
                          bgcolor: 'grey.200',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          position: 'relative',
                        }}
                      >
                        {resource.coverImageUrl ? (
                          <img
                            src={resource.coverImageUrl}
                            alt={resource.title}
                            style={{
                              width: '100%',
                              height: '100%',
                              objectFit: 'cover',
                            }}
                          />
                        ) : (
                          <CloudUpload
                            sx={{ fontSize: 48, color: 'text.secondary' }}
                          />
                        )}

                        {/* 状态标签 */}
                        <Chip
                          label={getStatusText(resource.status)}
                          color={getStatusColor(resource.status) as any}
                          size='small'
                          sx={{ position: 'absolute', top: 8, left: 8 }}
                        />

                        {/* 操作菜单 */}
                        <IconButton
                          sx={{
                            position: 'absolute',
                            top: 8,
                            right: 8,
                            bgcolor: 'rgba(255,255,255,0.8)',
                          }}
                        >
                          <MoreVert />
                        </IconButton>
                      </CardMedia>

                      <CardContent sx={{ flexGrow: 1 }}>
                        <Typography variant='h6' gutterBottom noWrap>
                          {resource.title}
                        </Typography>

                        <Typography
                          variant='body2'
                          color='text.secondary'
                          sx={{ mb: 2 }}
                        >
                          {resource.description.length > 100
                            ? `${resource.description.substring(0, 100)}...`
                            : resource.description}
                        </Typography>

                        <Box
                          sx={{
                            display: 'flex',
                            gap: 1,
                            mb: 2,
                            flexWrap: 'wrap',
                          }}
                        >
                          <Chip
                            label={getCategoryText(resource.category)}
                            size='small'
                            color='primary'
                            variant='outlined'
                          />
                          {resource.tags.slice(0, 2).map(tag => (
                            <Chip
                              key={tag}
                              label={tag}
                              size='small'
                              variant='outlined'
                            />
                          ))}
                          {resource.tags.length > 2 && (
                            <Chip
                              label={`+${resource.tags.length - 2}`}
                              size='small'
                              variant='outlined'
                            />
                          )}
                        </Box>

                        <Box
                          sx={{
                            display: 'flex',
                            justifyContent: 'space-between',
                            alignItems: 'center',
                            mb: 2,
                          }}
                        >
                          <Typography variant='h6' color='primary'>
                            ${resource.priceUsd}
                          </Typography>
                          <Typography variant='body2' color='text.secondary'>
                            {formatFileSize(resource.fileSize)}
                          </Typography>
                        </Box>

                        <Box sx={{ display: 'flex', gap: 3, mb: 2 }}>
                          <Box
                            sx={{
                              display: 'flex',
                              alignItems: 'center',
                              gap: 0.5,
                            }}
                          >
                            <Download fontSize='small' color='action' />
                            <Typography variant='body2'>
                              {resource.downloadCount}
                            </Typography>
                          </Box>
                          <Box
                            sx={{
                              display: 'flex',
                              alignItems: 'center',
                              gap: 0.5,
                            }}
                          >
                            <AttachMoney fontSize='small' color='action' />
                            <Typography variant='body2'>
                              ${resource.revenue.toFixed(2)}
                            </Typography>
                          </Box>
                        </Box>

                        <Typography variant='caption' color='text.secondary'>
                          更新于 {formatDate(resource.updatedAt)}
                        </Typography>

                        <Box sx={{ mt: 2, display: 'flex', gap: 1 }}>
                          <Button size='small' startIcon={<Visibility />}>
                            查看
                          </Button>
                          <Button size='small' startIcon={<Edit />}>
                            编辑
                          </Button>
                        </Box>
                      </CardContent>
                    </Card>
                  </Grid>
                ))}
            </Grid>
          )}

          {/* 分页 */}
          {filteredResources.length > 0 && (
            <Box sx={{ display: 'flex', justifyContent: 'center', p: 2 }}>
              <TablePagination
                component='div'
                count={filteredResources.length}
                page={page}
                onPageChange={handleChangePage}
                rowsPerPage={rowsPerPage}
                onRowsPerPageChange={handleChangeRowsPerPage}
                rowsPerPageOptions={[6, 12, 24]}
                labelRowsPerPage='每页显示:'
                labelDisplayedRows={({ from, to, count }) =>
                  `${from}-${to} 共 ${count} 项`
                }
              />
            </Box>
          )}
        </TabPanel>

        {/* 列表视图 */}
        <TabPanel value={tabValue} index={1}>
          <Alert severity='info' sx={{ m: 3 }}>
            列表视图功能正在开发中，敬请期待。
          </Alert>
        </TabPanel>

        {/* 数据分析 */}
        <TabPanel value={tabValue} index={2}>
          <Alert severity='info' sx={{ m: 3 }}>
            数据分析功能正在开发中，敬请期待。
          </Alert>
        </TabPanel>
      </Paper>
    </Container>
  );
};

export default SimpleResourceManagePage;
