# AIGC Service Hub Frontend Development Dockerfile
FROM node:18-alpine

# 设置工作目录
WORKDIR /app

# 设置npm镜像源（可选，提高国内构建速度）
RUN npm config set registry https://registry.npmmirror.com

# 安装必要的工具
RUN apk add --no-cache curl

# 复制package文件
COPY package*.json ./

# 安装依赖
RUN npm ci && npm cache clean --force

# 复制源代码
COPY . .

# 暴露端口
EXPOSE 5173

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=10s --retries=3 \
    CMD curl -f http://localhost:5173/ || exit 1

# 启动开发服务器
CMD ["npm", "run", "dev", "--", "--host", "0.0.0.0"]
