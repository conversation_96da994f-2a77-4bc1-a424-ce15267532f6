import { Request, Response, NextFunction } from 'express';

// 请求日志中间件
export const requestLogger = (
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  const start = Date.now();

  // 记录请求开始
  console.log(
    `[${new Date().toISOString()}] ${req.method} ${req.originalUrl} - Started`
  );

  // 监听响应结束事件
  res.on('finish', () => {
    const duration = Date.now() - start;
    const logData = {
      timestamp: new Date().toISOString(),
      method: req.method,
      url: req.originalUrl,
      statusCode: res.statusCode,
      duration: `${duration}ms`,
      userAgent: req.get('User-Agent'),
      ip: req.ip || req.connection.remoteAddress,
      contentLength: res.get('Content-Length') || '0',
    };

    console.log(
      `[${logData.timestamp}] ${logData.method} ${logData.url} - ${logData.statusCode} - ${logData.duration}`
    );

    // 在开发环境中记录更详细的信息
    if (process.env.NODE_ENV === 'development') {
      console.log('Request details:', {
        ...logData,
        body: req.body,
        params: req.params,
        query: req.query,
      });
    }
  });

  next();
};
