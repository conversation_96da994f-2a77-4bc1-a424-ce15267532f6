# AIGC Service Hub 字体规范

## 🔤 字体系统概览

AIGC Service Hub 采用现代化的字体系统，确保在各种设备和平台上都能提供优秀的阅读体验。

### 字体层级

1. **主字体**: Inter (英文) + 思源黑体 (中文)
2. **代码字体**: JetBrains Mono
3. **装饰字体**: Poppins (特殊场景)

## 🌍 多语言字体配置

### 英文字体 - Inter

```css
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap');

.font-inter {
  font-family:
    'Inter',
    -apple-system,
    BlinkMacSystemFont,
    'Segoe UI',
    sans-serif;
}
```

**特点**:

- 现代无衬线字体
- 优秀的屏幕显示效果
- 支持多种字重
- 字符间距优化

### 中文字体 - 思源黑体

```css
@import url('https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@100;300;400;500;700;900&display=swap');

.font-chinese {
  font-family: 'Noto Sans SC', 'PingFang SC', 'Microsoft YaHei', sans-serif;
}
```

**特点**:

- Google开源字体
- 简洁现代的中文字形
- 多字重支持
- 跨平台兼容性好

### 代码字体 - JetBrains Mono

```css
@import url('https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@100;200;300;400;500;600;700;800&display=swap');

.font-mono {
  font-family: 'JetBrains Mono', 'Fira Code', 'Monaco', 'Consolas', monospace;
}
```

**特点**:

- 专为编程设计
- 字符清晰易辨认
- 支持连字符
- 等宽字体

## 📏 字体尺寸系统

### 基础尺寸规范

```css
/* 字体尺寸变量 */
:root {
  --text-xs: 0.75rem; /* 12px */
  --text-sm: 0.875rem; /* 14px */
  --text-base: 1rem; /* 16px - 基准尺寸 */
  --text-lg: 1.125rem; /* 18px */
  --text-xl: 1.25rem; /* 20px */
  --text-2xl: 1.5rem; /* 24px */
  --text-3xl: 1.875rem; /* 30px */
  --text-4xl: 2.25rem; /* 36px */
  --text-5xl: 3rem; /* 48px */
  --text-6xl: 3.75rem; /* 60px */
  --text-7xl: 4.5rem; /* 72px */
  --text-8xl: 6rem; /* 96px */
  --text-9xl: 8rem; /* 128px */
}
```

### 标题层级

```css
/* H1 - 主标题 */
.heading-1 {
  font-size: var(--text-4xl);
  font-weight: 700;
  line-height: 1.2;
  letter-spacing: -0.025em;
}

/* H2 - 二级标题 */
.heading-2 {
  font-size: var(--text-3xl);
  font-weight: 600;
  line-height: 1.3;
  letter-spacing: -0.025em;
}

/* H3 - 三级标题 */
.heading-3 {
  font-size: var(--text-2xl);
  font-weight: 600;
  line-height: 1.4;
}

/* H4 - 四级标题 */
.heading-4 {
  font-size: var(--text-xl);
  font-weight: 500;
  line-height: 1.4;
}

/* H5 - 五级标题 */
.heading-5 {
  font-size: var(--text-lg);
  font-weight: 500;
  line-height: 1.5;
}

/* H6 - 六级标题 */
.heading-6 {
  font-size: var(--text-base);
  font-weight: 500;
  line-height: 1.5;
}
```

### 正文文字

```css
/* 大号正文 */
.text-large {
  font-size: var(--text-lg);
  font-weight: 400;
  line-height: 1.6;
}

/* 标准正文 */
.text-body {
  font-size: var(--text-base);
  font-weight: 400;
  line-height: 1.6;
}

/* 小号正文 */
.text-small {
  font-size: var(--text-sm);
  font-weight: 400;
  line-height: 1.5;
}

/* 说明文字 */
.text-caption {
  font-size: var(--text-xs);
  font-weight: 400;
  line-height: 1.4;
}
```

## ⚖️ 字重规范

### 字重定义

```css
:root {
  --font-thin: 100;
  --font-extralight: 200;
  --font-light: 300;
  --font-normal: 400; /* 默认字重 */
  --font-medium: 500;
  --font-semibold: 600;
  --font-bold: 700;
  --font-extrabold: 800;
  --font-black: 900;
}
```

### 使用场景

```css
/* 标题使用 */
.title-primary {
  font-weight: var(--font-bold); /* 700 */
}

.title-secondary {
  font-weight: var(--font-semibold); /* 600 */
}

/* 正文使用 */
.text-emphasis {
  font-weight: var(--font-medium); /* 500 */
}

.text-normal {
  font-weight: var(--font-normal); /* 400 */
}

.text-light {
  font-weight: var(--font-light); /* 300 */
}
```

## 📐 行高和间距

### 行高规范

```css
:root {
  --leading-none: 1;
  --leading-tight: 1.25;
  --leading-snug: 1.375;
  --leading-normal: 1.5; /* 默认行高 */
  --leading-relaxed: 1.625;
  --leading-loose: 2;
}
```

### 字符间距

```css
:root {
  --tracking-tighter: -0.05em;
  --tracking-tight: -0.025em;
  --tracking-normal: 0em; /* 默认间距 */
  --tracking-wide: 0.025em;
  --tracking-wider: 0.05em;
  --tracking-widest: 0.1em;
}
```

### 应用示例

```css
/* 大标题 - 紧密间距 */
.display-title {
  letter-spacing: var(--tracking-tight);
  line-height: var(--leading-tight);
}

/* 正文 - 标准间距 */
.body-text {
  letter-spacing: var(--tracking-normal);
  line-height: var(--leading-normal);
}

/* 按钮文字 - 稍宽间距 */
.button-text {
  letter-spacing: var(--tracking-wide);
  line-height: var(--leading-none);
}
```

## 📱 响应式字体

### 移动端适配

```css
/* 基础响应式字体 */
.responsive-text {
  font-size: clamp(1rem, 2.5vw, 1.25rem);
}

/* 标题响应式 */
.responsive-heading {
  font-size: clamp(1.5rem, 4vw, 3rem);
}

/* 媒体查询适配 */
@media (max-width: 768px) {
  .heading-1 {
    font-size: var(--text-3xl);
  }
  .heading-2 {
    font-size: var(--text-2xl);
  }
  .heading-3 {
    font-size: var(--text-xl);
  }
}

@media (max-width: 480px) {
  .heading-1 {
    font-size: var(--text-2xl);
  }
  .heading-2 {
    font-size: var(--text-xl);
  }
  .heading-3 {
    font-size: var(--text-lg);
  }
}
```

## 🎨 特殊文字样式

### 强调样式

```css
/* 高亮文字 */
.text-highlight {
  background: linear-gradient(120deg, #a8edea 0%, #fed6e3 100%);
  background-size: 100% 0.2em;
  background-repeat: no-repeat;
  background-position: 0 88%;
}

/* 渐变文字 */
.text-gradient {
  background: linear-gradient(135deg, var(--primary-600), var(--secondary-600));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* 阴影文字 */
.text-shadow {
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}
```

### 代码文字样式

```css
/* 内联代码 */
.code-inline {
  font-family: var(--font-mono);
  font-size: 0.875em;
  background-color: var(--gray-100);
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
  color: var(--gray-800);
}

/* 代码块 */
.code-block {
  font-family: var(--font-mono);
  font-size: var(--text-sm);
  line-height: var(--leading-relaxed);
  background-color: var(--gray-900);
  color: var(--gray-100);
  padding: 1rem;
  border-radius: 0.5rem;
  overflow-x: auto;
}
```

## 🌐 国际化支持

### 多语言字体栈

```css
/* 通用字体栈 */
.font-universal {
  font-family:
    'Inter',
    /* 英文主字体 */ 'Noto Sans SC',
    /* 简体中文 */ 'Noto Sans TC',
    /* 繁体中文 */ 'Noto Sans JP',
    /* 日文 */ 'Noto Sans KR',
    /* 韩文 */ -apple-system,
    /* macOS */ BlinkMacSystemFont,
    /* macOS */ 'Segoe UI',
    /* Windows */ 'Roboto',
    /* Android */ sans-serif; /* 后备字体 */
}
```

### 语言特定样式

```css
/* 中文文本优化 */
:lang(zh) {
  font-feature-settings: 'kern' 1;
  text-spacing: ideograph-alpha ideograph-numeric;
}

/* 日文文本优化 */
:lang(ja) {
  font-feature-settings:
    'kern' 1,
    'liga' 1;
}

/* 阿拉伯文本优化 */
:lang(ar) {
  direction: rtl;
  text-align: right;
}
```

## ✅ 使用检查清单

### 设计阶段

- [ ] 是否使用了定义的字体系统
- [ ] 字体尺寸是否符合层级规范
- [ ] 行高和间距是否合适
- [ ] 是否考虑了多语言支持
- [ ] 移动端字体是否可读

### 开发阶段

- [ ] 是否正确引入了字体文件
- [ ] 是否使用了CSS变量
- [ ] 字体是否有合适的后备方案
- [ ] 是否实现了响应式字体
- [ ] 字体加载性能是否优化

### 测试阶段

- [ ] 在不同设备上字体显示是否正常
- [ ] 字体加载速度是否可接受
- [ ] 无障碍阅读是否友好
- [ ] 多语言显示是否正确

---

**字体规范版本**: 1.0  
**最后更新**: 2025年7月17日  
**维护团队**: AIGC Service Hub 设计团队
