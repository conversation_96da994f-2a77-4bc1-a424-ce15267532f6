# <type>(<scope>): <subject>
#
# <body>
#
# <footer>

# Type should be one of the following:
# * feat: A new feature
# * fix: A bug fix
# * docs: Documentation only changes
# * style: Changes that do not affect the meaning of the code
# * refactor: A code change that neither fixes a bug nor adds a feature
# * perf: A code change that improves performance
# * test: Adding missing tests or correcting existing tests
# * build: Changes that affect the build system or external dependencies
# * ci: Changes to our CI configuration files and scripts
# * chore: Other changes that don't modify src or test files
# * revert: Reverts a previous commit

# Scope should be one of the following (optional):
# * frontend: Frontend related changes
# * backend: Backend related changes
# * api: API related changes
# * ui: UI component changes
# * auth: Authentication related changes
# * db: Database related changes
# * config: Configuration changes
# * deps: Dependency updates
# * docker: Docker related changes
# * infra: Infrastructure changes

# Subject line rules:
# * Use imperative mood ("Add feature" not "Added feature")
# * Don't capitalize first letter
# * No period at the end
# * Limit to 50 characters

# Body rules:
# * Wrap at 72 characters
# * Use imperative mood
# * Include motivation for the change
# * Contrast with previous behavior

# Footer rules:
# * Reference issues and pull requests
# * Include breaking changes
# * Format: "BREAKING CHANGE: <description>"
# * Format: "Closes #123, #456"
# * Format: "Refs #123"

# Examples:
# feat(auth): add OAuth2 login support
# fix(api): resolve user data validation error
# docs: update API documentation for v2.0
# style(frontend): format code with prettier
# refactor(backend): extract user service logic
# perf(db): optimize user query performance
# test(api): add integration tests for auth endpoints
# build(deps): update react to version 18.2.0
# ci: add automated testing workflow
# chore(config): update eslint configuration
