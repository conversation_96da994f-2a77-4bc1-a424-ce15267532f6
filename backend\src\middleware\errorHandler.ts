import { Request, Response, NextFunction } from 'express';
import { AppError } from '@/types/index';
import { config } from '@/config/index';

// 错误处理中间件
export const errorHandler = (
  error: Error | AppError,
  req: Request,
  res: Response,
  _next: NextFunction
): void => {
  let statusCode = 500;
  let message = 'Internal Server Error';
  let code = 'INTERNAL_ERROR';
  let details: any[] = [];

  // 处理自定义应用错误
  if (error instanceof AppError) {
    statusCode = error.statusCode;
    message = error.message;
    code = error.code || 'APP_ERROR';
  }
  // 处理数据库错误
  else if (
    error.name === 'QueryFailedError' ||
    error.message.includes('duplicate key')
  ) {
    statusCode = 400;
    message = 'Database operation failed';
    code = 'DATABASE_ERROR';
  }
  // 处理JWT错误
  else if (error.name === 'JsonWebTokenError') {
    statusCode = 401;
    message = 'Invalid token';
    code = 'INVALID_TOKEN';
  } else if (error.name === 'TokenExpiredError') {
    statusCode = 401;
    message = 'Token expired';
    code = 'TOKEN_EXPIRED';
  }
  // 处理验证错误
  else if (error.name === 'ValidationError') {
    statusCode = 400;
    message = 'Validation failed';
    code = 'VALIDATION_ERROR';
    details = Object.values(error as any).map((err: any) => ({
      field: err.path,
      message: err.message,
    }));
  }
  // 处理语法错误
  else if (error instanceof SyntaxError && 'body' in error) {
    statusCode = 400;
    message = 'Invalid JSON format';
    code = 'INVALID_JSON';
  }
  // 处理其他已知错误
  else if (error.message) {
    message = error.message;
  }

  // 记录错误日志
  console.error('Error occurred:', {
    timestamp: new Date().toISOString(),
    method: req.method,
    url: req.url,
    statusCode,
    code,
    message,
    stack: config.nodeEnv === 'development' ? error.stack : undefined,
    body: req.body,
    params: req.params,
    query: req.query,
    headers: req.headers,
  });

  // 构建错误响应
  const errorResponse = {
    success: false,
    error: {
      code,
      message,
      ...(details.length > 0 && { details }),
      ...(config.nodeEnv === 'development' && { stack: error.stack }),
    },
    timestamp: new Date().toISOString(),
  };

  res.status(statusCode).json(errorResponse);
};

// 异步错误包装器
export const asyncHandler = (fn: Function) => {
  return (req: Request, res: Response, next: NextFunction) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
};

// 创建应用错误的辅助函数
export const createError = (
  message: string,
  statusCode: number,
  code?: string
): AppError => {
  return new AppError(message, statusCode, code);
};

// 常用错误创建函数
export const badRequest = (message: string, code?: string): AppError => {
  return createError(message, 400, code || 'BAD_REQUEST');
};

export const unauthorized = (
  message: string = 'Unauthorized',
  code?: string
): AppError => {
  return createError(message, 401, code || 'UNAUTHORIZED');
};

export const forbidden = (
  message: string = 'Forbidden',
  code?: string
): AppError => {
  return createError(message, 403, code || 'FORBIDDEN');
};

export const notFound = (
  message: string = 'Resource not found',
  code?: string
): AppError => {
  return createError(message, 404, code || 'NOT_FOUND');
};

export const conflict = (message: string, code?: string): AppError => {
  return createError(message, 409, code || 'CONFLICT');
};

export const unprocessableEntity = (
  message: string,
  code?: string
): AppError => {
  return createError(message, 422, code || 'UNPROCESSABLE_ENTITY');
};

export const internalServerError = (
  message: string = 'Internal Server Error',
  code?: string
): AppError => {
  return createError(message, 500, code || 'INTERNAL_ERROR');
};
