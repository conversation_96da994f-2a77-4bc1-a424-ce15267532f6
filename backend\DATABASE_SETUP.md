# AIGC Service Hub 数据库设置指南

## 📋 数据库设置概览

本指南将帮助您设置AIGC Service Hub项目的PostgreSQL数据库环境。

## 🛠 PostgreSQL 安装

### Windows 安装

1. **下载PostgreSQL**
   - 访问 [PostgreSQL官网](https://www.postgresql.org/download/windows/)
   - 下载PostgreSQL 14或更高版本的Windows安装程序

2. **安装PostgreSQL**

   ```bash
   # 运行下载的安装程序
   # 设置超级用户密码（建议使用: password）
   # 端口保持默认: 5432
   # 区域设置选择: Chinese (Simplified), China
   ```

3. **验证安装**
   ```bash
   # 打开命令提示符，测试连接
   psql -U postgres -h localhost
   # 输入设置的密码
   ```

### 使用Docker安装（推荐）

```bash
# 拉取PostgreSQL镜像
docker pull postgres:14

# 运行PostgreSQL容器
docker run --name aigc-postgres \
  -e POSTGRES_PASSWORD=password \
  -e POSTGRES_DB=aigc_service_hub_dev \
  -p 5432:5432 \
  -d postgres:14

# 验证容器运行
docker ps
```

## 🗄️ 数据库创建

### 方法一：使用psql命令行

```bash
# 连接到PostgreSQL
psql -U postgres -h localhost

# 创建数据库
CREATE DATABASE aigc_service_hub_dev;

# 创建用户（可选）
CREATE USER aigc_user WITH PASSWORD 'aigc_password';
GRANT ALL PRIVILEGES ON DATABASE aigc_service_hub_dev TO aigc_user;

# 退出psql
\q
```

### 方法二：使用pgAdmin

1. 打开pgAdmin（随PostgreSQL安装）
2. 连接到本地服务器
3. 右键"Databases" → "Create" → "Database"
4. 数据库名称：`aigc_service_hub_dev`
5. 点击"Save"

## ⚙️ 环境变量配置

更新 `backend/.env` 文件中的数据库连接信息：

```env
# 数据库配置
DATABASE_URL=postgresql://postgres:password@localhost:5432/aigc_service_hub_dev
DB_HOST=localhost
DB_PORT=5432
DB_NAME=aigc_service_hub_dev
DB_USER=postgres
DB_PASSWORD=password
DB_SSL=false
```

## 🚀 运行数据库迁移

### 1. 检查迁移状态

```bash
cd backend
npm run migrate status
```

### 2. 执行迁移

```bash
# 运行所有待执行的迁移
npm run migrate

# 或者明确指定up命令
npm run migrate up
```

### 3. 验证迁移结果

```bash
# 再次检查状态
npm run migrate status

# 应该看到所有迁移都已执行
```

## 🌱 创建测试数据

```bash
# 创建测试用户和数据
npm run seed

# 清理测试数据（如需要）
npm run seed clean
```

## 📊 数据库验证

### 1. 检查表结构

```sql
-- 连接到数据库
psql -U postgres -d aigc_service_hub_dev

-- 查看所有表
\dt

-- 查看特定表结构
\d users
\d resources
\d transactions

-- 查看索引
\di

-- 查看视图
\dv
```

### 2. 验证数据

```sql
-- 检查用户数据
SELECT id, email, user_type, is_verified FROM users;

-- 检查资源数据
SELECT id, title, category, status, price_usd FROM resources;

-- 检查系统配置
SELECT config_key, config_value, category FROM system_configs;
```

## 🔧 常见问题解决

### 问题1: 连接被拒绝

```bash
# 检查PostgreSQL服务状态
# Windows:
net start postgresql-x64-14

# 或使用服务管理器启动PostgreSQL服务
```

### 问题2: 密码认证失败

```bash
# 重置postgres用户密码
# 1. 找到pg_hba.conf文件
# 2. 临时修改认证方式为trust
# 3. 重启PostgreSQL服务
# 4. 使用psql重置密码
# 5. 恢复认证方式为md5
```

### 问题3: 数据库不存在

```sql
-- 手动创建数据库
CREATE DATABASE aigc_service_hub_dev
WITH ENCODING 'UTF8'
LC_COLLATE='Chinese (Simplified)_China.936'
LC_CTYPE='Chinese (Simplified)_China.936';
```

### 问题4: 权限不足

```sql
-- 授予用户权限
GRANT ALL PRIVILEGES ON DATABASE aigc_service_hub_dev TO postgres;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO postgres;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO postgres;
```

## 📈 性能优化建议

### 1. 配置优化

编辑 `postgresql.conf` 文件：

```conf
# 内存配置
shared_buffers = 256MB
effective_cache_size = 1GB
work_mem = 4MB

# 连接配置
max_connections = 100

# 日志配置
log_statement = 'all'
log_duration = on
```

### 2. 索引监控

```sql
-- 查看索引使用情况
SELECT schemaname, tablename, indexname, idx_scan, idx_tup_read, idx_tup_fetch
FROM pg_stat_user_indexes
ORDER BY idx_scan DESC;

-- 查看未使用的索引
SELECT schemaname, tablename, indexname, idx_scan
FROM pg_stat_user_indexes
WHERE idx_scan = 0;
```

## 🔄 备份和恢复

### 备份数据库

```bash
# 完整备份
pg_dump -U postgres -h localhost aigc_service_hub_dev > backup.sql

# 仅数据备份
pg_dump -U postgres -h localhost --data-only aigc_service_hub_dev > data_backup.sql

# 仅结构备份
pg_dump -U postgres -h localhost --schema-only aigc_service_hub_dev > schema_backup.sql
```

### 恢复数据库

```bash
# 从备份恢复
psql -U postgres -h localhost aigc_service_hub_dev < backup.sql

# 恢复到新数据库
createdb -U postgres new_database
psql -U postgres -h localhost new_database < backup.sql
```

## 🐳 Docker Compose 配置

创建 `docker-compose.yml` 用于开发环境：

```yaml
version: '3.8'
services:
  postgres:
    image: postgres:14
    environment:
      POSTGRES_DB: aigc_service_hub_dev
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
    ports:
      - '5432:5432'
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backend/src/database/migrations:/docker-entrypoint-initdb.d
    networks:
      - aigc-network

volumes:
  postgres_data:

networks:
  aigc-network:
    driver: bridge
```

启动命令：

```bash
docker-compose up -d postgres
```

## ✅ 设置完成检查清单

- [ ] PostgreSQL 14+ 已安装并运行
- [ ] 数据库 `aigc_service_hub_dev` 已创建
- [ ] 环境变量已正确配置
- [ ] 数据库迁移已成功执行
- [ ] 测试数据已创建
- [ ] 数据库连接测试通过
- [ ] 所有表和索引已创建
- [ ] 系统配置已加载

## 📞 获取帮助

如果遇到问题，请：

1. 检查PostgreSQL服务是否运行
2. 验证环境变量配置
3. 查看数据库日志文件
4. 参考PostgreSQL官方文档
5. 联系开发团队获取支持

---

**文档版本**: 1.0  
**最后更新**: 2025年7月17日  
**维护团队**: AIGC Service Hub 开发团队
