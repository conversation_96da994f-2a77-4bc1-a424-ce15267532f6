# S3 Module for AIGC Service Hub

# Random suffix for bucket names to ensure uniqueness
resource "random_string" "bucket_suffix" {
  length  = 8
  special = false
  upper   = false
}

# S3 Bucket for Files (AI models, resources, etc.)
resource "aws_s3_bucket" "files" {
  bucket = "${var.name_prefix}-files-${random_string.bucket_suffix.result}"

  tags = merge(var.tags, {
    Name        = "${var.name_prefix}-files-bucket"
    Purpose     = "AI Resources Storage"
    Environment = var.environment
  })
}

# S3 Bucket for Backups
resource "aws_s3_bucket" "backups" {
  bucket = "${var.name_prefix}-backups-${random_string.bucket_suffix.result}"

  tags = merge(var.tags, {
    Name        = "${var.name_prefix}-backups-bucket"
    Purpose     = "Database and Application Backups"
    Environment = var.environment
  })
}

# S3 Bucket for Logs
resource "aws_s3_bucket" "logs" {
  bucket = "${var.name_prefix}-logs-${random_string.bucket_suffix.result}"

  tags = merge(var.tags, {
    Name        = "${var.name_prefix}-logs-bucket"
    Purpose     = "Application and Access Logs"
    Environment = var.environment
  })
}

# S3 Bucket Versioning - Files
resource "aws_s3_bucket_versioning" "files" {
  bucket = aws_s3_bucket.files.id
  versioning_configuration {
    status = "Enabled"
  }
}

# S3 Bucket Versioning - Backups
resource "aws_s3_bucket_versioning" "backups" {
  bucket = aws_s3_bucket.backups.id
  versioning_configuration {
    status = "Enabled"
  }
}

# S3 Bucket Server Side Encryption - Files
resource "aws_s3_bucket_server_side_encryption_configuration" "files" {
  bucket = aws_s3_bucket.files.id

  rule {
    apply_server_side_encryption_by_default {
      sse_algorithm = "AES256"
    }
    bucket_key_enabled = true
  }
}

# S3 Bucket Server Side Encryption - Backups
resource "aws_s3_bucket_server_side_encryption_configuration" "backups" {
  bucket = aws_s3_bucket.backups.id

  rule {
    apply_server_side_encryption_by_default {
      sse_algorithm = "AES256"
    }
    bucket_key_enabled = true
  }
}

# S3 Bucket Server Side Encryption - Logs
resource "aws_s3_bucket_server_side_encryption_configuration" "logs" {
  bucket = aws_s3_bucket.logs.id

  rule {
    apply_server_side_encryption_by_default {
      sse_algorithm = "AES256"
    }
    bucket_key_enabled = true
  }
}

# S3 Bucket Public Access Block - Files
resource "aws_s3_bucket_public_access_block" "files" {
  bucket = aws_s3_bucket.files.id

  block_public_acls       = true
  block_public_policy     = true
  ignore_public_acls      = true
  restrict_public_buckets = true
}

# S3 Bucket Public Access Block - Backups
resource "aws_s3_bucket_public_access_block" "backups" {
  bucket = aws_s3_bucket.backups.id

  block_public_acls       = true
  block_public_policy     = true
  ignore_public_acls      = true
  restrict_public_buckets = true
}

# S3 Bucket Public Access Block - Logs
resource "aws_s3_bucket_public_access_block" "logs" {
  bucket = aws_s3_bucket.logs.id

  block_public_acls       = true
  block_public_policy     = true
  ignore_public_acls      = true
  restrict_public_buckets = true
}

# S3 Bucket Lifecycle Configuration - Files
resource "aws_s3_bucket_lifecycle_configuration" "files" {
  bucket = aws_s3_bucket.files.id

  rule {
    id     = "files_lifecycle"
    status = "Enabled"

    # Transition to IA after 30 days
    transition {
      days          = 30
      storage_class = "STANDARD_IA"
    }

    # Transition to Glacier after 90 days
    transition {
      days          = 90
      storage_class = "GLACIER"
    }

    # Delete old versions after 365 days
    noncurrent_version_expiration {
      noncurrent_days = 365
    }
  }
}

# S3 Bucket Lifecycle Configuration - Backups
resource "aws_s3_bucket_lifecycle_configuration" "backups" {
  bucket = aws_s3_bucket.backups.id

  rule {
    id     = "backups_lifecycle"
    status = "Enabled"

    # Transition to IA after 7 days
    transition {
      days          = 7
      storage_class = "STANDARD_IA"
    }

    # Transition to Glacier after 30 days
    transition {
      days          = 30
      storage_class = "GLACIER"
    }

    # Delete backups after 2 years
    expiration {
      days = 730
    }
  }
}

# S3 Bucket Lifecycle Configuration - Logs
resource "aws_s3_bucket_lifecycle_configuration" "logs" {
  bucket = aws_s3_bucket.logs.id

  rule {
    id     = "logs_lifecycle"
    status = "Enabled"

    # Transition to IA after 30 days
    transition {
      days          = 30
      storage_class = "STANDARD_IA"
    }

    # Delete logs after 90 days
    expiration {
      days = 90
    }
  }
}

# S3 Bucket CORS Configuration - Files (for direct uploads)
resource "aws_s3_bucket_cors_configuration" "files" {
  bucket = aws_s3_bucket.files.id

  cors_rule {
    allowed_headers = ["*"]
    allowed_methods = ["GET", "PUT", "POST", "DELETE", "HEAD"]
    allowed_origins = ["*"]  # Restrict this in production
    expose_headers  = ["ETag"]
    max_age_seconds = 3000
  }
}

# S3 Bucket Notification for file uploads (optional)
resource "aws_s3_bucket_notification" "files" {
  bucket = aws_s3_bucket.files.id

  # Add SNS/SQS/Lambda notifications here if needed
  # For example, to trigger processing when files are uploaded
}

# CloudWatch Metric Filters for S3 access logs (if needed)
resource "aws_cloudwatch_log_metric_filter" "s3_errors" {
  count = var.enable_monitoring ? 1 : 0

  name           = "${var.name_prefix}-s3-errors"
  log_group_name = "/aws/s3/${aws_s3_bucket.files.bucket}"
  pattern        = "[timestamp, request_id, client_ip, requester, request_id, operation, bucket, key, request_uri, http_status=\"4*\" || http_status=\"5*\", ...]"

  metric_transformation {
    name      = "S3Errors"
    namespace = "AIGC/S3"
    value     = "1"
  }
}
