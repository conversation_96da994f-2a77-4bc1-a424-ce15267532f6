#!/bin/bash

# AIGC Service Hub 代码质量检查脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查Node.js版本
check_node_version() {
    log_info "检查Node.js版本..."
    
    if ! command -v node &> /dev/null; then
        log_error "Node.js未安装"
        return 1
    fi
    
    local node_version=$(node --version | cut -d'v' -f2)
    local required_version="18.0.0"
    
    if [ "$(printf '%s\n' "$required_version" "$node_version" | sort -V | head -n1)" = "$required_version" ]; then
        log_success "Node.js版本: $node_version (满足要求 >= $required_version)"
    else
        log_error "Node.js版本过低: $node_version (需要 >= $required_version)"
        return 1
    fi
}

# 检查npm版本
check_npm_version() {
    log_info "检查npm版本..."
    
    if ! command -v npm &> /dev/null; then
        log_error "npm未安装"
        return 1
    fi
    
    local npm_version=$(npm --version)
    local required_version="8.0.0"
    
    if [ "$(printf '%s\n' "$required_version" "$npm_version" | sort -V | head -n1)" = "$required_version" ]; then
        log_success "npm版本: $npm_version (满足要求 >= $required_version)"
    else
        log_error "npm版本过低: $npm_version (需要 >= $required_version)"
        return 1
    fi
}

# 检查依赖安装
check_dependencies() {
    log_info "检查依赖安装..."
    
    # 检查根目录依赖
    if [ ! -d "node_modules" ]; then
        log_warning "根目录依赖未安装，正在安装..."
        npm install
    fi
    
    # 检查前端依赖
    if [ ! -d "frontend/node_modules" ]; then
        log_warning "前端依赖未安装，正在安装..."
        cd frontend && npm install && cd ..
    fi
    
    # 检查后端依赖
    if [ ! -d "backend/node_modules" ]; then
        log_warning "后端依赖未安装，正在安装..."
        cd backend && npm install && cd ..
    fi
    
    log_success "依赖检查完成"
}

# 代码格式检查
check_formatting() {
    log_info "检查代码格式..."
    
    if npm run format:check; then
        log_success "代码格式正确"
        return 0
    else
        log_warning "代码格式不正确，正在自动修复..."
        npm run format
        log_success "代码格式已修复"
        return 0
    fi
}

# ESLint检查
check_linting() {
    log_info "运行ESLint检查..."
    
    local lint_failed=0
    
    # 检查前端
    log_info "检查前端代码..."
    if cd frontend && npm run lint; then
        log_success "前端ESLint检查通过"
    else
        log_error "前端ESLint检查失败"
        lint_failed=1
    fi
    cd ..
    
    # 检查后端
    log_info "检查后端代码..."
    if cd backend && npm run lint; then
        log_success "后端ESLint检查通过"
    else
        log_error "后端ESLint检查失败"
        lint_failed=1
    fi
    cd ..
    
    return $lint_failed
}

# TypeScript类型检查
check_types() {
    log_info "运行TypeScript类型检查..."
    
    local type_failed=0
    
    # 检查前端
    log_info "检查前端类型..."
    if cd frontend && npm run type-check; then
        log_success "前端类型检查通过"
    else
        log_error "前端类型检查失败"
        type_failed=1
    fi
    cd ..
    
    # 检查后端
    log_info "检查后端类型..."
    if cd backend && npm run type-check; then
        log_success "后端类型检查通过"
    else
        log_error "后端类型检查失败"
        type_failed=1
    fi
    cd ..
    
    return $type_failed
}

# 运行测试
run_tests() {
    log_info "运行测试..."
    
    local test_failed=0
    
    # 运行前端测试
    log_info "运行前端测试..."
    if cd frontend && npm run test; then
        log_success "前端测试通过"
    else
        log_error "前端测试失败"
        test_failed=1
    fi
    cd ..
    
    # 运行后端测试
    log_info "运行后端测试..."
    if cd backend && npm run test; then
        log_success "后端测试通过"
    else
        log_error "后端测试失败"
        test_failed=1
    fi
    cd ..
    
    return $test_failed
}

# 检查安全漏洞
check_security() {
    log_info "检查安全漏洞..."
    
    local security_failed=0
    
    # 检查前端安全漏洞
    log_info "检查前端安全漏洞..."
    if cd frontend && npm audit --audit-level=high; then
        log_success "前端安全检查通过"
    else
        log_warning "前端发现安全漏洞，建议运行 'npm audit fix'"
        security_failed=1
    fi
    cd ..
    
    # 检查后端安全漏洞
    log_info "检查后端安全漏洞..."
    if cd backend && npm audit --audit-level=high; then
        log_success "后端安全检查通过"
    else
        log_warning "后端发现安全漏洞，建议运行 'npm audit fix'"
        security_failed=1
    fi
    cd ..
    
    return $security_failed
}

# 生成报告
generate_report() {
    local checks_passed=$1
    local total_checks=$2
    
    log_info "生成代码质量报告..."
    
    local report_file="code-quality-report.txt"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    
    cat > "$report_file" << EOF
AIGC Service Hub 代码质量检查报告
生成时间: $timestamp

检查结果: $checks_passed/$total_checks 通过

详细检查项目:
✓ Node.js版本检查
✓ npm版本检查
✓ 依赖安装检查
✓ 代码格式检查
✓ ESLint代码质量检查
✓ TypeScript类型检查
✓ 单元测试运行
✓ 安全漏洞检查

建议:
- 定期运行代码质量检查
- 提交前确保所有检查通过
- 及时修复发现的问题
- 保持依赖更新

EOF
    
    log_success "代码质量报告已生成: $report_file"
}

# 主函数
main() {
    log_info "开始代码质量检查..."
    echo
    
    local checks_passed=0
    local total_checks=8
    
    # 运行所有检查
    if check_node_version; then ((checks_passed++)); fi
    echo
    
    if check_npm_version; then ((checks_passed++)); fi
    echo
    
    if check_dependencies; then ((checks_passed++)); fi
    echo
    
    if check_formatting; then ((checks_passed++)); fi
    echo
    
    if check_linting; then ((checks_passed++)); fi
    echo
    
    if check_types; then ((checks_passed++)); fi
    echo
    
    if run_tests; then ((checks_passed++)); fi
    echo
    
    if check_security; then ((checks_passed++)); fi
    echo
    
    # 生成报告
    generate_report $checks_passed $total_checks
    
    # 输出结果
    log_info "代码质量检查完成！"
    echo "通过检查: $checks_passed/$total_checks"
    
    if [ "$checks_passed" -eq "$total_checks" ]; then
        log_success "所有检查通过！代码质量良好。"
        return 0
    else
        log_warning "部分检查未通过，请修复问题后重新检查。"
        return 1
    fi
}

# 运行检查
main "$@"
