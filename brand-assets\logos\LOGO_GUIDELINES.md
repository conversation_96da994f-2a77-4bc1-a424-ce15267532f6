# AIGC Service Hub Logo设计规范

## 🎯 Logo概念

AIGC Service Hub的Logo设计体现了AI技术的创新性和平台的专业性，通过现代化的视觉语言传达品牌价值。

### 设计理念

- **连接**: 象征AI创作者之间的连接和协作
- **创新**: 体现前沿的AI技术和创新精神
- **专业**: 传达平台的专业性和可信度
- **开放**: 表达开放共享的平台理念

## 🎨 Logo构成元素

### 主要组成

1. **图标标识**: 抽象的神经网络图形
2. **文字标识**: "AIGC Service Hub"
3. **组合形式**: 图标+文字的多种组合方式

### 图标设计说明

```
图标元素：
- 中心节点：代表平台核心
- 连接线：象征网络连接和数据流
- 渐变色：体现AI的智能和创新
- 圆角设计：传达友好和现代感
```

## 📐 Logo规格和尺寸

### 标准尺寸

```
最小使用尺寸：
- 数字媒体：24px 高度
- 印刷媒体：15mm 高度
- 大型应用：无最大限制

推荐尺寸：
- 网站头部：40px 高度
- 移动应用：32px 高度
- 名片：20mm 高度
- 海报：50mm 高度
```

### 比例关系

```
图标与文字比例：
- 图标高度 = 文字高度
- 图标与文字间距 = 图标宽度的 0.5倍
- 整体宽高比约为 4:1
```

## 🔄 Logo变体

### 1. 完整版Logo

```
使用场景：
- 网站首页
- 官方文档
- 正式场合
- 品牌宣传

文件命名：
- logo-full-color.svg
- logo-full-color.png
- logo-full-white.svg
- logo-full-black.svg
```

### 2. 简化版Logo

```
使用场景：
- 移动应用
- 社交媒体
- 小尺寸应用
- 简洁设计需求

文件命名：
- logo-simple-color.svg
- logo-simple-white.svg
- logo-simple-black.svg
```

### 3. 图标版Logo

```
使用场景：
- 应用图标
- 收藏夹图标
- 社交头像
- 极小尺寸应用

文件命名：
- icon-color.svg
- icon-white.svg
- icon-black.svg
```

### 4. 文字版Logo

```
使用场景：
- 纯文字环境
- 特殊版式需求
- 辅助应用

文件命名：
- wordmark-color.svg
- wordmark-black.svg
```

## 🎨 颜色规范

### 主色版本

```css
/* 主品牌色 */
.logo-primary {
  --logo-primary: #2563eb;
  --logo-secondary: #7c3aed;
  --logo-text: #1f2937;
}
```

### 单色版本

```css
/* 白色版本 - 深色背景使用 */
.logo-white {
  --logo-color: #ffffff;
}

/* 黑色版本 - 浅色背景使用 */
.logo-black {
  --logo-color: #000000;
}

/* 灰色版本 - 特殊场景使用 */
.logo-gray {
  --logo-color: #6b7280;
}
```

## 📏 安全区域

### 安全区域定义

```
安全区域 = Logo高度的 1/2

示例：
- Logo高度 40px → 安全区域 20px
- Logo高度 60px → 安全区域 30px

应用：
- Logo周围必须保持安全区域的空白
- 任何其他元素不得进入安全区域
- 确保Logo的视觉独立性
```

### 安全区域示意

```
┌─────────────────────────────────┐
│           安全区域               │
│   ┌─────────────────────────┐   │
│   │                         │   │
│   │        LOGO区域         │   │
│   │                         │   │
│   └─────────────────────────┘   │
│           安全区域               │
└─────────────────────────────────┘
```

## 🚫 使用禁忌

### 严格禁止的操作

#### 1. 比例变形

```
❌ 不得拉伸或压缩Logo
❌ 不得改变图标与文字的比例关系
❌ 不得单独缩放图标或文字部分
```

#### 2. 颜色修改

```
❌ 不得使用未定义的颜色
❌ 不得添加渐变效果（除原有设计）
❌ 不得降低颜色饱和度
❌ 不得使用彩虹色或霓虹色
```

#### 3. 效果添加

```
❌ 不得添加阴影效果
❌ 不得添加发光效果
❌ 不得添加3D效果
❌ 不得添加动画效果（静态版本）
```

#### 4. 背景问题

```
❌ 不得在低对比度背景上使用
❌ 不得在复杂图案背景上使用
❌ 不得在干扰性背景上使用
```

#### 5. 组合问题

```
❌ 不得与其他Logo组合
❌ 不得与装饰元素重叠
❌ 不得被其他元素遮挡
❌ 不得作为图案重复使用
```

## ✅ 正确使用示例

### 网站应用

```html
<!-- 网站头部 -->
<header>
  <img src="logo-full-color.svg" alt="AIGC Service Hub" height="40" />
</header>

<!-- 页脚 -->
<footer>
  <img src="logo-simple-white.svg" alt="AIGC Service Hub" height="32" />
</footer>
```

### 移动应用

```html
<!-- 启动屏幕 -->
<div class="splash-screen">
  <img src="logo-full-color.svg" alt="AIGC Service Hub" height="60" />
</div>

<!-- 导航栏 -->
<nav>
  <img src="icon-color.svg" alt="AIGC Service Hub" height="24" />
</nav>
```

## 📱 不同平台适配

### 社交媒体

```
Facebook封面：1200x630px
- 使用完整版Logo，居中放置
- 确保在移动端也清晰可见

Twitter头像：400x400px
- 使用图标版Logo
- 保持足够的边距

LinkedIn：1584x396px
- 使用简化版Logo
- 配合品牌色背景
```

### 应用商店

```
iOS App Icon：1024x1024px
- 使用图标版Logo
- 遵循iOS设计规范
- 避免使用文字

Google Play：512x512px
- 使用图标版Logo
- 确保在小尺寸下清晰
- 考虑圆角适配
```

## 📄 文件格式规范

### 矢量格式

```
SVG格式：
- 用途：网页、应用界面
- 优点：无损缩放、文件小
- 命名：logo-[variant]-[color].svg

AI/EPS格式：
- 用途：印刷、专业设计
- 优点：专业软件兼容
- 命名：logo-[variant]-[color].ai
```

### 位图格式

```
PNG格式：
- 用途：透明背景需求
- 规格：24px, 32px, 48px, 64px, 128px, 256px, 512px
- 命名：logo-[variant]-[color]-[size].png

JPG格式：
- 用途：不需要透明背景
- 规格：高分辨率版本
- 命名：logo-[variant]-[color]-[size].jpg
```

## 🔍 质量检查清单

### 设计质量

- [ ] Logo在最小尺寸下是否清晰可辨
- [ ] 颜色是否符合品牌规范
- [ ] 比例是否正确
- [ ] 是否有足够的安全区域

### 技术质量

- [ ] SVG文件是否优化
- [ ] PNG文件是否有透明背景
- [ ] 文件大小是否合理
- [ ] 是否提供了所有必要的尺寸

### 应用质量

- [ ] 在不同背景上是否清晰
- [ ] 在不同设备上是否正常显示
- [ ] 是否符合平台规范
- [ ] 是否易于识别和记忆

---

**Logo规范版本**: 1.0  
**最后更新**: 2025年7月17日  
**维护团队**: AIGC Service Hub 设计团队
