-- 创建资源表
-- 支持AI模型、LoRA、工作流、提示词、工具等资源类型

CREATE TABLE resources (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    creator_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    
    -- 基本信息
    title VARCHAR(255) NOT NULL,
    description TEXT,
    category VARCHAR(50) NOT NULL CHECK (category IN (
        'fine_tuned_models', 
        'lora', 
        'workflows', 
        'prompts', 
        'tools'
    )),
    tags TEXT[] DEFAULT '{}',
    
    -- 定价信息
    price_usd DECIMAL(10,2) NOT NULL CHECK (price_usd >= 0),
    price_points INTEGER NOT NULL CHECK (price_points >= 0),
    
    -- 文件信息
    file_url VARCHAR(500),
    file_size BIGINT CHECK (file_size > 0),
    file_name VARCHAR(255),
    file_type VARCHAR(100),
    cover_image_url VARCHAR(500),
    
    -- 统计信息
    download_count INTEGER DEFAULT 0,
    sales_count INTEGER DEFAULT 0,
    view_count INTEGER DEFAULT 0,
    rating_average DECIMAL(3,2) DEFAULT 0.00 CHECK (rating_average >= 0 AND rating_average <= 5),
    rating_count INTEGER DEFAULT 0,
    
    -- 状态管理
    status VARCHAR(20) DEFAULT 'pending_review' CHECK (status IN (
        'active', 
        'inactive', 
        'pending_review', 
        'rejected',
        'suspended'
    )),
    rejection_reason TEXT,
    
    -- 时间戳
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    published_at TIMESTAMP,
    
    -- 约束
    CONSTRAINT valid_pricing CHECK (price_usd > 0 OR price_points > 0),
    CONSTRAINT non_negative_stats CHECK (
        download_count >= 0 AND 
        sales_count >= 0 AND 
        view_count >= 0 AND
        rating_count >= 0
    )
);

-- 创建索引
CREATE INDEX idx_resources_creator_id ON resources(creator_id);
CREATE INDEX idx_resources_category ON resources(category);
CREATE INDEX idx_resources_status ON resources(status);
CREATE INDEX idx_resources_created_at ON resources(created_at);
CREATE INDEX idx_resources_published_at ON resources(published_at);
CREATE INDEX idx_resources_price_usd ON resources(price_usd);
CREATE INDEX idx_resources_price_points ON resources(price_points);
CREATE INDEX idx_resources_sales_count ON resources(sales_count);
CREATE INDEX idx_resources_rating_average ON resources(rating_average);

-- 全文搜索索引
CREATE INDEX idx_resources_title_search ON resources USING gin(to_tsvector('english', title));
CREATE INDEX idx_resources_description_search ON resources USING gin(to_tsvector('english', description));
CREATE INDEX idx_resources_tags_search ON resources USING gin(tags);

-- 创建更新时间戳触发器
CREATE TRIGGER update_resources_updated_at 
    BEFORE UPDATE ON resources 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- 创建发布时间自动设置触发器
CREATE OR REPLACE FUNCTION set_published_at()
RETURNS TRIGGER AS $$
BEGIN
    -- 当状态从非active变为active时，设置发布时间
    IF OLD.status != 'active' AND NEW.status = 'active' AND NEW.published_at IS NULL THEN
        NEW.published_at = CURRENT_TIMESTAMP;
    END IF;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER set_resources_published_at 
    BEFORE UPDATE ON resources 
    FOR EACH ROW 
    EXECUTE FUNCTION set_published_at();

-- 添加注释
COMMENT ON TABLE resources IS 'AI资源表，支持模型、LoRA、工作流等';
COMMENT ON COLUMN resources.category IS '资源分类：fine_tuned_models, lora, workflows, prompts, tools';
COMMENT ON COLUMN resources.tags IS '资源标签数组';
COMMENT ON COLUMN resources.price_usd IS '美元价格';
COMMENT ON COLUMN resources.price_points IS '积分价格';
COMMENT ON COLUMN resources.file_size IS '文件大小（字节）';
COMMENT ON COLUMN resources.status IS '资源状态：active, inactive, pending_review, rejected, suspended';
