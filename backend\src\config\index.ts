import dotenv from 'dotenv';
import type { AppConfig } from '@/types/index';

// 加载环境变量
dotenv.config();

// 验证必需的环境变量
const requiredEnvVars = ['NODE_ENV', 'PORT', 'JWT_SECRET', 'DATABASE_URL'];

for (const envVar of requiredEnvVars) {
  if (!process.env[envVar]) {
    throw new Error(`Missing required environment variable: ${envVar}`);
  }
}

// 应用配置
export const config: AppConfig = {
  // 基础配置
  port: parseInt(process.env.PORT || '3000', 10),
  nodeEnv: process.env.NODE_ENV || 'development',

  // JWT配置
  jwtSecret: process.env.JWT_SECRET!,
  jwtExpiresIn: process.env.JWT_EXPIRES_IN || '7d',

  // 数据库配置
  databaseUrl: process.env.DATABASE_URL!,

  // CORS配置
  corsOrigin: process.env.CORS_ORIGIN || 'http://localhost:5173',

  // 文件上传配置
  maxFileSize: parseInt(process.env.MAX_FILE_SIZE || '32212254720', 10), // 30GB
  chunkSize: parseInt(process.env.CHUNK_SIZE || '5242880', 10), // 5MB
  allowedFileTypes: (
    process.env.ALLOWED_FILE_TYPES ||
    '.zip,.rar,.7z,.tar,.gz,.json,.txt,.md,.py,.js,.ts'
  ).split(','),
};

// 数据库配置
export const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT || '5432', 10),
  database: process.env.DB_NAME || 'aigc_service_hub',
  user: process.env.DB_USER || 'postgres',
  password: process.env.DB_PASSWORD || 'password',
  ssl: process.env.DB_SSL === 'true',
  max: 20, // 连接池最大连接数
  idleTimeoutMillis: 30000,
  connectionTimeoutMillis: 2000,
};

// JWT配置
export const jwtConfig = {
  secret: process.env.JWT_SECRET!,
  expiresIn: process.env.JWT_EXPIRES_IN || '7d',
  refreshSecret: process.env.JWT_REFRESH_SECRET || process.env.JWT_SECRET!,
  refreshExpiresIn: process.env.JWT_REFRESH_EXPIRES_IN || '30d',
};

// PayPal配置
export const paypalConfig = {
  clientId: process.env.PAYPAL_CLIENT_ID || '',
  clientSecret: process.env.PAYPAL_CLIENT_SECRET || '',
  mode: process.env.PAYPAL_MODE || 'sandbox',
  webhookId: process.env.PAYPAL_WEBHOOK_ID || '',
  baseUrl:
    process.env.PAYPAL_MODE === 'live'
      ? 'https://api.paypal.com'
      : 'https://api.sandbox.paypal.com',
};

// AWS配置
export const awsConfig = {
  accessKeyId: process.env.AWS_ACCESS_KEY_ID || '',
  secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY || '',
  region: process.env.AWS_REGION || 'us-west-2',
  s3Bucket: process.env.AWS_S3_BUCKET || 'aigc-service-hub-files',
  cloudfrontDomain: process.env.AWS_CLOUDFRONT_DOMAIN || '',
};

// Redis配置
export const redisConfig = {
  url: process.env.REDIS_URL || 'redis://localhost:6379',
  host: process.env.REDIS_HOST || 'localhost',
  port: parseInt(process.env.REDIS_PORT || '6379', 10),
  password: process.env.REDIS_PASSWORD || undefined,
  db: 0,
  retryDelayOnFailover: 100,
  enableReadyCheck: false,
  maxRetriesPerRequest: null,
};

// 邮件配置
export const emailConfig = {
  host: process.env.SMTP_HOST || 'smtp.gmail.com',
  port: parseInt(process.env.SMTP_PORT || '587', 10),
  secure: process.env.SMTP_SECURE === 'true',
  auth: {
    user: process.env.SMTP_USER || '',
    pass: process.env.SMTP_PASS || '',
  },
  from: {
    email: process.env.FROM_EMAIL || '<EMAIL>',
    name: process.env.FROM_NAME || 'AIGC Service Hub',
  },
};

// 安全配置
export const securityConfig = {
  bcryptRounds: parseInt(process.env.BCRYPT_ROUNDS || '12', 10),
  rateLimitWindowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS || '900000', 10), // 15分钟
  rateLimitMaxRequests: parseInt(
    process.env.RATE_LIMIT_MAX_REQUESTS || '100',
    10
  ),
  uploadTimeout: parseInt(process.env.UPLOAD_TIMEOUT || '300000', 10), // 5分钟
};

// 业务配置
export const businessConfig = {
  // 积分系统
  pointsToUsdRate: parseFloat(process.env.POINTS_TO_USD_RATE || '0.01'),
  dailyCheckinPoints: parseInt(process.env.DAILY_CHECKIN_POINTS || '10', 10),
  firstUploadPoints: parseInt(process.env.FIRST_UPLOAD_POINTS || '100', 10),
  salesMilestonePoints: parseInt(
    process.env.SALES_MILESTONE_POINTS || '50',
    10
  ),

  // 分佣系统
  individualCreatorInitialRate: parseFloat(
    process.env.INDIVIDUAL_CREATOR_INITIAL_RATE || '0.95'
  ),
  individualCreatorFinalRate: parseFloat(
    process.env.INDIVIDUAL_CREATOR_FINAL_RATE || '0.50'
  ),
  enterpriseCreatorInitialRate: parseFloat(
    process.env.ENTERPRISE_CREATOR_INITIAL_RATE || '0.92'
  ),
  enterpriseCreatorFinalRate: parseFloat(
    process.env.ENTERPRISE_CREATOR_FINAL_RATE || '0.56'
  ),
  commissionDecayFactor: parseFloat(
    process.env.COMMISSION_DECAY_FACTOR || '0.05'
  ),

  // 提现系统
  withdrawalMinAmount: parseFloat(process.env.WITHDRAWAL_MIN_AMOUNT || '10.00'),
  withdrawalFeeRate: parseFloat(process.env.WITHDRAWAL_FEE_RATE || '0.02'),
  withdrawalFreezeDays: parseInt(process.env.WITHDRAWAL_FREEZE_DAYS || '7', 10),
};

// 日志配置
export const logConfig = {
  level: process.env.LOG_LEVEL || 'info',
  file: process.env.LOG_FILE || 'logs/app.log',
  maxSize: process.env.LOG_MAX_SIZE || '10m',
  maxFiles: parseInt(process.env.LOG_MAX_FILES || '5', 10),
};

// 功能开关
export const featureFlags = {
  enableRegistration: process.env.ENABLE_REGISTRATION !== 'false',
  enableEmailVerification: process.env.ENABLE_EMAIL_VERIFICATION === 'true',
  enableRateLimiting: process.env.ENABLE_RATE_LIMITING !== 'false',
  enableRequestLogging: process.env.ENABLE_REQUEST_LOGGING !== 'false',
  enableCors: process.env.ENABLE_CORS !== 'false',
  enableMetrics: process.env.ENABLE_METRICS === 'true',
};

// 监控配置
export const monitoringConfig = {
  metricsPort: parseInt(process.env.METRICS_PORT || '9090', 10),
  healthCheckInterval: parseInt(
    process.env.HEALTH_CHECK_INTERVAL || '30000',
    10
  ),
  sentryDsn: process.env.SENTRY_DSN || '',
};

// 导出所有配置
export default {
  config,
  dbConfig,
  jwtConfig,
  paypalConfig,
  awsConfig,
  redisConfig,
  emailConfig,
  securityConfig,
  businessConfig,
  logConfig,
  featureFlags,
  monitoringConfig,
};
