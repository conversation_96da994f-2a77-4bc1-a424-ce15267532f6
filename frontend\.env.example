# AIGC Service Hub Frontend Environment Variables

# API配置
VITE_API_BASE_URL=http://localhost:3000/api
VITE_APP_NAME=AIGC Service Hub
VITE_APP_VERSION=1.0.0

# PayPal配置
VITE_PAYPAL_CLIENT_ID=your_paypal_client_id_here
VITE_PAYPAL_MODE=sandbox

# AWS配置
VITE_AWS_REGION=us-west-2
VITE_AWS_S3_BUCKET=aigc-service-hub-files
VITE_AWS_CLOUDFRONT_DOMAIN=your_cloudfront_domain_here

# 文件上传配置
VITE_MAX_FILE_SIZE=32212254720
VITE_CHUNK_SIZE=5242880
VITE_ALLOWED_FILE_TYPES=.zip,.rar,.7z,.tar,.gz,.json,.txt,.md,.py,.js,.ts

# 应用配置
VITE_DEFAULT_LANGUAGE=zh-CN
VITE_ENABLE_ANALYTICS=false
VITE_ENABLE_DEBUG=true

# 第三方服务配置
VITE_GOOGLE_ANALYTICS_ID=your_ga_id_here
VITE_SENTRY_DSN=your_sentry_dsn_here

# 功能开关
VITE_ENABLE_DARK_MODE=true
VITE_ENABLE_PWA=false
VITE_ENABLE_OFFLINE_MODE=false
