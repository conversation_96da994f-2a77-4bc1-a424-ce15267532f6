<!doctype html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>AIGC Service Hub 品牌展示</title>
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap"
      rel="stylesheet"
    />
    <link rel="stylesheet" href="../colors/css/brand-colors.css" />
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family:
          'Inter',
          -apple-system,
          BlinkMacSystemFont,
          sans-serif;
        line-height: 1.6;
        color: var(--color-text-primary);
        background-color: var(--color-bg-primary);
      }

      .container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 0 20px;
      }

      .section {
        padding: 60px 0;
      }

      .section-title {
        font-size: 2.5rem;
        font-weight: 700;
        text-align: center;
        margin-bottom: 3rem;
        color: var(--color-text-primary);
      }

      .hero {
        background: var(--gradient-brand);
        color: white;
        text-align: center;
        padding: 100px 0;
      }

      .hero h1 {
        font-size: 3.5rem;
        font-weight: 700;
        margin-bottom: 1rem;
      }

      .hero p {
        font-size: 1.25rem;
        margin-bottom: 2rem;
        opacity: 0.9;
      }

      .logo-showcase {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 2rem;
        margin-top: 2rem;
      }

      .logo-item {
        background: var(--color-bg-secondary);
        padding: 2rem;
        border-radius: 12px;
        text-align: center;
        border: 1px solid var(--color-border-primary);
      }

      .logo-item h3 {
        margin-bottom: 1rem;
        color: var(--color-text-secondary);
      }

      .logo-item img {
        max-width: 200px;
        height: auto;
      }

      .color-palette {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 2rem;
        margin-top: 2rem;
      }

      .color-group {
        background: var(--color-bg-secondary);
        padding: 1.5rem;
        border-radius: 12px;
        border: 1px solid var(--color-border-primary);
      }

      .color-group h3 {
        margin-bottom: 1rem;
        color: var(--color-text-secondary);
      }

      .color-swatches {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 0.5rem;
      }

      .color-swatch {
        aspect-ratio: 1;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 0.75rem;
        font-weight: 500;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
      }

      .typography-showcase {
        display: grid;
        gap: 2rem;
        margin-top: 2rem;
      }

      .typography-item {
        background: var(--color-bg-secondary);
        padding: 2rem;
        border-radius: 12px;
        border: 1px solid var(--color-border-primary);
      }

      .heading-1 {
        font-size: 2.25rem;
        font-weight: 700;
      }
      .heading-2 {
        font-size: 1.875rem;
        font-weight: 600;
      }
      .heading-3 {
        font-size: 1.5rem;
        font-weight: 600;
      }
      .heading-4 {
        font-size: 1.25rem;
        font-weight: 500;
      }
      .body-large {
        font-size: 1.125rem;
        font-weight: 400;
      }
      .body-normal {
        font-size: 1rem;
        font-weight: 400;
      }
      .body-small {
        font-size: 0.875rem;
        font-weight: 400;
      }

      .button-showcase {
        display: flex;
        flex-wrap: wrap;
        gap: 1rem;
        margin-top: 2rem;
        justify-content: center;
      }

      .btn {
        padding: 12px 24px;
        border: none;
        border-radius: 8px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s;
        text-decoration: none;
        display: inline-block;
      }

      .btn-primary {
        background-color: var(--primary-600);
        color: white;
      }

      .btn-primary:hover {
        background-color: var(--primary-700);
      }

      .btn-secondary {
        background-color: var(--secondary-600);
        color: white;
      }

      .btn-secondary:hover {
        background-color: var(--secondary-700);
      }

      .btn-outline {
        background-color: transparent;
        color: var(--primary-600);
        border: 2px solid var(--primary-600);
      }

      .btn-outline:hover {
        background-color: var(--primary-600);
        color: white;
      }

      .icon-showcase {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
        gap: 1rem;
        margin-top: 2rem;
      }

      .icon-item {
        background: var(--color-bg-secondary);
        padding: 1rem;
        border-radius: 8px;
        text-align: center;
        border: 1px solid var(--color-border-primary);
      }

      .icon-placeholder {
        width: 24px;
        height: 24px;
        background: var(--primary-600);
        border-radius: 4px;
        margin: 0 auto 0.5rem;
      }

      .footer {
        background: var(--gray-900);
        color: white;
        text-align: center;
        padding: 2rem 0;
      }
    </style>
  </head>
  <body>
    <!-- Hero Section -->
    <section class="hero">
      <div class="container">
        <h1>AIGC Service Hub</h1>
        <p>专业的AI创作者服务平台品牌展示</p>
      </div>
    </section>

    <!-- Logo Showcase -->
    <section class="section">
      <div class="container">
        <h2 class="section-title">Logo 展示</h2>
        <div class="logo-showcase">
          <div class="logo-item">
            <h3>完整版 Logo</h3>
            <div style="background: white; padding: 20px; border-radius: 8px">
              <div
                style="
                  width: 200px;
                  height: 50px;
                  background: var(--gradient-brand);
                  border-radius: 4px;
                  margin: 0 auto;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  color: white;
                  font-weight: 600;
                "
              >
                AIGC Service Hub
              </div>
            </div>
          </div>
          <div class="logo-item">
            <h3>简化版 Logo</h3>
            <div style="background: white; padding: 20px; border-radius: 8px">
              <div
                style="
                  width: 120px;
                  height: 50px;
                  background: var(--gradient-brand);
                  border-radius: 4px;
                  margin: 0 auto;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  color: white;
                  font-weight: 600;
                "
              >
                ASH
              </div>
            </div>
          </div>
          <div class="logo-item">
            <h3>图标版 Logo</h3>
            <div style="background: white; padding: 20px; border-radius: 8px">
              <div
                style="
                  width: 50px;
                  height: 50px;
                  background: var(--gradient-brand);
                  border-radius: 8px;
                  margin: 0 auto;
                "
              ></div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Color Palette -->
    <section class="section" style="background: var(--color-bg-secondary)">
      <div class="container">
        <h2 class="section-title">色彩系统</h2>
        <div class="color-palette">
          <div class="color-group">
            <h3>主品牌色</h3>
            <div class="color-swatches">
              <div class="color-swatch" style="background: var(--primary-500)">
                500
              </div>
              <div class="color-swatch" style="background: var(--primary-600)">
                600
              </div>
              <div class="color-swatch" style="background: var(--primary-700)">
                700
              </div>
            </div>
          </div>
          <div class="color-group">
            <h3>辅助色</h3>
            <div class="color-swatches">
              <div
                class="color-swatch"
                style="background: var(--secondary-500)"
              >
                500
              </div>
              <div
                class="color-swatch"
                style="background: var(--secondary-600)"
              >
                600
              </div>
              <div
                class="color-swatch"
                style="background: var(--secondary-700)"
              >
                700
              </div>
            </div>
          </div>
          <div class="color-group">
            <h3>功能色彩</h3>
            <div class="color-swatches">
              <div class="color-swatch" style="background: var(--success-600)">
                成功
              </div>
              <div class="color-swatch" style="background: var(--warning-600)">
                警告
              </div>
              <div class="color-swatch" style="background: var(--error-600)">
                错误
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Typography -->
    <section class="section">
      <div class="container">
        <h2 class="section-title">字体系统</h2>
        <div class="typography-showcase">
          <div class="typography-item">
            <div class="heading-1">H1 主标题 - Inter Bold 36px</div>
            <div class="heading-2">H2 二级标题 - Inter Semibold 30px</div>
            <div class="heading-3">H3 三级标题 - Inter Semibold 24px</div>
            <div class="heading-4">H4 四级标题 - Inter Medium 20px</div>
            <div class="body-large">大号正文 - Inter Regular 18px</div>
            <div class="body-normal">标准正文 - Inter Regular 16px</div>
            <div class="body-small">小号正文 - Inter Regular 14px</div>
          </div>
        </div>
      </div>
    </section>

    <!-- Buttons -->
    <section class="section" style="background: var(--color-bg-secondary)">
      <div class="container">
        <h2 class="section-title">按钮组件</h2>
        <div class="button-showcase">
          <button class="btn btn-primary">主要按钮</button>
          <button class="btn btn-secondary">次要按钮</button>
          <button class="btn btn-outline">轮廓按钮</button>
        </div>
      </div>
    </section>

    <!-- Icons -->
    <section class="section">
      <div class="container">
        <h2 class="section-title">图标系统</h2>
        <div class="icon-showcase">
          <div class="icon-item">
            <div class="icon-placeholder"></div>
            <div style="font-size: 0.75rem">搜索</div>
          </div>
          <div class="icon-item">
            <div class="icon-placeholder"></div>
            <div style="font-size: 0.75rem">上传</div>
          </div>
          <div class="icon-item">
            <div class="icon-placeholder"></div>
            <div style="font-size: 0.75rem">下载</div>
          </div>
          <div class="icon-item">
            <div class="icon-placeholder"></div>
            <div style="font-size: 0.75rem">用户</div>
          </div>
          <div class="icon-item">
            <div class="icon-placeholder"></div>
            <div style="font-size: 0.75rem">设置</div>
          </div>
          <div class="icon-item">
            <div class="icon-placeholder"></div>
            <div style="font-size: 0.75rem">帮助</div>
          </div>
        </div>
      </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
      <div class="container">
        <p>&copy; 2025 AIGC Service Hub. 品牌素材库展示页面</p>
      </div>
    </footer>
  </body>
</html>
