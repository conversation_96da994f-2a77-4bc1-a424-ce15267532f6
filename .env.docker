# AIGC Service Hub Docker Environment Variables
# 复制此文件为 .env 并填入真实的配置值

# ================================
# 数据库配置
# ================================
DB_NAME=aigc_service_hub
DB_USER=postgres
DB_PASSWORD=your_secure_database_password_here

# ================================
# JWT配置
# ================================
JWT_SECRET=your_super_secure_jwt_secret_key_minimum_32_characters_here
JWT_REFRESH_SECRET=your_super_secure_refresh_token_secret_key_here

# ================================
# PayPal配置
# ================================
PAYPAL_CLIENT_ID=your_paypal_client_id_here
PAYPAL_CLIENT_SECRET=your_paypal_client_secret_here
PAYPAL_MODE=live
PAYPAL_WEBHOOK_ID=your_paypal_webhook_id_here

# ================================
# AWS配置
# ================================
AWS_ACCESS_KEY_ID=your_aws_access_key_id_here
AWS_SECRET_ACCESS_KEY=your_aws_secret_access_key_here
AWS_REGION=us-west-2
AWS_S3_BUCKET=aigc-service-hub-files
AWS_CLOUDFRONT_DOMAIN=your_cloudfront_domain.cloudfront.net

# ================================
# 应用配置
# ================================
CORS_ORIGIN=https://aigc-service-hub.com
APP_DOMAIN=aigc-service-hub.com

# ================================
# 邮件服务配置
# ================================
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your_app_password
FROM_EMAIL=<EMAIL>
FROM_NAME=AIGC Service Hub

# ================================
# 监控配置
# ================================
GRAFANA_USER=admin
GRAFANA_PASSWORD=your_secure_grafana_password_here

# ================================
# Redis配置 (可选)
# ================================
REDIS_PASSWORD=your_redis_password_here

# ================================
# SSL证书配置
# ================================
SSL_CERT_PATH=/etc/nginx/ssl/cert.pem
SSL_KEY_PATH=/etc/nginx/ssl/key.pem

# ================================
# 备份配置
# ================================
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30
BACKUP_S3_BUCKET=aigc-service-hub-backups

# ================================
# 安全配置
# ================================
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
BCRYPT_ROUNDS=12

# ================================
# 业务配置
# ================================
POINTS_TO_USD_RATE=0.01
WITHDRAWAL_MIN_AMOUNT=10.00
WITHDRAWAL_FEE_RATE=0.02
MAX_FILE_SIZE=32212254720

# ================================
# 功能开关
# ================================
ENABLE_REGISTRATION=true
ENABLE_EMAIL_VERIFICATION=true
ENABLE_RATE_LIMITING=true
MAINTENANCE_MODE=false
