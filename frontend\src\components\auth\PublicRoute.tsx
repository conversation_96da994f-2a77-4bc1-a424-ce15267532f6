import React, { ReactNode } from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { Box, CircularProgress, Typography } from '@mui/material';
import { useAuth } from '../../hooks/useAuth';

interface PublicRouteProps {
  children: ReactNode;
  redirectTo?: string;
}

// 加载组件
const LoadingScreen: React.FC = () => (
  <Box
    sx={{
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      justifyContent: 'center',
      minHeight: '100vh',
      gap: 2,
    }}
  >
    <CircularProgress size={48} />
    <Typography variant="body1" color="text.secondary">
      正在检查登录状态...
    </Typography>
  </Box>
);

// 公共路由组件（用于登录、注册等页面）
const PublicRoute: React.FC<PublicRouteProps> = ({
  children,
  redirectTo = '/dashboard',
}) => {
  const { isAuthenticated, initialized, loading } = useAuth();
  const location = useLocation();

  // 如果还在初始化或加载中，显示加载界面
  if (!initialized || loading) {
    return <LoadingScreen />;
  }

  // 如果已经认证，重定向到指定页面
  if (isAuthenticated) {
    // 检查是否有来源页面，如果有则重定向到来源页面
    const from = (location.state as any)?.from?.pathname || redirectTo;
    return <Navigate to={from} replace />;
  }

  // 未认证，渲染子组件
  return <>{children}</>;
};

export default PublicRoute;
