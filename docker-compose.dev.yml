# AIGC Service Hub - 开发环境 Docker Compose 配置
version: '3.8'

services:
  # PostgreSQL 数据库
  postgres:
    image: postgres:14-alpine
    container_name: aigc-postgres-dev
    environment:
      POSTGRES_DB: aigc_service_hub_dev
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
    ports:
      - "5432:5432"
    volumes:
      - postgres_dev_data:/var/lib/postgresql/data
      - ./backend/src/database/migrations:/docker-entrypoint-initdb.d:ro
    networks:
      - aigc-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d aigc_service_hub_dev"]
      interval: 10s
      timeout: 5s
      retries: 5
    restart: unless-stopped

  # Redis 缓存 (可选)
  redis:
    image: redis:7-alpine
    container_name: aigc-redis-dev
    ports:
      - "6379:6379"
    volumes:
      - redis_dev_data:/data
    networks:
      - aigc-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 3s
      retries: 3
    restart: unless-stopped
    command: redis-server --appendonly yes

  # 后端 API 服务
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
      target: runtime
    container_name: aigc-backend-dev
    environment:
      - NODE_ENV=development
      - PORT=3000
      - DATABASE_URL=********************************************/aigc_service_hub_dev
      - REDIS_URL=redis://redis:6379
      - JWT_SECRET=dev_jwt_secret_key_for_development_only_32_chars_minimum
      - CORS_ORIGIN=http://localhost:5173
      - LOG_LEVEL=debug
    ports:
      - "3000:3000"
    volumes:
      - ./backend:/app:ro
      - /app/node_modules
      - backend_logs:/app/logs
    networks:
      - aigc-network
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    restart: unless-stopped
    command: ["npm", "run", "dev"]

  # 前端应用 (开发模式)
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile.dev
    container_name: aigc-frontend-dev
    environment:
      - VITE_API_BASE_URL=http://localhost:3000/api
      - VITE_APP_NAME=AIGC Service Hub
      - VITE_APP_VERSION=1.0.0-dev
    ports:
      - "5173:5173"
    volumes:
      - ./frontend:/app:ro
      - /app/node_modules
    networks:
      - aigc-network
    depends_on:
      - backend
    restart: unless-stopped
    command: ["npm", "run", "dev", "--", "--host", "0.0.0.0"]

  # pgAdmin 数据库管理工具
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: aigc-pgadmin-dev
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin123
      PGADMIN_CONFIG_SERVER_MODE: 'False'
    ports:
      - "8080:80"
    volumes:
      - pgadmin_dev_data:/var/lib/pgadmin
    networks:
      - aigc-network
    depends_on:
      - postgres
    restart: unless-stopped

  # Mailhog 邮件测试工具
  mailhog:
    image: mailhog/mailhog:latest
    container_name: aigc-mailhog-dev
    ports:
      - "1025:1025"  # SMTP
      - "8025:8025"  # Web UI
    networks:
      - aigc-network
    restart: unless-stopped

volumes:
  postgres_dev_data:
    driver: local
  redis_dev_data:
    driver: local
  pgadmin_dev_data:
    driver: local
  backend_logs:
    driver: local

networks:
  aigc-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
