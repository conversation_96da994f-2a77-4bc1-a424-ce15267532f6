import React, { ReactNode } from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { Box, CircularProgress, Typography } from '@mui/material';
import { useAuth } from '../../hooks/useAuth';

interface ProtectedRouteProps {
  children: ReactNode;
  requireEmailVerification?: boolean;
  allowedUserTypes?: ('individual' | 'enterprise')[];
}

// 加载组件
const LoadingScreen: React.FC = () => (
  <Box
    sx={{
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      justifyContent: 'center',
      minHeight: '100vh',
      gap: 2,
    }}
  >
    <CircularProgress size={48} />
    <Typography variant="body1" color="text.secondary">
      正在验证身份...
    </Typography>
  </Box>
);

// 受保护的路由组件
const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  requireEmailVerification = false,
  allowedUserTypes,
}) => {
  const { user, isAuthenticated, initialized, loading } = useAuth();
  const location = useLocation();

  // 如果还在初始化或加载中，显示加载界面
  if (!initialized || loading) {
    return <LoadingScreen />;
  }

  // 如果未认证，重定向到登录页面
  if (!isAuthenticated) {
    return (
      <Navigate
        to="/login"
        state={{ from: location }}
        replace
      />
    );
  }

  // 如果需要邮箱验证但用户邮箱未验证
  if (requireEmailVerification && user && !user.emailVerified) {
    return (
      <Navigate
        to="/verify-email"
        state={{ from: location }}
        replace
      />
    );
  }

  // 如果指定了允许的用户类型，检查用户类型
  if (allowedUserTypes && user && !allowedUserTypes.includes(user.userType)) {
    return (
      <Navigate
        to="/unauthorized"
        state={{ from: location }}
        replace
      />
    );
  }

  // 检查用户状态
  if (user) {
    switch (user.status) {
      case 'pending':
        return (
          <Navigate
            to="/account-pending"
            state={{ from: location }}
            replace
          />
        );
      case 'suspended':
        return (
          <Navigate
            to="/account-suspended"
            state={{ from: location }}
            replace
          />
        );
      case 'banned':
        return (
          <Navigate
            to="/account-banned"
            state={{ from: location }}
            replace
          />
        );
    }
  }

  // 所有检查通过，渲染子组件
  return <>{children}</>;
};

export default ProtectedRoute;
