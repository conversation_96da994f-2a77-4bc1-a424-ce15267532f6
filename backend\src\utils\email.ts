import nodemailer from 'nodemailer';

// 邮件配置
const emailConfig = {
  host: process.env.SMTP_HOST || 'smtp.gmail.com',
  port: parseInt(process.env.SMTP_PORT || '587'),
  secure: false, // true for 465, false for other ports
  auth: {
    user: process.env.SMTP_USER,
    pass: process.env.SMTP_PASS
  }
};

// 创建邮件传输器
const transporter = nodemailer.createTransporter(emailConfig);

// 验证邮件配置
export async function verifyEmailConfig(): Promise<boolean> {
  try {
    await transporter.verify();
    console.log('Email configuration is valid');
    return true;
  } catch (error) {
    console.error('Email configuration error:', error);
    return false;
  }
}

// 发送邮箱验证邮件
export async function sendVerificationEmail(email: string, token: string): Promise<void> {
  const verificationUrl = `${process.env.FRONTEND_URL}/verify-email/${token}`;
  
  const mailOptions = {
    from: {
      name: 'AIGC Service Hub',
      address: process.env.SMTP_USER || '<EMAIL>'
    },
    to: email,
    subject: '验证您的邮箱地址 - AIGC Service Hub',
    html: `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>邮箱验证</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: linear-gradient(135deg, #1976d2, #42a5f5); color: white; padding: 30px; text-align: center; border-radius: 8px 8px 0 0; }
          .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 8px 8px; }
          .button { display: inline-block; background: #1976d2; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; margin: 20px 0; }
          .footer { text-align: center; margin-top: 30px; color: #666; font-size: 14px; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>欢迎加入 AIGC Service Hub</h1>
            <p>全球领先的AI创作者服务平台</p>
          </div>
          <div class="content">
            <h2>验证您的邮箱地址</h2>
            <p>感谢您注册AIGC Service Hub！为了确保账户安全，请点击下面的按钮验证您的邮箱地址：</p>
            
            <div style="text-align: center;">
              <a href="${verificationUrl}" class="button">验证邮箱</a>
            </div>
            
            <p>如果按钮无法点击，请复制以下链接到浏览器地址栏：</p>
            <p style="word-break: break-all; background: #eee; padding: 10px; border-radius: 4px;">
              ${verificationUrl}
            </p>
            
            <p><strong>注意：</strong></p>
            <ul>
              <li>此验证链接将在24小时后过期</li>
              <li>如果您没有注册AIGC Service Hub账户，请忽略此邮件</li>
              <li>请勿将此链接分享给他人</li>
            </ul>
          </div>
          <div class="footer">
            <p>此邮件由系统自动发送，请勿回复</p>
            <p>© 2025 AIGC Service Hub. All rights reserved.</p>
          </div>
        </div>
      </body>
      </html>
    `,
    text: `
      欢迎加入 AIGC Service Hub！
      
      请点击以下链接验证您的邮箱地址：
      ${verificationUrl}
      
      此验证链接将在24小时后过期。
      如果您没有注册AIGC Service Hub账户，请忽略此邮件。
      
      © 2025 AIGC Service Hub. All rights reserved.
    `
  };

  await transporter.sendMail(mailOptions);
}

// 发送密码重置邮件
export async function sendPasswordResetEmail(email: string, token: string): Promise<void> {
  const resetUrl = `${process.env.FRONTEND_URL}/reset-password/${token}`;
  
  const mailOptions = {
    from: {
      name: 'AIGC Service Hub',
      address: process.env.SMTP_USER || '<EMAIL>'
    },
    to: email,
    subject: '重置您的密码 - AIGC Service Hub',
    html: `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>密码重置</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: linear-gradient(135deg, #1976d2, #42a5f5); color: white; padding: 30px; text-align: center; border-radius: 8px 8px 0 0; }
          .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 8px 8px; }
          .button { display: inline-block; background: #1976d2; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; margin: 20px 0; }
          .footer { text-align: center; margin-top: 30px; color: #666; font-size: 14px; }
          .warning { background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 4px; margin: 20px 0; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>密码重置请求</h1>
            <p>AIGC Service Hub</p>
          </div>
          <div class="content">
            <h2>重置您的密码</h2>
            <p>我们收到了您的密码重置请求。如果这是您本人的操作，请点击下面的按钮重置密码：</p>
            
            <div style="text-align: center;">
              <a href="${resetUrl}" class="button">重置密码</a>
            </div>
            
            <p>如果按钮无法点击，请复制以下链接到浏览器地址栏：</p>
            <p style="word-break: break-all; background: #eee; padding: 10px; border-radius: 4px;">
              ${resetUrl}
            </p>
            
            <div class="warning">
              <p><strong>安全提醒：</strong></p>
              <ul>
                <li>此重置链接将在1小时后过期</li>
                <li>如果您没有请求重置密码，请忽略此邮件</li>
                <li>请勿将此链接分享给他人</li>
                <li>建议使用强密码保护您的账户</li>
              </ul>
            </div>
          </div>
          <div class="footer">
            <p>此邮件由系统自动发送，请勿回复</p>
            <p>© 2025 AIGC Service Hub. All rights reserved.</p>
          </div>
        </div>
      </body>
      </html>
    `,
    text: `
      密码重置请求 - AIGC Service Hub
      
      我们收到了您的密码重置请求。如果这是您本人的操作，请点击以下链接重置密码：
      ${resetUrl}
      
      此重置链接将在1小时后过期。
      如果您没有请求重置密码，请忽略此邮件。
      
      © 2025 AIGC Service Hub. All rights reserved.
    `
  };

  await transporter.sendMail(mailOptions);
}

// 发送欢迎邮件
export async function sendWelcomeEmail(email: string, username: string): Promise<void> {
  const mailOptions = {
    from: {
      name: 'AIGC Service Hub',
      address: process.env.SMTP_USER || '<EMAIL>'
    },
    to: email,
    subject: '欢迎加入 AIGC Service Hub！',
    html: `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>欢迎加入</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: linear-gradient(135deg, #1976d2, #42a5f5); color: white; padding: 30px; text-align: center; border-radius: 8px 8px 0 0; }
          .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 8px 8px; }
          .button { display: inline-block; background: #1976d2; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; margin: 20px 0; }
          .footer { text-align: center; margin-top: 30px; color: #666; font-size: 14px; }
          .feature { background: white; padding: 20px; margin: 15px 0; border-radius: 8px; border-left: 4px solid #1976d2; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>欢迎加入 AIGC Service Hub！</h1>
            <p>全球领先的AI创作者服务平台</p>
          </div>
          <div class="content">
            <h2>您好，${username}！</h2>
            <p>恭喜您成功注册AIGC Service Hub！我们很高兴您加入我们的创作者社区。</p>
            
            <h3>平台特色功能：</h3>
            
            <div class="feature">
              <h4>🎨 资源交易</h4>
              <p>上传和销售您的AI模型、LoRA、工作流、提示词和工具</p>
            </div>
            
            <div class="feature">
              <h4>💰 智能分佣</h4>
              <p>阶梯式分佣系统，销量越高，收益越多</p>
            </div>
            
            <div class="feature">
              <h4>🏆 积分系统</h4>
              <p>每日签到获得积分，积分可用于购买资源</p>
            </div>
            
            <div class="feature">
              <h4>🔒 安全保障</h4>
              <p>7天冻结期保护，PayPal安全支付</p>
            </div>
            
            <div style="text-align: center;">
              <a href="${process.env.FRONTEND_URL}/dashboard" class="button">开始创作之旅</a>
            </div>
            
            <p>如果您有任何问题，请随时联系我们的客服团队。</p>
          </div>
          <div class="footer">
            <p>此邮件由系统自动发送，请勿回复</p>
            <p>© 2025 AIGC Service Hub. All rights reserved.</p>
          </div>
        </div>
      </body>
      </html>
    `,
    text: `
      欢迎加入 AIGC Service Hub！
      
      您好，${username}！
      
      恭喜您成功注册AIGC Service Hub！我们很高兴您加入我们的创作者社区。
      
      平台特色功能：
      - 资源交易：上传和销售您的AI模型、LoRA、工作流、提示词和工具
      - 智能分佣：阶梯式分佣系统，销量越高，收益越多
      - 积分系统：每日签到获得积分，积分可用于购买资源
      - 安全保障：7天冻结期保护，PayPal安全支付
      
      立即开始：${process.env.FRONTEND_URL}/dashboard
      
      如果您有任何问题，请随时联系我们的客服团队。
      
      © 2025 AIGC Service Hub. All rights reserved.
    `
  };

  await transporter.sendMail(mailOptions);
}
