import { describe, it, expect } from 'vitest';
import { render, screen } from '@testing-library/react';
import App from './App';

describe('App Component', () => {
  it('renders AIGC Service Hub title', () => {
    render(<App />);

    // 检查主标题是否渲染
    const title = screen.getByText('欢迎来到 AIGC Service Hub');
    expect(title).toBeInTheDocument();
  });

  it('renders subtitle', () => {
    render(<App />);

    // 检查副标题是否渲染
    const subtitle = screen.getByText('全球领先的AI创作者服务平台');
    expect(subtitle).toBeInTheDocument();
  });

  it('renders navigation bar', () => {
    render(<App />);

    // 检查导航栏标题是否渲染
    const navTitle = screen.getByText('AIGC Service Hub');
    expect(navTitle).toBeInTheDocument();
  });

  it('renders feature list', () => {
    render(<App />);

    // 检查功能特性是否渲染
    expect(screen.getByText('🤖 AI模型交易')).toBeInTheDocument();
    expect(screen.getByText('💰 智能分佣系统')).toBeInTheDocument();
    expect(screen.getByText('🔒 安全支付保障')).toBeInTheDocument();
  });

  it('renders development status', () => {
    render(<App />);

    // 检查开发状态提示是否渲染
    const devStatus = screen.getByText('🚧 开发中');
    expect(devStatus).toBeInTheDocument();
  });
});
