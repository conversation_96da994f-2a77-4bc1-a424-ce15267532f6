# AIGC Service Hub Prometheus Configuration

global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  # - "first_rules.yml"
  # - "second_rules.yml"

scrape_configs:
  # Prometheus自身监控
  - job_name: "prometheus"
    static_configs:
      - targets: ["localhost:9090"]

  # AIGC Service Hub 后端API监控
  - job_name: "aigc-backend"
    static_configs:
      - targets: ["backend:3000"]
    metrics_path: "/api/metrics"
    scrape_interval: 30s
    scrape_timeout: 10s

  # Nginx监控
  - job_name: "nginx"
    static_configs:
      - targets: ["nginx:8080"]
    metrics_path: "/nginx_status"
    scrape_interval: 30s

  # PostgreSQL监控 (需要postgres_exporter)
  - job_name: "postgres"
    static_configs:
      - targets: ["postgres-exporter:9187"]
    scrape_interval: 30s

  # Redis监控 (需要redis_exporter)
  - job_name: "redis"
    static_configs:
      - targets: ["redis-exporter:9121"]
    scrape_interval: 30s

  # Node.js应用监控
  - job_name: "node-exporter"
    static_configs:
      - targets: ["node-exporter:9100"]
    scrape_interval: 30s

# 告警规则配置
alerting:
  alertmanagers:
    - static_configs:
        - targets:
          # - alertmanager:9093
# 远程写入配置 (可选)
# remote_write:
#   - url: "https://your-remote-prometheus-endpoint/api/v1/write"

# 远程读取配置 (可选)
# remote_read:
#   - url: "https://your-remote-prometheus-endpoint/api/v1/read"
