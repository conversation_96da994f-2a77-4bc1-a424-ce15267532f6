import { Brightness4, Brightness7 } from '@mui/icons-material';
import {
  AppBar,
  Box,
  Container,
  CssBaseline,
  IconButton,
  ThemeProvider,
  Toolbar,
  Typography,
} from '@mui/material';
import { getTheme, themeStorage } from '@utils/theme';
import { useEffect, useState } from 'react';
import { BrowserRouter as Router } from 'react-router-dom';

function App() {
  const [themeMode, setThemeMode] = useState<'light' | 'dark'>('light');

  // 初始化主题模式
  useEffect(() => {
    const savedMode = themeStorage.get();
    setThemeMode(savedMode);
  }, []);

  // 切换主题模式
  const toggleTheme = () => {
    const newMode = themeMode === 'light' ? 'dark' : 'light';
    setThemeMode(newMode);
    themeStorage.set(newMode);
  };

  const theme = getTheme(themeMode);

  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <Router>
        <Box sx={{ flexGrow: 1 }}>
          {/* 顶部导航栏 */}
          <AppBar position='static' elevation={1}>
            <Toolbar>
              <Typography
                variant='h6'
                component='div'
                sx={{ flexGrow: 1, fontWeight: 600 }}
              >
                AIGC Service Hub
              </Typography>
              <IconButton
                color='inherit'
                onClick={toggleTheme}
                aria-label='切换主题'
              >
                {themeMode === 'dark' ? <Brightness7 /> : <Brightness4 />}
              </IconButton>
            </Toolbar>
          </AppBar>

          {/* 主要内容区域 */}
          <Container maxWidth='lg' sx={{ mt: 4, mb: 4 }}>
            <Box
              sx={{
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                textAlign: 'center',
                py: 8,
              }}
            >
              <Typography
                variant='h2'
                component='h1'
                gutterBottom
                color='primary'
              >
                欢迎来到 AIGC Service Hub
              </Typography>
              <Typography
                variant='h5'
                component='h2'
                gutterBottom
                color='text.secondary'
                sx={{ mb: 4 }}
              >
                全球领先的AI创作者服务平台
              </Typography>
              <Typography
                variant='body1'
                color='text.secondary'
                sx={{ maxWidth: 600, mb: 4 }}
              >
                为AI创作者提供一个自由、高效的资源交易与共享空间，支持微调模型、LoRA、工作流、提示词等AI资源的上传与交易。
              </Typography>

              {/* 功能特性展示 */}
              <Box
                sx={{
                  display: 'flex',
                  flexWrap: 'wrap',
                  gap: 2,
                  justifyContent: 'center',
                  mt: 4,
                }}
              >
                {[
                  '🤖 AI模型交易',
                  '💰 智能分佣系统',
                  '🔒 安全支付保障',
                  '📊 数据统计分析',
                  '🎮 游戏化积分',
                  '🏆 创作者排名',
                ].map((feature, index) => (
                  <Box
                    key={index}
                    sx={{
                      px: 3,
                      py: 1.5,
                      bgcolor: 'background.paper',
                      borderRadius: 2,
                      border: 1,
                      borderColor: 'divider',
                      fontSize: '0.875rem',
                      fontWeight: 500,
                    }}
                  >
                    {feature}
                  </Box>
                ))}
              </Box>

              {/* 开发状态提示 */}
              <Box
                sx={{
                  mt: 6,
                  p: 3,
                  bgcolor: 'info.main',
                  color: 'info.contrastText',
                  borderRadius: 2,
                  maxWidth: 500,
                }}
              >
                <Typography variant='h6' gutterBottom>
                  🚧 开发中
                </Typography>
                <Typography variant='body2'>
                  前端开发环境已配置完成！
                  <br />
                  React + Vite + Material-UI + TypeScript 技术栈就绪
                </Typography>
              </Box>
            </Box>
          </Container>
        </Box>
      </Router>
    </ThemeProvider>
  );
}

export default App;
