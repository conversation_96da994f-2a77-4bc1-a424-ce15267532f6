import {
  Add,
  AudioFile,
  AutoAwesome,
  Build,
  Download,
  EmojiEvents,
  Favorite,
  Home,
  Image,
  Language,
  Login,
  MonetizationOn,
  PlayArrow,
  Search,
  Share,
  Star,
  TextFields,
  TrendingUp,
  VideoFile,
} from '@mui/icons-material';
import {
  AppBar,
  Avatar,
  Box,
  Button,
  Card,
  CardContent,
  CardMedia,
  Container,
  Grid,
  IconButton,
  InputBase,
  Menu,
  MenuItem,
  Paper,
  Stack,
  Toolbar,
  Typography,
} from '@mui/material';
import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';

// 菜单栏数据
const menuItems = [
  { id: 'home', name: '首页', icon: <Home />, path: '/' },
  {
    id: 'fine_tuned',
    name: '微调模型',
    icon: <AutoAwesome />,
    path: '/marketplace?category=fine_tuned_models',
  },
  {
    id: 'lora',
    name: 'LoRA',
    icon: <TrendingUp />,
    path: '/marketplace?category=lora',
  },
  {
    id: 'workflows',
    name: '工作流',
    icon: <PlayArrow />,
    path: '/marketplace?category=workflows',
  },
  {
    id: 'prompts',
    name: '提示词',
    icon: <Star />,
    path: '/marketplace?category=prompts',
  },
  {
    id: 'tools',
    name: '工具',
    icon: <Build />,
    path: '/marketplace?category=tools',
  },
  {
    id: 'challenges',
    name: '挑战',
    icon: <EmojiEvents />,
    path: '/challenges',
  },
  { id: 'bounty', name: '悬赏', icon: <MonetizationOn />, path: '/bounty' },
];

// 分类栏数据（由创作者填写自动生成）
const contentTypes = [
  { id: 'video', name: '视频', icon: <VideoFile />, count: 1234 },
  { id: 'audio', name: '音频', icon: <AudioFile />, count: 567 },
  { id: 'image', name: '图片', icon: <Image />, count: 8901 },
  { id: 'text', name: '文本', icon: <TextFields />, count: 2345 },
];

// 风格栏数据（由创作者填写自动生成）
const styleCategories = [
  '电商',
  '网页',
  '写真',
  '节日',
  '动漫',
  '国画',
  '建筑',
  '园林',
  '摄影',
  '卡通',
  '人像',
  '老照片',
  '美女',
  '男人',
  '女人',
  '素材',
];

// 轮播图数据
const carouselItems = [
  {
    id: 1,
    title: 'AI创作大赛开始啦！',
    subtitle: '参与创作，赢取丰厚奖品',
    image: '1920x350 轮播图1',
    link: '/contest',
  },
  {
    id: 2,
    title: '新功能上线',
    subtitle: '支持更多AI模型格式',
    image: '1920x350 轮播图2',
    link: '/features',
  },
  {
    id: 3,
    title: '创作者激励计划',
    subtitle: '上传作品即可获得积分奖励',
    image: '1920x350 轮播图3',
    link: '/incentive',
  },
];

// 英雄榜数据（系统自动生成）
const heroRanking = [
  {
    rank: 1,
    name: 'AI大师',
    avatar: '60x60 头像1',
    downloads: 15420,
    works: 89,
    likes: 3421,
    badge: 'gold',
  },
  {
    rank: 2,
    name: '创作专家',
    avatar: '60x60 头像2',
    downloads: 12890,
    works: 67,
    likes: 2876,
    badge: 'silver',
  },
  {
    rank: 3,
    name: '设计师',
    avatar: '60x60 头像3',
    downloads: 9876,
    works: 45,
    likes: 2134,
    badge: 'bronze',
  },
];

// 精选作品数据（管理员选定）
const featuredWorks = [
  {
    id: 1,
    title: '精美人像LoRA模型',
    creator: 'AI艺术家',
    image: '400x300 精选作品1',
    downloads: 2341,
    likes: 567,
    price: 29.99,
  },
  {
    id: 2,
    title: '商业摄影工作流',
    creator: '摄影大师',
    image: '400x300 精选作品2',
    downloads: 1876,
    likes: 432,
    price: 19.99,
  },
  {
    id: 3,
    title: '动漫风格提示词集',
    creator: '动漫创作者',
    image: '400x300 精选作品3',
    downloads: 3421,
    likes: 789,
    price: 9.99,
  },
];

const NewHomePage: React.FC = () => {
  const navigate = useNavigate();
  const [currentSlide, setCurrentSlide] = useState(0);
  const [selectedContentType, setSelectedContentType] = useState('all');
  const [selectedStyle, setSelectedStyle] = useState('all');
  const [language, setLanguage] = useState('en');
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);

  const handleLanguageClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleLanguageClose = () => {
    setAnchorEl(null);
  };

  const handleLanguageChange = (lang: string) => {
    setLanguage(lang);
    handleLanguageClose();
  };

  return (
    <Box sx={{ bgcolor: '#f8f9fa', minHeight: '100vh' }}>
      {/* H1 页眉 - 分为四行 */}

      {/* 1. 头部 - 高100px */}
      <AppBar
        position='sticky'
        sx={{
          bgcolor: '#ffffff',
          color: '#333333',
          boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
          height: 100,
        }}
      >
        <Toolbar sx={{ height: 100, px: 3 }}>
          {/* LOGO */}
          <Box
            sx={{
              display: 'flex',
              alignItems: 'center',
              cursor: 'pointer',
              mr: 4,
            }}
            onClick={() => navigate('/')}
          >
            <AutoAwesome sx={{ fontSize: 32, color: '#2563EB', mr: 1 }} />
            <Typography
              variant='h5'
              component='div'
              sx={{
                fontWeight: 'bold',
                color: '#2563EB',
                fontSize: '1.5rem',
              }}
            >
              AIGC Hub
            </Typography>
          </Box>

          {/* 搜索框 */}
          <Box
            sx={{
              position: 'relative',
              borderRadius: 25,
              backgroundColor: '#f5f5f5',
              '&:hover': {
                backgroundColor: '#eeeeee',
              },
              marginLeft: 2,
              width: { xs: 'auto', sm: '400px' },
              height: 40,
            }}
          >
            <Box
              sx={{
                padding: '0 16px',
                height: '100%',
                position: 'absolute',
                pointerEvents: 'none',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
              }}
            >
              <Search sx={{ color: '#666666' }} />
            </Box>
            <InputBase
              placeholder='搜索AI资源...'
              sx={{
                color: '#333333',
                width: '100%',
                height: '100%',
                '& .MuiInputBase-input': {
                  padding: '8px 8px 8px 48px',
                  fontSize: '0.95rem',
                },
              }}
            />
          </Box>

          <Box sx={{ flexGrow: 1 }} />

          {/* 右侧操作区域 */}
          <Stack direction='row' spacing={2} alignItems='center'>
            {/* 发布+ */}
            <Button
              variant='contained'
              startIcon={<Add />}
              sx={{
                bgcolor: '#2563EB',
                color: '#ffffff',
                borderRadius: 20,
                px: 3,
                height: 36,
                '&:hover': {
                  bgcolor: '#1d4ed8',
                },
              }}
              onClick={() => navigate('/upload')}
            >
              发布
            </Button>

            {/* 登录 */}
            <Button
              variant='outlined'
              startIcon={<Login />}
              sx={{
                borderColor: '#2563EB',
                color: '#2563EB',
                borderRadius: 20,
                px: 3,
                height: 36,
                '&:hover': {
                  borderColor: '#1d4ed8',
                  bgcolor: 'rgba(37, 99, 235, 0.04)',
                },
              }}
              onClick={() => navigate('/login')}
            >
              登录
            </Button>

            {/* 分享 */}
            <IconButton
              sx={{
                color: '#666666',
                '&:hover': {
                  bgcolor: 'rgba(0,0,0,0.04)',
                },
              }}
            >
              <Share />
            </IconButton>

            {/* 语言切换 */}
            <IconButton
              onClick={handleLanguageClick}
              sx={{
                color: '#666666',
                '&:hover': {
                  bgcolor: 'rgba(0,0,0,0.04)',
                },
              }}
            >
              <Language />
            </IconButton>
            <Menu
              anchorEl={anchorEl}
              open={Boolean(anchorEl)}
              onClose={handleLanguageClose}
            >
              <MenuItem
                onClick={() => handleLanguageChange('en')}
                selected={language === 'en'}
              >
                English
              </MenuItem>
              <MenuItem
                onClick={() => handleLanguageChange('zh')}
                selected={language === 'zh'}
              >
                中文
              </MenuItem>
            </Menu>
          </Stack>
        </Toolbar>
      </AppBar>

      {/* 2. 菜单栏 - 高30px */}
      <Box
        sx={{
          bgcolor: '#ffffff',
          borderBottom: '1px solid #e0e0e0',
          height: 30,
        }}
      >
        <Container maxWidth='xl'>
          <Stack
            direction='row'
            spacing={0}
            sx={{
              height: 30,
              alignItems: 'center',
            }}
          >
            {menuItems.map(item => (
              <Button
                key={item.id}
                startIcon={item.icon}
                onClick={() => navigate(item.path)}
                sx={{
                  color: '#333333',
                  fontSize: '0.85rem',
                  fontWeight: 500,
                  px: 2,
                  py: 0.5,
                  minHeight: 30,
                  borderRadius: 0,
                  '&:hover': {
                    bgcolor: 'rgba(37, 99, 235, 0.04)',
                    color: '#2563EB',
                  },
                }}
              >
                {item.name}
              </Button>
            ))}
          </Stack>
        </Container>
      </Box>

      {/* 3. 分类栏 - 高26px */}
      <Box
        sx={{
          bgcolor: '#f8f9fa',
          borderBottom: '1px solid #e0e0e0',
          height: 26,
        }}
      >
        <Container maxWidth='xl'>
          <Stack
            direction='row'
            spacing={3}
            sx={{
              height: 26,
              alignItems: 'center',
            }}
          >
            <Typography
              variant='body2'
              sx={{
                color: '#666666',
                fontSize: '0.8rem',
                fontWeight: 500,
                mr: 1,
              }}
            >
              分类:
            </Typography>
            {contentTypes.map(type => (
              <Button
                key={type.id}
                startIcon={type.icon}
                onClick={() => setSelectedContentType(type.id)}
                sx={{
                  color:
                    selectedContentType === type.id ? '#2563EB' : '#666666',
                  fontSize: '0.75rem',
                  fontWeight: 500,
                  px: 1.5,
                  py: 0,
                  minHeight: 24,
                  borderRadius: 12,
                  bgcolor:
                    selectedContentType === type.id
                      ? 'rgba(37, 99, 235, 0.08)'
                      : 'transparent',
                  '&:hover': {
                    bgcolor: 'rgba(37, 99, 235, 0.08)',
                    color: '#2563EB',
                  },
                }}
              >
                {type.name} ({type.count})
              </Button>
            ))}
          </Stack>
        </Container>
      </Box>

      {/* 4. 风格栏 - 高20px */}
      <Box
        sx={{
          bgcolor: '#ffffff',
          borderBottom: '1px solid #e0e0e0',
          height: 20,
        }}
      >
        <Container maxWidth='xl'>
          <Stack
            direction='row'
            spacing={2}
            sx={{
              height: 20,
              alignItems: 'center',
              overflow: 'hidden',
            }}
          >
            <Typography
              variant='body2'
              sx={{
                color: '#666666',
                fontSize: '0.75rem',
                fontWeight: 500,
                mr: 1,
                whiteSpace: 'nowrap',
              }}
            >
              风格:
            </Typography>
            {styleCategories.map(style => (
              <Button
                key={style}
                onClick={() => setSelectedStyle(style)}
                sx={{
                  color: selectedStyle === style ? '#2563EB' : '#666666',
                  fontSize: '0.7rem',
                  fontWeight: 400,
                  px: 1,
                  py: 0,
                  minHeight: 18,
                  minWidth: 'auto',
                  borderRadius: 9,
                  bgcolor:
                    selectedStyle === style
                      ? 'rgba(37, 99, 235, 0.08)'
                      : 'transparent',
                  '&:hover': {
                    bgcolor: 'rgba(37, 99, 235, 0.08)',
                    color: '#2563EB',
                  },
                }}
              >
                {style}
              </Button>
            ))}
          </Stack>
        </Container>
      </Box>

      {/* H2 分两段 */}
      <Container maxWidth='xl' sx={{ mt: 2 }}>
        <Grid container spacing={2} sx={{ height: 350 }}>
          {/* 1. 轮播图活动 - 占5/4 */}
          <Grid item xs={12} md={8}>
            <Paper
              sx={{
                height: 350,
                borderRadius: 2,
                overflow: 'hidden',
                position: 'relative',
                bgcolor: '#e3f2fd',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                cursor: 'pointer',
              }}
              onClick={() => navigate(carouselItems[currentSlide].link)}
            >
              <Box sx={{ textAlign: 'center', p: 4 }}>
                <Typography
                  variant='h4'
                  sx={{
                    color: '#1976d2',
                    fontWeight: 'bold',
                    mb: 2,
                  }}
                >
                  {carouselItems[currentSlide].title}
                </Typography>
                <Typography
                  variant='h6'
                  sx={{
                    color: '#666666',
                    mb: 3,
                  }}
                >
                  {carouselItems[currentSlide].subtitle}
                </Typography>
                <Typography
                  variant='body2'
                  sx={{
                    color: '#999999',
                    fontSize: '0.9rem',
                  }}
                >
                  图片规格: {carouselItems[currentSlide].image}
                </Typography>
              </Box>

              {/* 轮播指示器 */}
              <Box
                sx={{
                  position: 'absolute',
                  bottom: 16,
                  left: '50%',
                  transform: 'translateX(-50%)',
                  display: 'flex',
                  gap: 1,
                }}
              >
                {carouselItems.map((_, index) => (
                  <Box
                    key={index}
                    onClick={e => {
                      e.stopPropagation();
                      setCurrentSlide(index);
                    }}
                    sx={{
                      width: 8,
                      height: 8,
                      borderRadius: '50%',
                      bgcolor:
                        index === currentSlide
                          ? '#1976d2'
                          : 'rgba(255,255,255,0.5)',
                      cursor: 'pointer',
                    }}
                  />
                ))}
              </Box>
            </Paper>
          </Grid>

          {/* 2. 英雄榜 - 占5/1 */}
          <Grid item xs={12} md={4}>
            <Paper
              sx={{
                height: 350,
                borderRadius: 2,
                p: 2,
                bgcolor: '#ffffff',
              }}
            >
              <Typography
                variant='h6'
                sx={{
                  color: '#333333',
                  fontWeight: 'bold',
                  mb: 2,
                  display: 'flex',
                  alignItems: 'center',
                }}
              >
                <EmojiEvents sx={{ color: '#ffd700', mr: 1 }} />
                英雄榜
              </Typography>

              <Stack spacing={2}>
                {heroRanking.map(hero => (
                  <Box
                    key={hero.rank}
                    sx={{
                      display: 'flex',
                      alignItems: 'center',
                      p: 1.5,
                      borderRadius: 1,
                      bgcolor:
                        hero.rank === 1
                          ? '#fff8e1'
                          : hero.rank === 2
                            ? '#f3e5f5'
                            : '#e8f5e8',
                      border: `1px solid ${hero.rank === 1 ? '#ffd700' : hero.rank === 2 ? '#c0c0c0' : '#cd7f32'}`,
                    }}
                  >
                    <Typography
                      sx={{
                        color:
                          hero.rank === 1
                            ? '#ffd700'
                            : hero.rank === 2
                              ? '#c0c0c0'
                              : '#cd7f32',
                        fontWeight: 'bold',
                        fontSize: '1.2rem',
                        mr: 2,
                        minWidth: 20,
                      }}
                    >
                      {hero.rank}
                    </Typography>

                    <Avatar
                      sx={{
                        width: 32,
                        height: 32,
                        mr: 1.5,
                        bgcolor: '#2563EB',
                        fontSize: '0.9rem',
                      }}
                    >
                      {hero.name.charAt(0)}
                    </Avatar>

                    <Box sx={{ flexGrow: 1 }}>
                      <Typography
                        variant='body2'
                        sx={{
                          fontWeight: 'bold',
                          color: '#333333',
                          fontSize: '0.85rem',
                        }}
                      >
                        {hero.name}
                      </Typography>
                      <Stack direction='row' spacing={1} sx={{ mt: 0.5 }}>
                        <Typography
                          variant='caption'
                          sx={{ color: '#666666', fontSize: '0.7rem' }}
                        >
                          下载: {hero.downloads.toLocaleString()}
                        </Typography>
                        <Typography
                          variant='caption'
                          sx={{ color: '#666666', fontSize: '0.7rem' }}
                        >
                          作品: {hero.works}
                        </Typography>
                        <Typography
                          variant='caption'
                          sx={{ color: '#666666', fontSize: '0.7rem' }}
                        >
                          点赞: {hero.likes.toLocaleString()}
                        </Typography>
                      </Stack>
                    </Box>
                  </Box>
                ))}
              </Stack>
            </Paper>
          </Grid>
        </Grid>
      </Container>

      {/* H3 精选区 - 高300px */}
      <Container maxWidth='xl' sx={{ mt: 3 }}>
        <Typography
          variant='h5'
          sx={{
            color: '#333333',
            fontWeight: 'bold',
            mb: 3,
            display: 'flex',
            alignItems: 'center',
          }}
        >
          <Star sx={{ color: '#ffd700', mr: 1 }} />
          精选作品
        </Typography>

        <Grid container spacing={3} sx={{ height: 300 }}>
          {featuredWorks.map(work => (
            <Grid item xs={12} md={4} key={work.id}>
              <Card
                sx={{
                  height: 300,
                  cursor: 'pointer',
                  transition: 'all 0.3s ease',
                  '&:hover': {
                    transform: 'translateY(-4px)',
                    boxShadow: 4,
                  },
                }}
                onClick={() => navigate(`/resource/${work.id}`)}
              >
                <CardMedia
                  component='div'
                  sx={{
                    height: 180,
                    bgcolor: '#f5f5f5',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    border: '2px dashed #ddd',
                  }}
                >
                  <Typography
                    variant='body2'
                    sx={{
                      color: '#999999',
                      textAlign: 'center',
                    }}
                  >
                    {work.image}
                  </Typography>
                </CardMedia>

                <CardContent sx={{ p: 2 }}>
                  <Typography
                    variant='h6'
                    sx={{
                      fontWeight: 'bold',
                      color: '#333333',
                      fontSize: '1rem',
                      mb: 1,
                      overflow: 'hidden',
                      textOverflow: 'ellipsis',
                      whiteSpace: 'nowrap',
                    }}
                  >
                    {work.title}
                  </Typography>

                  <Typography
                    variant='body2'
                    sx={{
                      color: '#666666',
                      mb: 2,
                      fontSize: '0.85rem',
                    }}
                  >
                    by {work.creator}
                  </Typography>

                  <Stack
                    direction='row'
                    justifyContent='space-between'
                    alignItems='center'
                  >
                    <Stack direction='row' spacing={2}>
                      <Box
                        sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}
                      >
                        <Download fontSize='small' sx={{ color: '#666666' }} />
                        <Typography variant='caption' sx={{ color: '#666666' }}>
                          {work.downloads.toLocaleString()}
                        </Typography>
                      </Box>
                      <Box
                        sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}
                      >
                        <Favorite fontSize='small' sx={{ color: '#e91e63' }} />
                        <Typography variant='caption' sx={{ color: '#666666' }}>
                          {work.likes}
                        </Typography>
                      </Box>
                    </Stack>

                    <Typography
                      variant='h6'
                      sx={{
                        color: '#2563EB',
                        fontWeight: 'bold',
                        fontSize: '1.1rem',
                      }}
                    >
                      ${work.price}
                    </Typography>
                  </Stack>
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>
      </Container>

      {/* H4 资源展示区 - 瀑布流 */}
      <Container maxWidth='xl' sx={{ mt: 4, mb: 4 }}>
        <Typography
          variant='h5'
          sx={{
            color: '#333333',
            fontWeight: 'bold',
            mb: 3,
          }}
        >
          最新资源
        </Typography>

        <Grid container spacing={2}>
          {/* 瀑布流布局 - 使用不同高度模拟 */}
          <Grid item xs={12} sm={6} md={4} lg={3}>
            <Card
              sx={{
                cursor: 'pointer',
                transition: 'all 0.3s ease',
                '&:hover': {
                  transform: 'translateY(-2px)',
                  boxShadow: 3,
                },
              }}
              onClick={() => navigate('/resource/1')}
            >
              <CardMedia
                component='div'
                sx={{
                  height: 200,
                  bgcolor: '#f0f8ff',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  border: '1px dashed #ccc',
                }}
              >
                <Typography variant='body2' sx={{ color: '#999999' }}>
                  300x200 资源1
                </Typography>
              </CardMedia>
              <CardContent sx={{ p: 1.5 }}>
                <Typography
                  variant='body1'
                  sx={{ fontWeight: 'bold', fontSize: '0.9rem', mb: 0.5 }}
                >
                  AI人像生成模型
                </Typography>
                <Typography
                  variant='body2'
                  sx={{ color: '#666666', fontSize: '0.8rem', mb: 1 }}
                >
                  专业创作者
                </Typography>
                <Stack
                  direction='row'
                  justifyContent='space-between'
                  alignItems='center'
                >
                  <Stack direction='row' spacing={1}>
                    <Typography variant='caption' sx={{ color: '#666666' }}>
                      1560↓
                    </Typography>
                    <Typography variant='caption' sx={{ color: '#e91e63' }}>
                      234♥
                    </Typography>
                  </Stack>
                  <Typography
                    variant='body2'
                    sx={{ color: '#2563EB', fontWeight: 'bold' }}
                  >
                    $29.99
                  </Typography>
                </Stack>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={4} lg={3}>
            <Card
              sx={{
                cursor: 'pointer',
                transition: 'all 0.3s ease',
                '&:hover': {
                  transform: 'translateY(-2px)',
                  boxShadow: 3,
                },
              }}
              onClick={() => navigate('/resource/2')}
            >
              <CardMedia
                component='div'
                sx={{
                  height: 250,
                  bgcolor: '#fff8f0',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  border: '1px dashed #ccc',
                }}
              >
                <Typography variant='body2' sx={{ color: '#999999' }}>
                  300x250 资源2
                </Typography>
              </CardMedia>
              <CardContent sx={{ p: 1.5 }}>
                <Typography
                  variant='body1'
                  sx={{ fontWeight: 'bold', fontSize: '0.9rem', mb: 0.5 }}
                >
                  ComfyUI工作流
                </Typography>
                <Typography
                  variant='body2'
                  sx={{ color: '#666666', fontSize: '0.8rem', mb: 1 }}
                >
                  AI工作室
                </Typography>
                <Stack
                  direction='row'
                  justifyContent='space-between'
                  alignItems='center'
                >
                  <Stack direction='row' spacing={1}>
                    <Typography variant='caption' sx={{ color: '#666666' }}>
                      2340↓
                    </Typography>
                    <Typography variant='caption' sx={{ color: '#e91e63' }}>
                      456♥
                    </Typography>
                  </Stack>
                  <Typography
                    variant='body2'
                    sx={{ color: '#2563EB', fontWeight: 'bold' }}
                  >
                    $19.99
                  </Typography>
                </Stack>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={4} lg={3}>
            <Card
              sx={{
                cursor: 'pointer',
                transition: 'all 0.3s ease',
                '&:hover': {
                  transform: 'translateY(-2px)',
                  boxShadow: 3,
                },
              }}
              onClick={() => navigate('/resource/3')}
            >
              <CardMedia
                component='div'
                sx={{
                  height: 180,
                  bgcolor: '#f0fff0',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  border: '1px dashed #ccc',
                }}
              >
                <Typography variant='body2' sx={{ color: '#999999' }}>
                  300x180 资源3
                </Typography>
              </CardMedia>
              <CardContent sx={{ p: 1.5 }}>
                <Typography
                  variant='body1'
                  sx={{ fontWeight: 'bold', fontSize: '0.9rem', mb: 0.5 }}
                >
                  商业摄影提示词
                </Typography>
                <Typography
                  variant='body2'
                  sx={{ color: '#666666', fontSize: '0.8rem', mb: 1 }}
                >
                  摄影大师
                </Typography>
                <Stack
                  direction='row'
                  justifyContent='space-between'
                  alignItems='center'
                >
                  <Stack direction='row' spacing={1}>
                    <Typography variant='caption' sx={{ color: '#666666' }}>
                      890↓
                    </Typography>
                    <Typography variant='caption' sx={{ color: '#e91e63' }}>
                      123♥
                    </Typography>
                  </Stack>
                  <Typography
                    variant='body2'
                    sx={{ color: '#2563EB', fontWeight: 'bold' }}
                  >
                    $9.99
                  </Typography>
                </Stack>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={4} lg={3}>
            <Card
              sx={{
                cursor: 'pointer',
                transition: 'all 0.3s ease',
                '&:hover': {
                  transform: 'translateY(-2px)',
                  boxShadow: 3,
                },
              }}
              onClick={() => navigate('/resource/4')}
            >
              <CardMedia
                component='div'
                sx={{
                  height: 220,
                  bgcolor: '#fff0f8',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  border: '1px dashed #ccc',
                }}
              >
                <Typography variant='body2' sx={{ color: '#999999' }}>
                  300x220 资源4
                </Typography>
              </CardMedia>
              <CardContent sx={{ p: 1.5 }}>
                <Typography
                  variant='body1'
                  sx={{ fontWeight: 'bold', fontSize: '0.9rem', mb: 0.5 }}
                >
                  动漫风格LoRA
                </Typography>
                <Typography
                  variant='body2'
                  sx={{ color: '#666666', fontSize: '0.8rem', mb: 1 }}
                >
                  动漫创作者
                </Typography>
                <Stack
                  direction='row'
                  justifyContent='space-between'
                  alignItems='center'
                >
                  <Stack direction='row' spacing={1}>
                    <Typography variant='caption' sx={{ color: '#666666' }}>
                      1890↓
                    </Typography>
                    <Typography variant='caption' sx={{ color: '#e91e63' }}>
                      345♥
                    </Typography>
                  </Stack>
                  <Typography
                    variant='body2'
                    sx={{ color: '#2563EB', fontWeight: 'bold' }}
                  >
                    $24.99
                  </Typography>
                </Stack>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </Container>

      {/* H5 页脚 - 高260px */}
      <Box
        component='footer'
        sx={{
          bgcolor: '#2c3e50',
          color: '#ffffff',
          height: 260,
          mt: 4,
        }}
      >
        <Container maxWidth='xl' sx={{ height: '100%', py: 3 }}>
          <Grid container spacing={4} sx={{ height: '100%' }}>
            {/* 品牌信息 */}
            <Grid item xs={12} md={3}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <AutoAwesome sx={{ fontSize: 28, color: '#3498db', mr: 1 }} />
                <Typography
                  variant='h6'
                  sx={{ fontWeight: 'bold', color: '#ffffff' }}
                >
                  AIGC Hub
                </Typography>
              </Box>

              <Typography
                variant='body2'
                sx={{ mb: 3, lineHeight: 1.6, color: '#bdc3c7' }}
              >
                全球领先的AI创作者服务平台，为AI创作者提供资源交易、技术分享和商业变现的综合服务。
              </Typography>

              <Stack direction='row' spacing={1}>
                <IconButton
                  sx={{
                    color: '#3498db',
                    '&:hover': { bgcolor: 'rgba(52, 152, 219, 0.1)' },
                  }}
                >
                  <Share />
                </IconButton>
                <IconButton
                  sx={{
                    color: '#3498db',
                    '&:hover': { bgcolor: 'rgba(52, 152, 219, 0.1)' },
                  }}
                >
                  <Language />
                </IconButton>
                <IconButton
                  sx={{
                    color: '#3498db',
                    '&:hover': { bgcolor: 'rgba(52, 152, 219, 0.1)' },
                  }}
                >
                  <Star />
                </IconButton>
              </Stack>
            </Grid>

            {/* 快速链接 */}
            <Grid item xs={12} sm={6} md={2}>
              <Typography
                variant='h6'
                sx={{ fontWeight: 'bold', mb: 2, color: '#ffffff' }}
              >
                平台
              </Typography>
              <Stack spacing={1}>
                <Button
                  variant='text'
                  sx={{
                    color: '#bdc3c7',
                    justifyContent: 'flex-start',
                    p: 0,
                    '&:hover': { color: '#3498db' },
                  }}
                  onClick={() => navigate('/marketplace')}
                >
                  资源市场
                </Button>
                <Button
                  variant='text'
                  sx={{
                    color: '#bdc3c7',
                    justifyContent: 'flex-start',
                    p: 0,
                    '&:hover': { color: '#3498db' },
                  }}
                  onClick={() => navigate('/creators')}
                >
                  创作者中心
                </Button>
                <Button
                  variant='text'
                  sx={{
                    color: '#bdc3c7',
                    justifyContent: 'flex-start',
                    p: 0,
                    '&:hover': { color: '#3498db' },
                  }}
                  onClick={() => navigate('/upload')}
                >
                  上传资源
                </Button>
                <Button
                  variant='text'
                  sx={{
                    color: '#bdc3c7',
                    justifyContent: 'flex-start',
                    p: 0,
                    '&:hover': { color: '#3498db' },
                  }}
                  onClick={() => navigate('/pricing')}
                >
                  定价方案
                </Button>
              </Stack>
            </Grid>

            {/* 资源分类 */}
            <Grid item xs={12} sm={6} md={2}>
              <Typography
                variant='h6'
                sx={{ fontWeight: 'bold', mb: 2, color: '#ffffff' }}
              >
                资源分类
              </Typography>
              <Stack spacing={1}>
                <Button
                  variant='text'
                  sx={{
                    color: '#bdc3c7',
                    justifyContent: 'flex-start',
                    p: 0,
                    '&:hover': { color: '#3498db' },
                  }}
                  onClick={() =>
                    navigate('/marketplace?category=fine_tuned_models')
                  }
                >
                  微调模型
                </Button>
                <Button
                  variant='text'
                  sx={{
                    color: '#bdc3c7',
                    justifyContent: 'flex-start',
                    p: 0,
                    '&:hover': { color: '#3498db' },
                  }}
                  onClick={() => navigate('/marketplace?category=lora')}
                >
                  LoRA
                </Button>
                <Button
                  variant='text'
                  sx={{
                    color: '#bdc3c7',
                    justifyContent: 'flex-start',
                    p: 0,
                    '&:hover': { color: '#3498db' },
                  }}
                  onClick={() => navigate('/marketplace?category=workflows')}
                >
                  工作流
                </Button>
                <Button
                  variant='text'
                  sx={{
                    color: '#bdc3c7',
                    justifyContent: 'flex-start',
                    p: 0,
                    '&:hover': { color: '#3498db' },
                  }}
                  onClick={() => navigate('/marketplace?category=prompts')}
                >
                  提示词
                </Button>
                <Button
                  variant='text'
                  sx={{
                    color: '#bdc3c7',
                    justifyContent: 'flex-start',
                    p: 0,
                    '&:hover': { color: '#3498db' },
                  }}
                  onClick={() => navigate('/marketplace?category=tools')}
                >
                  工具
                </Button>
              </Stack>
            </Grid>

            {/* 支持与帮助 */}
            <Grid item xs={12} sm={6} md={2}>
              <Typography
                variant='h6'
                sx={{ fontWeight: 'bold', mb: 2, color: '#ffffff' }}
              >
                支持
              </Typography>
              <Stack spacing={1}>
                <Button
                  variant='text'
                  sx={{
                    color: '#bdc3c7',
                    justifyContent: 'flex-start',
                    p: 0,
                    '&:hover': { color: '#3498db' },
                  }}
                  onClick={() => navigate('/docs')}
                >
                  帮助文档
                </Button>
                <Button
                  variant='text'
                  sx={{
                    color: '#bdc3c7',
                    justifyContent: 'flex-start',
                    p: 0,
                    '&:hover': { color: '#3498db' },
                  }}
                  onClick={() => navigate('/api-docs')}
                >
                  API文档
                </Button>
                <Button
                  variant='text'
                  sx={{
                    color: '#bdc3c7',
                    justifyContent: 'flex-start',
                    p: 0,
                    '&:hover': { color: '#3498db' },
                  }}
                  onClick={() => navigate('/contact')}
                >
                  联系我们
                </Button>
                <Button
                  variant='text'
                  sx={{
                    color: '#bdc3c7',
                    justifyContent: 'flex-start',
                    p: 0,
                    '&:hover': { color: '#3498db' },
                  }}
                  onClick={() => navigate('/support')}
                >
                  技术支持
                </Button>
              </Stack>
            </Grid>

            {/* 联系信息 */}
            <Grid item xs={12} sm={6} md={3}>
              <Typography
                variant='h6'
                sx={{ fontWeight: 'bold', mb: 2, color: '#ffffff' }}
              >
                联系我们
              </Typography>
              <Stack spacing={1.5}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <Typography variant='body2' sx={{ color: '#bdc3c7' }}>
                    📧 <EMAIL>
                  </Typography>
                </Box>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <Typography variant='body2' sx={{ color: '#bdc3c7' }}>
                    📞 +86 400-123-4567
                  </Typography>
                </Box>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <Typography variant='body2' sx={{ color: '#bdc3c7' }}>
                    📍 北京市朝阳区
                  </Typography>
                </Box>
              </Stack>
            </Grid>
          </Grid>

          {/* 底部版权信息 */}
          <Box
            sx={{
              borderTop: '1px solid #34495e',
              pt: 2,
              mt: 2,
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              flexWrap: 'wrap',
              gap: 2,
            }}
          >
            <Typography variant='body2' sx={{ color: '#95a5a6' }}>
              © 2024 AIGC Service Hub. 保留所有权利。
            </Typography>

            <Stack direction='row' spacing={3}>
              <Button
                variant='text'
                sx={{
                  color: '#95a5a6',
                  p: 0,
                  fontSize: '0.875rem',
                  '&:hover': { color: '#3498db' },
                }}
                onClick={() => navigate('/privacy')}
              >
                隐私政策
              </Button>
              <Button
                variant='text'
                sx={{
                  color: '#95a5a6',
                  p: 0,
                  fontSize: '0.875rem',
                  '&:hover': { color: '#3498db' },
                }}
                onClick={() => navigate('/terms')}
              >
                服务条款
              </Button>
              <Button
                variant='text'
                sx={{
                  color: '#95a5a6',
                  p: 0,
                  fontSize: '0.875rem',
                  '&:hover': { color: '#3498db' },
                }}
                onClick={() => navigate('/cookies')}
              >
                Cookie政策
              </Button>
            </Stack>
          </Box>
        </Container>
      </Box>
    </Box>
  );
};

export default NewHomePage;
