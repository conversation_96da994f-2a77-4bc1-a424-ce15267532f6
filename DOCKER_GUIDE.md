# AIGC Service Hub Docker 容器化指南

## 📋 容器化概览

本指南详细介绍了AIGC Service Hub项目的Docker容器化配置，包括开发环境、生产环境的完整部署方案。

### 🛠 技术栈

- **容器化**: Docker + Docker Compose
- **Web服务器**: <PERSON>inx (生产环境反向代理)
- **数据库**: PostgreSQL 14
- **缓存**: Redis 7
- **监控**: Prometheus + Grafana (可选)

## 📁 Docker配置文件结构

```
├── frontend/
│   ├── Dockerfile              # 前端生产环境镜像
│   ├── Dockerfile.dev          # 前端开发环境镜像
│   ├── nginx.conf              # 前端Nginx配置
│   └── .dockerignore           # 前端Docker忽略文件
├── backend/
│   ├── Dockerfile              # 后端镜像
│   └── .dockerignore           # 后端Docker忽略文件
├── nginx/
│   └── nginx.prod.conf         # 生产环境Nginx配置
├── redis/
│   └── redis.conf              # Redis生产环境配置
├── monitoring/
│   └── prometheus.yml          # Prometheus监控配置
├── scripts/
│   ├── deploy.sh               # Linux/Mac部署脚本
│   └── deploy.ps1              # Windows部署脚本
├── docker-compose.dev.yml     # 开发环境编排
├── docker-compose.prod.yml    # 生产环境编排
└── .env.docker                # 环境变量模板
```

## 🚀 快速开始

### 1. 环境准备

**安装Docker和Docker Compose**

```bash
# Windows: 安装 Docker Desktop
# macOS: 安装 Docker Desktop
# Linux: 安装 Docker Engine + Docker Compose
```

**配置环境变量**

```bash
# 复制环境变量模板
cp .env.docker .env

# 编辑环境变量文件
# 填入真实的数据库密码、JWT密钥、PayPal配置等
```

### 2. 开发环境部署

**使用部署脚本 (推荐)**

```bash
# Linux/Mac
chmod +x scripts/deploy.sh
./scripts/deploy.sh start dev

# Windows PowerShell
.\scripts\deploy.ps1 -Command start -Environment dev
```

**手动部署**

```bash
# 构建并启动开发环境
docker-compose -f docker-compose.dev.yml up -d

# 运行数据库迁移
docker-compose -f docker-compose.dev.yml exec backend npm run migrate

# 创建测试数据
docker-compose -f docker-compose.dev.yml exec backend npm run seed
```

### 3. 生产环境部署

**使用部署脚本 (推荐)**

```bash
# Linux/Mac
./scripts/deploy.sh build prod
./scripts/deploy.sh start prod

# Windows PowerShell
.\scripts\deploy.ps1 -Command build -Environment prod
.\scripts\deploy.ps1 -Command start -Environment prod
```

**手动部署**

```bash
# 构建生产镜像
docker-compose -f docker-compose.prod.yml build

# 启动生产环境
docker-compose -f docker-compose.prod.yml up -d

# 运行数据库迁移
docker-compose -f docker-compose.prod.yml exec backend npm run migrate
```

## 🔧 服务配置详解

### 前端服务 (Frontend)

**开发环境**

- 基于 `node:18-alpine`
- 热重载开发服务器
- 端口: 5173
- 卷挂载: 实时代码同步

**生产环境**

- 多阶段构建: 构建阶段 + Nginx运行阶段
- 静态文件服务
- Gzip压缩
- 安全头配置

### 后端服务 (Backend)

**特性**

- 基于 `node:18-alpine`
- 多阶段构建优化
- 非root用户运行
- 健康检查
- 日志卷挂载

**环境变量**

- 数据库连接
- JWT配置
- PayPal配置
- AWS配置

### 数据库服务 (PostgreSQL)

**配置**

- PostgreSQL 14-alpine
- 数据持久化
- 健康检查
- 初始化脚本支持

**数据卷**

- 开发环境: `postgres_dev_data`
- 生产环境: `postgres_prod_data`

### 缓存服务 (Redis)

**配置**

- Redis 7-alpine
- AOF持久化
- 内存优化配置
- 安全配置

### 反向代理 (Nginx)

**生产环境特性**

- SSL/TLS终止
- 负载均衡
- 静态文件缓存
- API限流
- 安全头

## 📊 监控配置

### Prometheus监控

**监控目标**

- 应用指标 (后端API)
- 系统指标 (Node Exporter)
- 数据库指标 (PostgreSQL Exporter)
- Redis指标 (Redis Exporter)
- Nginx指标

**启动监控**

```bash
# 启动包含监控的生产环境
docker-compose -f docker-compose.prod.yml --profile monitoring up -d
```

### Grafana仪表板

**访问地址**: http://localhost:3001 **默认账户**: admin/admin (可通过环境变量配置)

## 🛡️ 安全配置

### 容器安全

**最佳实践**

- 非root用户运行
- 最小化镜像
- 安全扫描
- 资源限制

**网络安全**

- 内部网络隔离
- 端口最小化暴露
- SSL/TLS加密

### 数据安全

**数据库**

- 强密码策略
- 连接加密
- 定期备份

**应用**

- JWT密钥管理
- 环境变量保护
- 日志脱敏

## 🔄 运维操作

### 常用命令

```bash
# 查看服务状态
./scripts/deploy.sh status dev

# 查看日志
./scripts/deploy.sh logs dev backend

# 重启服务
./scripts/deploy.sh restart prod

# 健康检查
./scripts/deploy.sh health prod

# 数据库备份
./scripts/deploy.sh backup prod

# 清理资源
./scripts/deploy.sh cleanup
```

### 数据库管理

**pgAdmin访问** (开发环境)

- 地址: http://localhost:8080
- 账户: <EMAIL>
- 密码: admin123

**数据库操作**

```bash
# 进入数据库容器
docker-compose -f docker-compose.dev.yml exec postgres psql -U postgres -d aigc_service_hub_dev

# 备份数据库
docker-compose -f docker-compose.prod.yml exec postgres pg_dump -U postgres aigc_service_hub > backup.sql

# 恢复数据库
docker-compose -f docker-compose.prod.yml exec -T postgres psql -U postgres aigc_service_hub < backup.sql
```

### 日志管理

**日志位置**

- 应用日志: `/app/logs` (容器内)
- Nginx日志: `/var/log/nginx` (容器内)
- 系统日志: Docker logs

**日志查看**

```bash
# 实时日志
docker-compose -f docker-compose.prod.yml logs -f backend

# 历史日志
docker logs aigc-backend-prod

# 日志文件
docker-compose -f docker-compose.prod.yml exec backend ls -la /app/logs
```

## 🚨 故障排除

### 常见问题

**1. 容器启动失败**

```bash
# 检查容器状态
docker-compose -f docker-compose.dev.yml ps

# 查看错误日志
docker-compose -f docker-compose.dev.yml logs backend
```

**2. 数据库连接失败**

```bash
# 检查数据库健康状态
docker-compose -f docker-compose.dev.yml exec postgres pg_isready -U postgres

# 检查网络连接
docker network ls
docker network inspect aigc_aigc-network
```

**3. 端口冲突**

```bash
# 检查端口占用
netstat -tulpn | grep :5432
lsof -i :3000

# 修改端口映射
# 编辑 docker-compose.yml 文件中的 ports 配置
```

**4. 磁盘空间不足**

```bash
# 清理Docker资源
docker system prune -a

# 清理数据卷
docker volume prune

# 查看磁盘使用
docker system df
```

### 性能优化

**容器资源限制**

```yaml
deploy:
  resources:
    limits:
      cpus: '1.0'
      memory: 1G
    reservations:
      cpus: '0.5'
      memory: 512M
```

**数据库优化**

- 调整PostgreSQL配置
- 优化查询索引
- 定期VACUUM

**缓存优化**

- Redis内存配置
- 缓存策略调整
- 过期时间设置

## 📈 扩展部署

### 水平扩展

**多实例部署**

```yaml
backend:
  deploy:
    replicas: 3
  # ... 其他配置
```

**负载均衡**

```nginx
upstream backend {
    server backend_1:3000;
    server backend_2:3000;
    server backend_3:3000;
}
```

### 云部署

**AWS ECS**

- 使用ECS任务定义
- ALB负载均衡
- RDS数据库

**Kubernetes**

- 转换为K8s部署文件
- 使用Helm Charts
- 自动扩缩容

## 📝 维护清单

### 定期维护

- [ ] 更新基础镜像
- [ ] 安全补丁更新
- [ ] 数据库备份验证
- [ ] 日志轮转配置
- [ ] 监控告警测试
- [ ] 性能指标检查
- [ ] 容器资源使用分析

### 安全检查

- [ ] 容器安全扫描
- [ ] 依赖漏洞检查
- [ ] 访问权限审计
- [ ] 网络安全配置
- [ ] 数据加密验证

---

**文档版本**: 1.0  
**最后更新**: 2025年7月17日  
**维护团队**: AIGC Service Hub 开发团队
