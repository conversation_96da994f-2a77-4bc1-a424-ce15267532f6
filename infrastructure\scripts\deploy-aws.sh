#!/bin/bash

# AIGC Service Hub AWS Infrastructure Deployment Script

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查必要的工具
check_prerequisites() {
    log_info "检查必要的工具..."
    
    # 检查AWS CLI
    if ! command -v aws &> /dev/null; then
        log_error "AWS CLI 未安装，请先安装 AWS CLI"
        exit 1
    fi
    
    # 检查Terraform
    if ! command -v terraform &> /dev/null; then
        log_error "Terraform 未安装，请先安装 Terraform"
        exit 1
    fi
    
    # 检查jq
    if ! command -v jq &> /dev/null; then
        log_warning "jq 未安装，建议安装以便更好地处理JSON输出"
    fi
    
    log_success "工具检查完成"
}

# 检查AWS凭证
check_aws_credentials() {
    log_info "检查AWS凭证..."
    
    if ! aws sts get-caller-identity &> /dev/null; then
        log_error "AWS凭证未配置或无效，请运行 'aws configure' 配置凭证"
        exit 1
    fi
    
    local account_id=$(aws sts get-caller-identity --query Account --output text)
    local region=$(aws configure get region)
    
    log_success "AWS凭证有效 - 账户: $account_id, 区域: $region"
}

# 初始化Terraform后端
init_terraform_backend() {
    log_info "初始化Terraform后端..."
    
    local bucket_name="aigc-service-hub-terraform-state"
    local table_name="aigc-terraform-locks"
    local region=${AWS_REGION:-us-west-2}
    
    # 创建S3存储桶（如果不存在）
    if ! aws s3api head-bucket --bucket "$bucket_name" 2>/dev/null; then
        log_info "创建Terraform状态存储桶: $bucket_name"
        aws s3api create-bucket \
            --bucket "$bucket_name" \
            --region "$region" \
            --create-bucket-configuration LocationConstraint="$region"
        
        # 启用版本控制
        aws s3api put-bucket-versioning \
            --bucket "$bucket_name" \
            --versioning-configuration Status=Enabled
        
        # 启用服务器端加密
        aws s3api put-bucket-encryption \
            --bucket "$bucket_name" \
            --server-side-encryption-configuration '{
                "Rules": [{
                    "ApplyServerSideEncryptionByDefault": {
                        "SSEAlgorithm": "AES256"
                    }
                }]
            }'
    fi
    
    # 创建DynamoDB表（如果不存在）
    if ! aws dynamodb describe-table --table-name "$table_name" &>/dev/null; then
        log_info "创建Terraform锁定表: $table_name"
        aws dynamodb create-table \
            --table-name "$table_name" \
            --attribute-definitions AttributeName=LockID,AttributeType=S \
            --key-schema AttributeName=LockID,KeyType=HASH \
            --provisioned-throughput ReadCapacityUnits=5,WriteCapacityUnits=5
        
        # 等待表创建完成
        aws dynamodb wait table-exists --table-name "$table_name"
    fi
    
    log_success "Terraform后端初始化完成"
}

# 初始化Terraform
init_terraform() {
    log_info "初始化Terraform..."
    
    cd infrastructure/terraform
    
    terraform init
    
    log_success "Terraform初始化完成"
}

# 验证Terraform配置
validate_terraform() {
    log_info "验证Terraform配置..."
    
    terraform validate
    
    log_success "Terraform配置验证通过"
}

# 规划Terraform部署
plan_terraform() {
    log_info "规划Terraform部署..."
    
    local var_file=${1:-terraform.tfvars}
    
    if [ ! -f "$var_file" ]; then
        log_error "变量文件 $var_file 不存在，请从 terraform.tfvars.example 复制并配置"
        exit 1
    fi
    
    terraform plan -var-file="$var_file" -out=tfplan
    
    log_success "Terraform规划完成"
}

# 应用Terraform配置
apply_terraform() {
    log_info "应用Terraform配置..."
    
    terraform apply tfplan
    
    log_success "Terraform应用完成"
}

# 销毁基础设施
destroy_terraform() {
    log_warning "准备销毁基础设施..."
    
    local var_file=${1:-terraform.tfvars}
    
    read -p "确定要销毁所有基础设施吗？这个操作不可逆！(yes/no): " confirm
    if [ "$confirm" != "yes" ]; then
        log_info "操作已取消"
        exit 0
    fi
    
    terraform destroy -var-file="$var_file" -auto-approve
    
    log_success "基础设施销毁完成"
}

# 获取输出信息
get_outputs() {
    log_info "获取Terraform输出..."
    
    terraform output -json > outputs.json
    
    if command -v jq &> /dev/null; then
        echo
        log_info "应用程序URL:"
        terraform output -raw application_url
        echo
        
        log_info "API URL:"
        terraform output -raw api_url
        echo
        
        log_info "CloudFront域名:"
        terraform output -raw cloudfront_domain_name
        echo
    else
        terraform output
    fi
    
    log_success "输出信息已保存到 outputs.json"
}

# 检查基础设施状态
check_status() {
    log_info "检查基础设施状态..."
    
    # 检查ECS服务状态
    local cluster_name=$(terraform output -raw ecs_cluster_name 2>/dev/null || echo "")
    if [ -n "$cluster_name" ]; then
        log_info "ECS集群状态:"
        aws ecs describe-clusters --clusters "$cluster_name" --query 'clusters[0].status' --output text
        
        local service_name=$(terraform output -raw ecs_service_name 2>/dev/null || echo "")
        if [ -n "$service_name" ]; then
            log_info "ECS服务状态:"
            aws ecs describe-services --cluster "$cluster_name" --services "$service_name" \
                --query 'services[0].{Status:status,Running:runningCount,Desired:desiredCount}' --output table
        fi
    fi
    
    # 检查RDS状态
    local db_endpoint=$(terraform output -raw db_endpoint 2>/dev/null || echo "")
    if [ -n "$db_endpoint" ]; then
        local db_identifier=$(echo "$db_endpoint" | cut -d'.' -f1)
        log_info "RDS实例状态:"
        aws rds describe-db-instances --db-instance-identifier "$db_identifier" \
            --query 'DBInstances[0].DBInstanceStatus' --output text 2>/dev/null || echo "未找到"
    fi
    
    log_success "状态检查完成"
}

# 显示帮助信息
show_help() {
    echo "AIGC Service Hub AWS基础设施部署脚本"
    echo ""
    echo "用法: $0 <命令> [选项]"
    echo ""
    echo "命令:"
    echo "  init                初始化Terraform和AWS后端"
    echo "  plan [var-file]     规划部署（默认使用terraform.tfvars）"
    echo "  apply               应用Terraform配置"
    echo "  destroy [var-file]  销毁基础设施"
    echo "  output              显示输出信息"
    echo "  status              检查基础设施状态"
    echo "  validate            验证Terraform配置"
    echo "  help                显示帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 init                    # 初始化"
    echo "  $0 plan                    # 规划部署"
    echo "  $0 apply                   # 应用配置"
    echo "  $0 plan prod.tfvars        # 使用特定变量文件规划"
    echo "  $0 status                  # 检查状态"
}

# 主函数
main() {
    case $1 in
        "init")
            check_prerequisites
            check_aws_credentials
            init_terraform_backend
            init_terraform
            ;;
        "plan")
            check_prerequisites
            check_aws_credentials
            validate_terraform
            plan_terraform $2
            ;;
        "apply")
            check_prerequisites
            check_aws_credentials
            apply_terraform
            get_outputs
            ;;
        "destroy")
            check_prerequisites
            check_aws_credentials
            destroy_terraform $2
            ;;
        "output")
            get_outputs
            ;;
        "status")
            check_prerequisites
            check_aws_credentials
            check_status
            ;;
        "validate")
            validate_terraform
            ;;
        "help"|"--help"|"-h")
            show_help
            ;;
        *)
            log_error "无效的命令: $1"
            show_help
            exit 1
            ;;
    esac
}

# 检查参数
if [ $# -eq 0 ]; then
    show_help
    exit 1
fi

# 执行主函数
main "$@"
