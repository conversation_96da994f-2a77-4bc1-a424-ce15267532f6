# AIGC Service Hub 代码质量检查脚本 (PowerShell)

# 颜色函数
function WriteColorOutput($ForegroundColor, $message) {
    $fc = $host.UI.RawUI.ForegroundColor
    $host.UI.RawUI.ForegroundColor = $ForegroundColor
    Write-Output $message
    $host.UI.RawUI.ForegroundColor = $fc
}

function LogInfo($message) {
    WriteColorOutput Blue "[INFO] $message"
}

function LogSuccess($message) {
    WriteColorOutput Green "[SUCCESS] $message"
}

function LogWarning($message) {
    WriteColorOutput Yellow "[WARNING] $message"
}

function LogError($message) {
    WriteColorOutput Red "[ERROR] $message"
}

# 检查Node.js版本
function CheckNodeVersion {
    LogInfo "检查Node.js版本..."
    
    try {
        $nodeVersion = node --version
        $nodeVersion = $nodeVersion.TrimStart('v')
        $requiredVersion = [Version]"18.0.0"
        $currentVersion = [Version]$nodeVersion
        
        if ($currentVersion -ge $requiredVersion) {
            LogSuccess "Node.js版本: $nodeVersion (满足要求 >= 18.0.0)"
            return $true
        } else {
            LogError "Node.js版本过低: $nodeVersion (需要 >= 18.0.0)"
            return $false
        }
    } catch {
        LogError "Node.js未安装"
        return $false
    }
}

# 检查npm版本
function CheckNpmVersion {
    LogInfo "检查npm版本..."
    
    try {
        $npmVersion = npm --version
        $requiredVersion = [Version]"8.0.0"
        $currentVersion = [Version]$npmVersion
        
        if ($currentVersion -ge $requiredVersion) {
            LogSuccess "npm版本: $npmVersion (满足要求 >= 8.0.0)"
            return $true
        } else {
            LogError "npm版本过低: $npmVersion (需要 >= 8.0.0)"
            return $false
        }
    } catch {
        LogError "npm未安装"
        return $false
    }
}

# 检查依赖安装
function CheckDependencies {
    LogInfo "检查依赖安装..."
    
    # 检查根目录依赖
    if (-not (Test-Path "node_modules")) {
        LogWarning "根目录依赖未安装，正在安装..."
        npm install
    }
    
    # 检查前端依赖
    if (-not (Test-Path "frontend/node_modules")) {
        LogWarning "前端依赖未安装，正在安装..."
        Set-Location frontend
        npm install
        Set-Location ..
    }
    
    # 检查后端依赖
    if (-not (Test-Path "backend/node_modules")) {
        LogWarning "后端依赖未安装，正在安装..."
        Set-Location backend
        npm install
        Set-Location ..
    }
    
    LogSuccess "依赖检查完成"
    return $true
}

# 代码格式检查
function CheckFormatting {
    LogInfo "检查代码格式..."
    
    try {
        npm run format:check
        LogSuccess "代码格式正确"
        return $true
    } catch {
        LogWarning "代码格式不正确，正在自动修复..."
        npm run format
        LogSuccess "代码格式已修复"
        return $true
    }
}

# ESLint检查
function CheckLinting {
    LogInfo "运行ESLint检查..."
    
    $lintFailed = $false
    
    # 检查前端
    LogInfo "检查前端代码..."
    Set-Location frontend
    try {
        npm run lint
        LogSuccess "前端ESLint检查通过"
    } catch {
        LogError "前端ESLint检查失败"
        $lintFailed = $true
    }
    Set-Location ..
    
    # 检查后端
    LogInfo "检查后端代码..."
    Set-Location backend
    try {
        npm run lint
        LogSuccess "后端ESLint检查通过"
    } catch {
        LogError "后端ESLint检查失败"
        $lintFailed = $true
    }
    Set-Location ..
    
    return -not $lintFailed
}

# TypeScript类型检查
function CheckTypes {
    LogInfo "运行TypeScript类型检查..."
    
    $typeFailed = $false
    
    # 检查前端
    LogInfo "检查前端类型..."
    Set-Location frontend
    try {
        npm run type-check
        LogSuccess "前端类型检查通过"
    } catch {
        LogError "前端类型检查失败"
        $typeFailed = $true
    }
    Set-Location ..
    
    # 检查后端
    LogInfo "检查后端类型..."
    Set-Location backend
    try {
        npm run type-check
        LogSuccess "后端类型检查通过"
    } catch {
        LogError "后端类型检查失败"
        $typeFailed = $true
    }
    Set-Location ..
    
    return -not $typeFailed
}

# 运行测试
function RunTests {
    LogInfo "运行测试..."
    
    $testFailed = $false
    
    # 运行前端测试
    LogInfo "运行前端测试..."
    Set-Location frontend
    try {
        npm run test
        LogSuccess "前端测试通过"
    } catch {
        LogError "前端测试失败"
        $testFailed = $true
    }
    Set-Location ..
    
    # 运行后端测试
    LogInfo "运行后端测试..."
    Set-Location backend
    try {
        npm run test
        LogSuccess "后端测试通过"
    } catch {
        LogError "后端测试失败"
        $testFailed = $true
    }
    Set-Location ..
    
    return -not $testFailed
}

# 检查安全漏洞
function CheckSecurity {
    LogInfo "检查安全漏洞..."
    
    $securityFailed = $false
    
    # 检查前端安全漏洞
    LogInfo "检查前端安全漏洞..."
    Set-Location frontend
    try {
        npm audit --audit-level=high
        LogSuccess "前端安全检查通过"
    } catch {
        LogWarning "前端发现安全漏洞，建议运行 'npm audit fix'"
        $securityFailed = $true
    }
    Set-Location ..
    
    # 检查后端安全漏洞
    LogInfo "检查后端安全漏洞..."
    Set-Location backend
    try {
        npm audit --audit-level=high
        LogSuccess "后端安全检查通过"
    } catch {
        LogWarning "后端发现安全漏洞，建议运行 'npm audit fix'"
        $securityFailed = $true
    }
    Set-Location ..
    
    return -not $securityFailed
}

# 生成报告
function GenerateReport($checksPassed, $totalChecks) {
    LogInfo "生成代码质量报告..."
    
    $reportFile = "code-quality-report.txt"
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    
    $reportContent = @"
AIGC Service Hub 代码质量检查报告
生成时间: $timestamp

检查结果: $checksPassed/$totalChecks 通过

详细检查项目:
✓ Node.js版本检查
✓ npm版本检查
✓ 依赖安装检查
✓ 代码格式检查
✓ ESLint代码质量检查
✓ TypeScript类型检查
✓ 单元测试运行
✓ 安全漏洞检查

建议:
- 定期运行代码质量检查
- 提交前确保所有检查通过
- 及时修复发现的问题
- 保持依赖更新
"@
    
    $reportContent | Out-File -FilePath $reportFile -Encoding UTF8
    LogSuccess "代码质量报告已生成: $reportFile"
}

# 主函数
function Main {
    LogInfo "开始代码质量检查..."
    Write-Output ""
    
    $checksPassed = 0
    $totalChecks = 8
    
    # 运行所有检查
    if (CheckNodeVersion) { $checksPassed++ }
    Write-Output ""
    
    if (CheckNpmVersion) { $checksPassed++ }
    Write-Output ""
    
    if (CheckDependencies) { $checksPassed++ }
    Write-Output ""
    
    if (CheckFormatting) { $checksPassed++ }
    Write-Output ""
    
    if (CheckLinting) { $checksPassed++ }
    Write-Output ""
    
    if (CheckTypes) { $checksPassed++ }
    Write-Output ""
    
    if (RunTests) { $checksPassed++ }
    Write-Output ""
    
    if (CheckSecurity) { $checksPassed++ }
    Write-Output ""
    
    # 生成报告
    GenerateReport $checksPassed $totalChecks
    
    # 输出结果
    LogInfo "代码质量检查完成！"
    Write-Output "通过检查: $checksPassed/$totalChecks"
    
    if ($checksPassed -eq $totalChecks) {
        LogSuccess "所有检查通过！代码质量良好。"
        return 0
    } else {
        LogWarning "部分检查未通过，请修复问题后重新检查。"
        return 1
    }
}

# 运行检查
Main
