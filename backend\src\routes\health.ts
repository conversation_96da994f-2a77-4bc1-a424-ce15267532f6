import { Router, Request, Response } from 'express';
import { healthCheck } from '@database/connection';
import { asyncHandler } from '@middleware/errorHandler';

const router = Router();

// 基础健康检查
router.get(
  '/',
  asyncHandler(async (_req: Request, res: Response) => {
    const dbHealth = await healthCheck();

    const healthStatus = {
      status: 'ok',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      environment: process.env.NODE_ENV,
      version: process.env.APP_VERSION || '1.0.0',
      database: dbHealth,
      memory: {
        used: Math.round(process.memoryUsage().heapUsed / 1024 / 1024),
        total: Math.round(process.memoryUsage().heapTotal / 1024 / 1024),
        external: Math.round(process.memoryUsage().external / 1024 / 1024),
      },
      cpu: process.cpuUsage(),
    };

    // 如果数据库不健康，返回503状态
    if (dbHealth.status !== 'healthy') {
      res.status(503);
    }

    res.json({
      success: true,
      data: healthStatus,
      timestamp: new Date().toISOString(),
    });
  })
);

// 详细健康检查
router.get(
  '/detailed',
  asyncHandler(async (_req: Request, res: Response) => {
    const dbHealth = await healthCheck();

    const detailedHealth = {
      status: 'ok',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      environment: process.env.NODE_ENV,
      version: process.env.APP_VERSION || '1.0.0',
      node_version: process.version,
      platform: process.platform,
      arch: process.arch,
      database: dbHealth,
      memory: process.memoryUsage(),
      cpu: process.cpuUsage(),
      load_average:
        process.platform !== 'win32' ? require('os').loadavg() : null,
      free_memory: require('os').freemem(),
      total_memory: require('os').totalmem(),
      network_interfaces: require('os').networkInterfaces(),
    };

    if (dbHealth.status !== 'healthy') {
      res.status(503);
    }

    res.json({
      success: true,
      data: detailedHealth,
      timestamp: new Date().toISOString(),
    });
  })
);

// 就绪检查 (用于Kubernetes等容器编排)
router.get(
  '/ready',
  asyncHandler(async (_req: Request, res: Response) => {
    const dbHealth = await healthCheck();

    if (dbHealth.status === 'healthy') {
      res.json({
        success: true,
        message: 'Service is ready',
        timestamp: new Date().toISOString(),
      });
    } else {
      res.status(503).json({
        success: false,
        error: {
          code: 'SERVICE_NOT_READY',
          message: 'Service is not ready',
        },
        timestamp: new Date().toISOString(),
      });
    }
  })
);

// 存活检查 (用于Kubernetes等容器编排)
router.get('/live', (_req: Request, res: Response) => {
  res.json({
    success: true,
    message: 'Service is alive',
    timestamp: new Date().toISOString(),
  });
});

export { router as healthRoutes };
