import { CssBaseline } from '@mui/material';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import { StrictMode } from 'react';
import { createRoot } from 'react-dom/client';
import { Route, BrowserRouter as Router, Routes } from 'react-router-dom';
import MarketplacePage from './pages/MarketplacePage';
import NewHomePage from './pages/NewHomePage';
import SimpleProfilePage from './pages/SimpleProfilePage';
import SimpleResourceManagePage from './pages/SimpleResourceManagePage';

// 创建简单主题
const theme = createTheme({
  palette: {
    primary: {
      main: '#1976d2',
    },
  },
});

// 简化的登录页面组件
function TestLoginPage() {
  return (
    <div style={{ padding: '20px', fontFamily: 'Arial, sans-serif' }}>
      <h1 style={{ color: '#1976d2' }}>登录页面 - 完整测试</h1>
      <p>登录页面正在正常工作！</p>
      <button
        onClick={() => (window.location.href = '/')}
        style={{
          padding: '10px 20px',
          backgroundColor: '#1976d2',
          color: 'white',
          border: 'none',
          borderRadius: '4px',
          cursor: 'pointer',
        }}
      >
        返回首页
      </button>
    </div>
  );
}

createRoot(document.getElementById('root')!).render(
  <StrictMode>
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <Router>
        <Routes>
          <Route path='/' element={<NewHomePage />} />
          <Route path='/marketplace' element={<MarketplacePage />} />
          <Route path='/login' element={<TestLoginPage />} />
          <Route path='/profile' element={<SimpleProfilePage />} />
          <Route path='/resources' element={<SimpleResourceManagePage />} />
        </Routes>
      </Router>
    </ThemeProvider>
  </StrictMode>
);
