import { Request, Response, NextFunction } from 'express';
import { securityConfig } from '@config/index';

// 简单的内存限流器 (生产环境建议使用Redis)
interface RateLimitStore {
  [key: string]: {
    count: number;
    resetTime: number;
  };
}

const store: RateLimitStore = {};

// 清理过期记录
setInterval(() => {
  const now = Date.now();
  Object.keys(store).forEach(key => {
    if (store[key]!.resetTime < now) {
      delete store[key];
    }
  });
}, 60000); // 每分钟清理一次

// 限流中间件
export const rateLimiter = (
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  const key = req.ip || req.connection.remoteAddress || 'unknown';
  const now = Date.now();
  const windowMs = securityConfig.rateLimitWindowMs;
  const maxRequests = securityConfig.rateLimitMaxRequests;

  // 获取或创建限流记录
  if (!store[key] || store[key]!.resetTime < now) {
    store[key] = {
      count: 1,
      resetTime: now + windowMs,
    };
  } else {
    store[key]!.count++;
  }

  const current = store[key]!;
  const remaining = Math.max(0, maxRequests - current.count);
  const resetTime = Math.ceil((current.resetTime - now) / 1000);

  // 设置响应头
  res.set({
    'X-RateLimit-Limit': maxRequests.toString(),
    'X-RateLimit-Remaining': remaining.toString(),
    'X-RateLimit-Reset': resetTime.toString(),
  });

  // 检查是否超过限制
  if (current.count > maxRequests) {
    res.status(429).json({
      success: false,
      error: {
        code: 'RATE_LIMIT_EXCEEDED',
        message: 'Too many requests, please try again later',
        retryAfter: resetTime,
      },
      timestamp: new Date().toISOString(),
    });
    return;
  }

  next();
};

// 特定路由的限流器
export const createRateLimiter = (windowMs: number, maxRequests: number) => {
  const customStore: RateLimitStore = {};

  // 清理过期记录
  setInterval(() => {
    const now = Date.now();
    Object.keys(customStore).forEach(key => {
      if (customStore[key]!.resetTime < now) {
        delete customStore[key];
      }
    });
  }, 60000);

  return (req: Request, res: Response, next: NextFunction): void => {
    const key = req.ip || req.connection.remoteAddress || 'unknown';
    const now = Date.now();

    if (!customStore[key] || customStore[key]!.resetTime < now) {
      customStore[key] = {
        count: 1,
        resetTime: now + windowMs,
      };
    } else {
      customStore[key]!.count++;
    }

    const current = customStore[key]!;
    const remaining = Math.max(0, maxRequests - current.count);
    const resetTime = Math.ceil((current.resetTime - now) / 1000);

    res.set({
      'X-RateLimit-Limit': maxRequests.toString(),
      'X-RateLimit-Remaining': remaining.toString(),
      'X-RateLimit-Reset': resetTime.toString(),
    });

    if (current.count > maxRequests) {
      res.status(429).json({
        success: false,
        error: {
          code: 'RATE_LIMIT_EXCEEDED',
          message: 'Too many requests, please try again later',
          retryAfter: resetTime,
        },
        timestamp: new Date().toISOString(),
      });
      return;
    }

    next();
  };
};

// 严格限流器 (用于敏感操作)
export const strictRateLimiter = createRateLimiter(60000, 5); // 1分钟5次
export const authRateLimiter = createRateLimiter(900000, 10); // 15分钟10次
