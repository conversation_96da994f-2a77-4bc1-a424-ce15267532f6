-- 创建积分交易表
-- 记录所有积分获得、消费、过期等操作

CREATE TABLE points_transactions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    
    -- 积分信息
    points INTEGER NOT NULL,
    type VARCHAR(20) NOT NULL CHECK (type IN ('earned', 'spent', 'expired', 'refunded')),
    source VARCHAR(50) NOT NULL CHECK (source IN (
        'daily_checkin',
        'first_upload',
        'sales_milestone',
        'purchase',
        'refund',
        'admin_adjustment',
        'promotion',
        'referral',
        'expiration'
    )),
    
    -- 关联信息
    related_transaction_id UUID REFERENCES transactions(id),
    related_resource_id UUID REFERENCES resources(id),
    description TEXT,
    
    -- 余额快照
    balance_before INTEGER NOT NULL CHECK (balance_before >= 0),
    balance_after INTEGER NOT NULL CHECK (balance_after >= 0),
    
    -- 过期信息
    expires_at TIMESTAMP,
    
    -- 时间戳
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- 约束
    CONSTRAINT valid_points_change CHECK (
        (type = 'earned' AND points > 0 AND balance_after = balance_before + points) OR
        (type = 'spent' AND points < 0 AND balance_after = balance_before + points) OR
        (type = 'expired' AND points < 0 AND balance_after = balance_before + points) OR
        (type = 'refunded' AND points > 0 AND balance_after = balance_before + points)
    ),
    CONSTRAINT valid_balance_after CHECK (balance_after >= 0)
);

-- 创建索引
CREATE INDEX idx_points_transactions_user_id ON points_transactions(user_id);
CREATE INDEX idx_points_transactions_type ON points_transactions(type);
CREATE INDEX idx_points_transactions_source ON points_transactions(source);
CREATE INDEX idx_points_transactions_created_at ON points_transactions(created_at);
CREATE INDEX idx_points_transactions_expires_at ON points_transactions(expires_at) 
    WHERE expires_at IS NOT NULL;
CREATE INDEX idx_points_transactions_related_transaction_id ON points_transactions(related_transaction_id) 
    WHERE related_transaction_id IS NOT NULL;

-- 创建视图：用户积分余额
CREATE VIEW user_points_balance AS
SELECT 
    user_id,
    SUM(points) as total_points,
    SUM(CASE WHEN expires_at IS NULL OR expires_at > CURRENT_TIMESTAMP THEN points ELSE 0 END) as available_points,
    SUM(CASE WHEN expires_at IS NOT NULL AND expires_at <= CURRENT_TIMESTAMP THEN points ELSE 0 END) as expired_points,
    MAX(created_at) as last_transaction_at
FROM points_transactions 
GROUP BY user_id;

-- 创建视图：积分获得统计
CREATE VIEW points_earning_stats AS
SELECT 
    user_id,
    source,
    COUNT(*) as transaction_count,
    SUM(points) as total_points,
    MAX(created_at) as last_earned_at
FROM points_transactions 
WHERE type = 'earned'
GROUP BY user_id, source;

-- 添加注释
COMMENT ON TABLE points_transactions IS '积分交易记录表，记录所有积分变动';
COMMENT ON COLUMN points_transactions.points IS '积分变动数量（正数为获得，负数为消费）';
COMMENT ON COLUMN points_transactions.type IS '交易类型：earned, spent, expired, refunded';
COMMENT ON COLUMN points_transactions.source IS '积分来源或用途';
COMMENT ON COLUMN points_transactions.balance_before IS '交易前积分余额';
COMMENT ON COLUMN points_transactions.balance_after IS '交易后积分余额';
COMMENT ON COLUMN points_transactions.expires_at IS '积分过期时间（如适用）';

COMMENT ON VIEW user_points_balance IS '用户积分余额视图';
COMMENT ON VIEW points_earning_stats IS '积分获得统计视图';
