-- 创建交易表
-- 记录所有资源购买交易，支持PayPal和积分支付

CREATE TABLE transactions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    buyer_id UUID NOT NULL REFERENCES users(id) ON DELETE RESTRICT,
    seller_id UUID NOT NULL REFERENCES users(id) ON DELETE RESTRICT,
    resource_id UUID NOT NULL REFERENCES resources(id) ON DELETE RESTRICT,
    
    -- 交易金额
    amount_usd DECIMAL(10,2) NOT NULL CHECK (amount_usd >= 0),
    platform_fee DECIMAL(10,2) NOT NULL CHECK (platform_fee >= 0),
    creator_earnings DECIMAL(10,2) NOT NULL CHECK (creator_earnings >= 0),
    
    -- 支付信息
    payment_method VARCHAR(20) NOT NULL CHECK (payment_method IN ('paypal', 'points')),
    paypal_transaction_id VARCHAR(255),
    paypal_order_id VARCHAR(255),
    points_used INTEGER CHECK (points_used >= 0),
    
    -- 交易状态
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN (
        'pending',
        'processing', 
        'completed', 
        'failed', 
        'cancelled',
        'refunded',
        'partially_refunded'
    )),
    
    -- 下载信息
    download_url VARCHAR(500),
    download_expires_at TIMESTAMP,
    download_count INTEGER DEFAULT 0,
    
    -- 退款信息
    refund_amount DECIMAL(10,2) DEFAULT 0.00 CHECK (refund_amount >= 0),
    refund_reason TEXT,
    refunded_at TIMESTAMP,
    
    -- 时间戳
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP,
    
    -- 约束
    CONSTRAINT valid_payment_method CHECK (
        (payment_method = 'paypal' AND paypal_transaction_id IS NOT NULL) OR
        (payment_method = 'points' AND points_used IS NOT NULL AND points_used > 0)
    ),
    CONSTRAINT valid_amounts CHECK (
        amount_usd = platform_fee + creator_earnings
    ),
    CONSTRAINT no_self_purchase CHECK (buyer_id != seller_id),
    CONSTRAINT valid_refund CHECK (refund_amount <= amount_usd)
);

-- 创建索引
CREATE INDEX idx_transactions_buyer_id ON transactions(buyer_id);
CREATE INDEX idx_transactions_seller_id ON transactions(seller_id);
CREATE INDEX idx_transactions_resource_id ON transactions(resource_id);
CREATE INDEX idx_transactions_status ON transactions(status);
CREATE INDEX idx_transactions_payment_method ON transactions(payment_method);
CREATE INDEX idx_transactions_created_at ON transactions(created_at);
CREATE INDEX idx_transactions_completed_at ON transactions(completed_at);
CREATE INDEX idx_transactions_paypal_transaction_id ON transactions(paypal_transaction_id) 
    WHERE paypal_transaction_id IS NOT NULL;

-- 创建更新时间戳触发器
CREATE TRIGGER update_transactions_updated_at 
    BEFORE UPDATE ON transactions 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- 创建完成时间自动设置触发器
CREATE OR REPLACE FUNCTION set_completed_at()
RETURNS TRIGGER AS $$
BEGIN
    -- 当状态变为completed时，设置完成时间
    IF OLD.status != 'completed' AND NEW.status = 'completed' AND NEW.completed_at IS NULL THEN
        NEW.completed_at = CURRENT_TIMESTAMP;
    END IF;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER set_transactions_completed_at 
    BEFORE UPDATE ON transactions 
    FOR EACH ROW 
    EXECUTE FUNCTION set_completed_at();

-- 添加注释
COMMENT ON TABLE transactions IS '交易记录表，支持PayPal和积分支付';
COMMENT ON COLUMN transactions.amount_usd IS '交易总金额（美元）';
COMMENT ON COLUMN transactions.platform_fee IS '平台手续费（美元）';
COMMENT ON COLUMN transactions.creator_earnings IS '创作者收益（美元）';
COMMENT ON COLUMN transactions.payment_method IS '支付方式：paypal 或 points';
COMMENT ON COLUMN transactions.status IS '交易状态';
COMMENT ON COLUMN transactions.download_expires_at IS '下载链接过期时间';
