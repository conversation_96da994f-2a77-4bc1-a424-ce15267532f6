import { query, transaction } from '@/database/connection';
import type {
  PaginatedResponse,
  QueryOptions,
  User,
  UserRegistrationData,
} from '@/types/index';
import bcrypt from 'bcryptjs';
import { v4 as uuidv4 } from 'uuid';

export class UserModel {
  // 创建用户
  static async create(userData: UserRegistrationData): Promise<User> {
    const id = uuidv4();
    const passwordHash = await bcrypt.hash(userData.password, 12);

    const result = await query(
      `
      INSERT INTO users (
        id, email, password_hash, username, display_name, user_type,
        first_name, last_name, phone_number, country,
        company_name, company_registration_number, company_address,
        tax_id, contact_person_name, contact_person_title,
        email_verified, status, language, timezone
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19, $20)
      RETURNING *
    `,
      [
        id,
        userData.email,
        passwordHash,
        userData.username || userData.email.split('@')[0],
        userData.displayName || null,
        userData.userType,
        userData.firstName || null,
        userData.lastName || null,
        userData.phoneNumber || null,
        userData.country || null,
        userData.companyName || null,
        userData.companyRegistrationNumber || null,
        userData.companyAddress || null,
        userData.taxId || null,
        userData.contactPersonName || null,
        userData.contactPersonTitle || null,
        false, // email_verified
        'pending', // status
        userData.language || 'en',
        userData.timezone || 'UTC',
      ]
    );

    return this.mapRowToUser(result.rows[0]);
  }

  // 根据ID查找用户
  static async findById(id: string): Promise<User | null> {
    const result = await query('SELECT * FROM users WHERE id = $1', [id]);
    return result.rows[0] ? this.mapRowToUser(result.rows[0]) : null;
  }

  // 根据邮箱查找用户
  static async findByEmail(email: string): Promise<User | null> {
    const result = await query('SELECT * FROM users WHERE email = $1', [email]);
    return result.rows[0] ? this.mapRowToUser(result.rows[0]) : null;
  }

  // 根据用户名查找用户
  static async findByUsername(username: string): Promise<User | null> {
    const result = await query('SELECT * FROM users WHERE username = $1', [
      username,
    ]);
    return result.rows[0] ? this.mapRowToUser(result.rows[0]) : null;
  }

  // 根据邮箱验证令牌查找用户
  static async findByEmailVerificationToken(
    token: string
  ): Promise<User | null> {
    const result = await query(
      'SELECT * FROM users WHERE email_verification_token = $1',
      [token]
    );
    return result.rows[0] ? this.mapRowToUser(result.rows[0]) : null;
  }

  // 根据密码重置令牌查找用户
  static async findByPasswordResetToken(token: string): Promise<User | null> {
    const result = await query(
      'SELECT * FROM users WHERE password_reset_token = $1',
      [token]
    );
    return result.rows[0] ? this.mapRowToUser(result.rows[0]) : null;
  }

  // 更新用户信息
  static async update(
    id: string,
    updates: Partial<
      UserRegistrationData & {
        emailVerified?: boolean;
        status?: string;
        emailVerificationToken?: string | null;
        emailVerificationExpires?: Date | null;
        passwordResetToken?: string | null;
        passwordResetExpires?: Date | null;
        lastLoginAt?: Date;
        lastLoginIp?: string;
        balance?: number;
        frozenBalance?: number;
        totalEarnings?: number;
        totalSales?: number;
        totalDownloads?: number;
        totalLikes?: number;
        points?: number;
        lastSignInDate?: Date;
        consecutiveSignInDays?: number;
      }
    >
  ): Promise<User | null> {
    const fields: string[] = [];
    const values: any[] = [];
    let paramIndex = 1;

    // 构建动态更新查询
    Object.entries(updates).forEach(([key, value]) => {
      if (value !== undefined) {
        // 将camelCase转换为snake_case
        const dbField = key.replace(/([A-Z])/g, '_$1').toLowerCase();
        fields.push(`${dbField} = $${paramIndex}`);
        values.push(value);
        paramIndex++;
      }
    });

    if (fields.length === 0) {
      return this.findById(id); // 没有更新字段，返回原用户
    }

    // 添加updated_at字段
    fields.push(`updated_at = CURRENT_TIMESTAMP`);
    values.push(id); // 最后一个参数是用户ID

    const query_text = `
      UPDATE users
      SET ${fields.join(', ')}
      WHERE id = $${paramIndex}
      RETURNING *
    `;

    const result = await query(query_text, values);
    return result.rows[0] ? this.mapRowToUser(result.rows[0]) : null;
  }

  // 验证密码
  static async validatePassword(
    user: User,
    password: string
  ): Promise<boolean> {
    return bcrypt.compare(password, user.passwordHash);
  }

  // 更新用户余额
  static async updateBalance(
    userId: string,
    balanceChanges: {
      totalEarnings?: number;
      availableBalance?: number;
      frozenBalance?: number;
      pointsBalance?: number;
    }
  ): Promise<User | null> {
    return transaction(async client => {
      // 获取当前余额
      const currentUser = await this.findById(userId);
      if (!currentUser) {
        throw new Error('User not found');
      }

      const newBalances = {
        totalEarnings:
          (balanceChanges.totalEarnings || 0) + currentUser.totalEarnings,
        availableBalance:
          (balanceChanges.availableBalance || 0) + currentUser.availableBalance,
        frozenBalance:
          (balanceChanges.frozenBalance || 0) + currentUser.frozenBalance,
        pointsBalance:
          (balanceChanges.pointsBalance || 0) + currentUser.pointsBalance,
      };

      // 验证余额不为负数
      if (Object.values(newBalances).some(balance => balance < 0)) {
        throw new Error('Insufficient balance');
      }

      const result = await client.query(
        `
        UPDATE users
        SET total_earnings = $1, available_balance = $2,
            frozen_balance = $3, points_balance = $4,
            updated_at = CURRENT_TIMESTAMP
        WHERE id = $5
        RETURNING *
      `,
        [
          newBalances.totalEarnings,
          newBalances.availableBalance,
          newBalances.frozenBalance,
          newBalances.pointsBalance,
          userId,
        ]
      );

      return result.rows[0] ? this.mapRowToUser(result.rows[0]) : null;
    });
  }

  // 获取用户列表（分页）
  static async findMany(
    options: QueryOptions = {}
  ): Promise<PaginatedResponse<User>> {
    const {
      page = 1,
      limit = 20,
      sortBy = 'created_at',
      sortOrder = 'desc',
      filters = {},
    } = options;

    const offset = (page - 1) * limit;
    const whereClause = [];
    const values = [];
    let paramIndex = 1;

    // 构建过滤条件
    for (const [key, value] of Object.entries(filters)) {
      if (value !== undefined) {
        whereClause.push(`${this.camelToSnake(key)} = $${paramIndex}`);
        values.push(value);
        paramIndex++;
      }
    }

    const whereSQL =
      whereClause.length > 0 ? `WHERE ${whereClause.join(' AND ')}` : '';
    const orderBy = `${this.camelToSnake(sortBy)} ${sortOrder.toUpperCase()}`;

    // 获取总数
    const countResult = await query(
      `
      SELECT COUNT(*) as total FROM users ${whereSQL}
    `,
      values
    );
    const total = parseInt(countResult.rows[0].total);

    // 获取数据
    const dataResult = await query(
      `
      SELECT * FROM users
      ${whereSQL}
      ORDER BY ${orderBy}
      LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
    `,
      [...values, limit, offset]
    );

    const items = dataResult.rows.map((row: any) => this.mapRowToUser(row));
    const totalPages = Math.ceil(total / limit);

    return {
      items,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1,
      },
    };
  }

  // 删除用户
  static async delete(id: string): Promise<boolean> {
    const result = await query('DELETE FROM users WHERE id = $1', [id]);
    return result.rowCount > 0;
  }

  // 数据库行映射到User对象
  private static mapRowToUser(row: any): User {
    return {
      id: row.id,
      email: row.email,
      passwordHash: row.password_hash,
      username: row.username,
      displayName: row.display_name,
      avatar: row.avatar,
      bio: row.bio,
      userType: row.user_type,
      status: row.status,

      // 个人创作者字段
      firstName: row.first_name,
      lastName: row.last_name,
      phoneNumber: row.phone_number,
      country: row.country,

      // 企业创作者字段
      companyName: row.company_name,
      companyRegistrationNumber: row.company_registration_number,
      companyAddress: row.company_address,
      taxId: row.tax_id,
      contactPersonName: row.contact_person_name,
      contactPersonTitle: row.contact_person_title,

      // PayPal相关字段
      paypalEmail: row.paypal_email,
      paypalAccountId: row.paypal_account_id,

      // 财务相关字段
      balance: parseFloat(row.balance || 0),
      frozenBalance: parseFloat(row.frozen_balance || 0),
      totalEarnings: parseFloat(row.total_earnings || 0),
      totalSales: parseInt(row.total_sales || 0),
      totalDownloads: parseInt(row.total_downloads || 0),
      totalLikes: parseInt(row.total_likes || 0),

      // 积分系统
      points: parseInt(row.points || 0),
      lastSignInDate: row.last_sign_in_date,
      consecutiveSignInDays: parseInt(row.consecutive_sign_in_days || 0),

      // 邮箱验证
      emailVerified: row.email_verified || false,
      emailVerificationToken: row.email_verification_token,
      emailVerificationExpires: row.email_verification_expires,

      // 密码重置
      passwordResetToken: row.password_reset_token,
      passwordResetExpires: row.password_reset_expires,

      // 最后登录信息
      lastLoginAt: row.last_login_at,
      lastLoginIp: row.last_login_ip,

      // 账户设置
      emailNotifications: row.email_notifications !== false,
      language: row.language || 'en',
      timezone: row.timezone || 'UTC',

      createdAt: row.created_at,
      updatedAt: row.updated_at,
    };
  }

  // 驼峰命名转下划线命名
  private static camelToSnake(str: string): string {
    return str.replace(/[A-Z]/g, letter => `_${letter.toLowerCase()}`);
  }
}
